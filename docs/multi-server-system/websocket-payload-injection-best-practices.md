# WebSocket Payload注入最佳实践指南

## 📋 概述

本文档详细阐述了在分布式微服务架构中，WebSocket网关自动注入payload的机制、最佳实践和开发规范。通过本指南，开发者可以正确理解和使用payload注入功能，编写高质量、类型安全的微服务代码。

## 🔧 Payload注入机制

### 核心流程

```mermaid
graph LR
    A[客户端消息] --> B[WebSocket网关]
    B --> C[认证守卫]
    C --> D[PayloadEnhancer]
    D --> E[MessageRouter]
    E --> F[目标微服务]
    F --> G[@MessagePattern方法]
```

### 注入字段结构

```typescript
interface EnhancedPayload {
  // === 原始客户端数据 ===
  [key: string]: any;
  
  // === 自动注入字段 ===
  userId: string;                    // 从Token中提取的用户ID
  
  wsContext: {                       // WebSocket上下文
    timestamp: number;               // 消息时间戳
    routingStrategy: string;         // 路由策略
    messageId?: string;              // 消息ID
  };
  
  serverContext?: {                  // 区服上下文（角色Token时注入）
    serverId: string;                // 区服ID
    characterId: string;             // 角色ID
    serverName?: string;             // 区服名称
    serverRegion?: string;           // 区服区域
    characterLevel?: number;         // 角色等级
    characterName?: string;          // 角色名称
  };
  
  crossServerContext?: {             // 跨服上下文（跨服路由时注入）
    sourceServerId: string;
    targetServerId: string;
    crossServerType: string;
  };
  
  globalContext?: {                  // 全服上下文（全服路由时注入）
    globalEventId?: string;
    globalEventType?: string;
  };
}
```

## 🎯 函数编写最佳实践

### ❌ 错误做法

```typescript
// 错误1：直接使用DTO类型，破坏DTO纯净性
@MessagePattern('character.login')
async loginCharacter(@Payload() loginDto: LoginCharacterDto) {
  // loginDto实际包含注入字段，类型不匹配
  const userId = loginDto.userId; // 可能重复或类型错误
}

// 错误2：多个@Payload装饰器
@MessagePattern('character.update')
async updateCharacter(
  @Payload() updateDto: UpdateCharacterDto,
  @Payload('userId') userId: string,        // ❌ 不需要
  @Payload('serverContext') context: any   // ❌ 不需要
) {}
```

### ✅ 推荐做法

#### 方案1：嵌套结构（推荐）

```typescript
@MessagePattern('character.login')
@CachePut({
  key: 'character:info:#{payload.characterId}',
  serverId: '#{payload.serverId}',
  ttl: 3600
})
async loginCharacter(@Payload() payload: { 
  loginDto: LoginCharacterDto;
  // 注入字段自动添加到payload根级别
}) {
  // 访问纯净的DTO
  const loginDto = payload.loginDto;
  
  // 访问注入的字段
  const userId = payload.userId;
  const serverContext = payload.serverContext;
  const timestamp = payload.wsContext.timestamp;
  
  // 业务逻辑
  return await this.characterService.loginCharacter({
    ...loginDto,
    userId,
    serverName: serverContext?.serverName,
    loginTime: new Date(timestamp),
  });
}
```

#### 方案2：明确接口定义（类型安全）

```typescript
// 定义增强Payload接口
interface EnhancedLoginPayload extends LoginCharacterDto {
  // 注入字段
  userId: string;
  wsContext: {
    timestamp: number;
    routingStrategy: string;
    messageId?: string;
  };
  serverContext?: {
    serverId: string;
    characterId: string;
    serverName?: string;
    serverRegion?: string;
    characterLevel?: number;
    characterName?: string;
  };
}

@MessagePattern('character.login')
@CachePut({
  key: 'character:info:#{payload.characterId}:#{payload.userId}',
  serverId: '#{payload.serverContext.serverId}',
  ttl: 3600
})
async loginCharacter(@Payload() payload: EnhancedLoginPayload) {
  // 类型安全，智能提示
  const characterId = payload.characterId;  // 原始字段
  const userId = payload.userId;            // 注入字段
  const serverName = payload.serverContext?.serverName; // 注入的嵌套字段
  
  return await this.characterService.loginCharacter(payload);
}
```

#### 方案3：通用增强Payload基类

```typescript
// 定义通用基类
interface BaseEnhancedPayload {
  userId: string;
  wsContext: {
    timestamp: number;
    routingStrategy: string;
    messageId?: string;
  };
  serverContext?: {
    serverId: string;
    characterId: string;
    serverName?: string;
    serverRegion?: string;
    characterLevel?: number;
    characterName?: string;
  };
}

// 具体业务Payload
interface SearchCharacterPayload extends BaseEnhancedPayload {
  name: string;
  serverId?: string;
}

@MessagePattern('character.searchByName')
@Cacheable({
  key: 'character:search:name:#{payload.name}:#{payload.serverId}:#{payload.userId}',
  serverId: '#{payload.serverContext.serverId}',
  condition: '#{payload.name != null}',
  ttl: 300
})
async searchCharacterByName(@Payload() payload: SearchCharacterPayload) {
  // 完整的类型安全
}
```

## 📨 参数读取规范

### 字段访问优先级

```typescript
async someMethod(@Payload() payload: EnhancedPayload) {
  // 1. 优先使用原始字段
  const serverId = payload.serverId || payload.serverContext?.serverId;
  
  // 2. 利用注入字段增强功能
  const userId = payload.userId;  // 总是可用
  const timestamp = payload.wsContext.timestamp;  // 总是可用
  
  // 3. 条件性字段（根据Token类型和路由策略）
  const characterLevel = payload.serverContext?.characterLevel; // 角色Token时可用
  const crossServerType = payload.crossServerContext?.crossServerType; // 跨服时可用
  
  // 4. 安全的嵌套访问
  const serverName = payload.serverContext?.serverName ?? '未知区服';
}
```

### 类型守卫和验证

```typescript
// 定义类型守卫
function hasServerContext(payload: any): payload is EnhancedPayload & { serverContext: NonNullable<EnhancedPayload['serverContext']> } {
  return payload.serverContext != null;
}

function hasCrossServerContext(payload: any): payload is EnhancedPayload & { crossServerContext: NonNullable<EnhancedPayload['crossServerContext']> } {
  return payload.crossServerContext != null;
}

@MessagePattern('character.crossServerBattle')
async crossServerBattle(@Payload() payload: EnhancedPayload) {
  // 类型安全的条件检查
  if (!hasServerContext(payload)) {
    throw new BadRequestException('需要角色Token');
  }
  
  if (!hasCrossServerContext(payload)) {
    throw new BadRequestException('需要跨服路由');
  }
  
  // 现在可以安全访问
  const sourceServer = payload.crossServerContext.sourceServerId;
  const characterLevel = payload.serverContext.characterLevel;
}
```

## 🗄️ Redis缓存最佳实践

### 缓存键设计原则

```typescript
// ✅ 基础缓存键模式
@Cacheable({
  // 1. 业务标识 + 主键
  key: 'character:info:#{payload.characterId}',
  // 2. 区服隔离
  serverId: '#{payload.serverId}',
  // 3. 合理TTL
  ttl: 3600
})

// ✅ 增强缓存键模式（利用注入字段）
@Cacheable({
  // 1. 包含用户上下文，避免权限问题
  key: 'character:info:#{payload.characterId}:#{payload.userId}',
  // 2. 优先使用注入的区服上下文（更可靠）
  serverId: '#{payload.serverContext.serverId}',
  // 3. 条件缓存，提高效率
  condition: '#{payload.serverContext.characterLevel > 0}',
  ttl: 3600
})

// ✅ 复合缓存键模式
@Cacheable({
  // 1. 多维度缓存键
  key: 'character:search:#{payload.name}:#{payload.serverId}:#{payload.userId}:#{payload.wsContext.routingStrategy}',
  // 2. 智能区服选择
  serverId: '#{payload.serverId}', // ExpressionParser会自动降级到注入字段
  // 3. 复杂条件
  condition: '#{payload.name != null && payload.serverContext.characterLevel > 10}',
  ttl: 300
})
```

### 缓存装饰器适配

#### 现有格式完全兼容

```typescript
// 当前的缓存装饰器无需修改，完全兼容注入后的payload
@MessagePattern('character.searchByName')
@Cacheable({
  key: 'character:search:name:#{payload.name}:#{payload.serverId}',
  dataType: 'server',
  serverId: '#{payload.serverId}',
  ttl: 300
})
async searchCharacterByName(@Payload() payload: { name: string; serverId?: string }) {
  // ExpressionParser会自动从增强后的payload中提取所有字段
}
```

#### 利用注入字段优化

```typescript
@MessagePattern('character.getPersonInfo')
@Cacheable({
  // 使用注入的用户上下文
  key: 'character:person:info:#{payload.characterId}:#{payload.userId}',
  // 使用注入的区服上下文
  serverId: '#{payload.serverContext.serverId}',
  // 使用注入的时间戳进行版本控制
  version: '#{payload.wsContext.timestamp}',
  // 条件缓存 - 只有活跃用户才缓存
  condition: '#{payload.serverContext.characterLevel > 5}',
  ttl: 300
})
async getPersonInfo(@Payload() payload: { characterId: string; serverId?: string }) {
  // 智能缓存策略
}
```

### 缓存更新策略

```typescript
@MessagePattern('character.update')
@CachePut({
  // 更新主缓存
  key: 'character:info:#{payload.characterId}:#{payload.userId}',
  serverId: '#{payload.serverContext.serverId}',
  ttl: 3600
})
@CacheEvict({
  // 清除相关缓存
  key: [
    'character:search:*:#{payload.serverContext.serverId}:#{payload.userId}',
    'user:characters:#{payload.userId}'
  ],
  dataType: 'server',
  serverId: '#{payload.serverContext.serverId}'
})
async updateCharacter(@Payload() payload: {
  characterId: string;
  updateDto: UpdateCharacterDto;
  serverId?: string;
}) {
  // 智能缓存更新
}
```

## 🔍 表达式解析规则

### 支持的表达式格式

```typescript
// 1. 简单字段访问
'#{payload.characterId}'           // 访问payload.characterId
'#{payload.userId}'                // 访问注入的userId

// 2. 嵌套字段访问
'#{payload.serverContext.serverId}'      // 访问注入的嵌套字段
'#{payload.wsContext.timestamp}'          // 访问WebSocket上下文

// 3. 条件表达式
'#{payload.characterId != null}'          // 非空检查
'#{payload.serverContext.characterLevel > 10}'  // 数值比较
'#{payload.name == "admin"}'              // 字符串比较

// 4. 复合表达式
'character:#{payload.characterId}:#{payload.userId}'  // 多字段组合
```

### 字段解析优先级

```typescript
// ExpressionParser的解析优先级：
// 1. 直接参数名匹配
// 2. 嵌套属性访问（payload.xxx）
// 3. 特殊关键字（result、target、methodName）
// 4. 索引访问（数字）

// 示例：#{payload.serverId}
// 1. 查找参数名为'payload'的参数
// 2. 在payload对象中查找'serverId'属性
// 3. 如果原始payload.serverId存在，使用原始值
// 4. 如果不存在，ExpressionParser会在增强后的payload中查找
// 5. 返回找到的值或抛出错误
```

## 🚨 常见问题和解决方案

### 问题1：DTO类型不匹配

```typescript
// ❌ 问题代码
async loginCharacter(@Payload() loginDto: LoginCharacterDto) {
  // TypeScript报错：Property 'userId' does not exist on type 'LoginCharacterDto'
  const userId = loginDto.userId;
}

// ✅ 解决方案
async loginCharacter(@Payload() payload: { loginDto: LoginCharacterDto }) {
  const loginDto = payload.loginDto;  // 纯净的DTO
  const userId = payload.userId;      // 注入的字段
}
```

### 问题2：缓存键解析失败

```typescript
// ❌ 问题代码
@Cacheable({
  key: 'character:info:#{characterId}',  // 缺少payload前缀
  serverId: '#{serverId}'                // 缺少payload前缀
})

// ✅ 解决方案
@Cacheable({
  key: 'character:info:#{payload.characterId}',
  serverId: '#{payload.serverId}'
})
```

### 问题3：条件性字段访问错误

```typescript
// ❌ 问题代码
async someMethod(@Payload() payload: any) {
  const characterLevel = payload.serverContext.characterLevel; // 可能为null
}

// ✅ 解决方案
async someMethod(@Payload() payload: EnhancedPayload) {
  const characterLevel = payload.serverContext?.characterLevel ?? 0;
  
  // 或使用类型守卫
  if (hasServerContext(payload)) {
    const characterLevel = payload.serverContext.characterLevel; // 类型安全
  }
}
```

## 📚 完整示例

### 角色管理Controller

```typescript
// 定义增强Payload接口
interface EnhancedCharacterPayload {
  characterId: string;
  serverId?: string;
  userId: string;
  wsContext: {
    timestamp: number;
    routingStrategy: string;
    messageId?: string;
  };
  serverContext?: {
    serverId: string;
    characterId: string;
    serverName?: string;
    serverRegion?: string;
    characterLevel?: number;
    characterName?: string;
  };
}

@Controller()
export class CharacterController {
  private readonly logger = new Logger(CharacterController.name);

  constructor(private readonly characterService: CharacterService) {}

  /**
   * 角色登录 - 展示CachePut用法
   */
  @MessagePattern('character.login')
  @CachePut({
    key: 'character:info:#{payload.characterId}:#{payload.userId}',
    serverId: '#{payload.serverContext.serverId}',
    ttl: 3600
  })
  async loginCharacter(@Payload() payload: { loginDto: LoginCharacterDto }) {
    this.logger.log(`角色登录: ${payload.loginDto.characterId}, 用户: ${payload.userId}`);
    
    const result = await this.characterService.loginCharacter({
      ...payload.loginDto,
      userId: payload.userId,
      serverName: payload.serverContext?.serverName,
      loginTime: new Date(payload.wsContext.timestamp),
    });
    
    return { code: 0, message: '登录成功', data: result };
  }

  /**
   * 获取角色信息 - 展示Cacheable用法
   */
  @MessagePattern('character.getInfo')
  @Cacheable({
    key: 'character:info:#{payload.characterId}:#{payload.userId}',
    serverId: '#{payload.serverContext.serverId}',
    condition: '#{payload.characterId != null}',
    ttl: 3600
  })
  async getCharacterInfo(@Payload() payload: EnhancedCharacterPayload) {
    this.logger.log(`获取角色信息: ${payload.characterId}`);
    
    const character = await this.characterService.getCharacterInfo(
      payload.characterId,
      payload.userId
    );
    
    return { code: 0, message: '获取成功', data: character };
  }

  /**
   * 搜索角色 - 展示复杂缓存键用法
   */
  @MessagePattern('character.searchByName')
  @Cacheable({
    key: 'character:search:name:#{payload.name}:#{payload.serverId}:#{payload.userId}',
    serverId: '#{payload.serverId}',
    condition: '#{payload.name != null && payload.serverContext.characterLevel > 0}',
    ttl: 300
  })
  async searchCharacterByName(@Payload() payload: {
    name: string;
    serverId?: string;
  }) {
    this.logger.log(`搜索角色: ${payload.name}, 搜索者: ${payload.userId}`);
    
    const character = await this.characterService.searchByName(
      payload.name,
      payload.serverId || payload.serverContext?.serverId,
      payload.userId // 用于权限检查
    );
    
    return { code: 0, message: '搜索成功', data: character };
  }

  /**
   * 更新角色 - 展示CachePut + CacheEvict组合用法
   */
  @MessagePattern('character.update')
  @CachePut({
    key: 'character:info:#{payload.characterId}:#{payload.userId}',
    serverId: '#{payload.serverContext.serverId}',
    ttl: 3600
  })
  @CacheEvict({
    key: [
      'character:search:*:#{payload.serverContext.serverId}:#{payload.userId}',
      'user:characters:#{payload.userId}'
    ],
    dataType: 'server',
    serverId: '#{payload.serverContext.serverId}'
  })
  async updateCharacter(@Payload() payload: {
    characterId: string;
    updateDto: UpdateCharacterDto;
    serverId?: string;
  }) {
    this.logger.log(`更新角色: ${payload.characterId}, 操作者: ${payload.userId}`);
    
    const character = await this.characterService.updateCharacter(
      payload.characterId,
      payload.updateDto,
      payload.userId // 用于权限检查
    );
    
    return { code: 0, message: '更新成功', data: character };
  }
}
```

## 🎯 开发检查清单

### 函数编写检查

- [ ] 使用正确的Payload类型定义
- [ ] 避免直接使用DTO类型作为@Payload参数
- [ ] 正确访问注入字段和原始字段
- [ ] 实现适当的类型守卫和验证
- [ ] 添加详细的中文注释

### 缓存设计检查

- [ ] 缓存键包含必要的业务标识
- [ ] 正确使用#{payload.xxx}表达式
- [ ] 实现适当的区服隔离
- [ ] 设置合理的TTL值
- [ ] 添加必要的条件缓存

### 性能优化检查

- [ ] 利用注入字段优化缓存策略
- [ ] 避免不必要的数据库查询
- [ ] 实现智能的缓存更新策略
- [ ] 考虑缓存预热和降级方案

### 安全性检查

- [ ] 验证用户权限（使用注入的userId）
- [ ] 检查区服权限（使用serverContext）
- [ ] 实现适当的输入验证
- [ ] 避免缓存敏感信息

## 🔧 高级用法和扩展

### 自定义Payload增强器

```typescript
// 扩展PayloadEnhancerService
@Injectable()
export class CustomPayloadEnhancer extends PayloadEnhancerService {

  /**
   * 为特定业务场景注入自定义上下文
   */
  async enhanceForBusiness(
    originalPayload: any,
    businessType: string,
    clientContext?: any
  ): Promise<EnhancedPayload> {
    const basePayload = await this.enhancePayload(
      originalPayload,
      clientContext.userId,
      clientContext
    );

    // 根据业务类型注入特定上下文
    switch (businessType) {
      case 'match':
        return this.injectMatchContext(basePayload, clientContext);
      case 'trade':
        return this.injectTradeContext(basePayload, clientContext);
      case 'guild':
        return this.injectGuildContext(basePayload, clientContext);
      default:
        return basePayload;
    }
  }

  private async injectMatchContext(payload: any, context: any) {
    payload.matchContext = {
      matchId: context.currentMatchId,
      matchType: context.matchType,
      teamId: context.teamId,
    };
    return payload;
  }
}
```

### 条件性Payload处理

```typescript
// 根据路由策略进行不同的处理
@MessagePattern('character.crossServerAction')
async crossServerAction(@Payload() payload: EnhancedPayload) {
  // 根据路由策略执行不同逻辑
  switch (payload.wsContext.routingStrategy) {
    case 'cross_server':
      return this.handleCrossServerLogic(payload);
    case 'global':
      return this.handleGlobalLogic(payload);
    case 'normal':
    default:
      return this.handleNormalLogic(payload);
  }
}

private async handleCrossServerLogic(payload: EnhancedPayload) {
  if (!payload.crossServerContext) {
    throw new BadRequestException('跨服上下文缺失');
  }

  // 跨服业务逻辑
  const sourceServer = payload.crossServerContext.sourceServerId;
  const targetServer = payload.crossServerContext.targetServerId;

  return await this.crossServerService.executeAction(
    payload,
    sourceServer,
    targetServer
  );
}
```

### 性能监控和调试

```typescript
// 添加性能监控装饰器
@Injectable()
export class PayloadPerformanceInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const start = Date.now();
    const payload = context.switchToRpc().getData();

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - start;
        this.logger.log(`Payload处理耗时: ${duration}ms, 用户: ${payload.userId}`);

        // 性能告警
        if (duration > 1000) {
          this.logger.warn(`Payload处理超时: ${duration}ms, 消息ID: ${payload.wsContext?.messageId}`);
        }
      })
    );
  }
}

// 使用监控装饰器
@Controller()
@UseInterceptors(PayloadPerformanceInterceptor)
export class CharacterController {
  // ... 控制器方法
}
```

## 🧪 测试最佳实践

### 单元测试模拟

```typescript
describe('CharacterController', () => {
  let controller: CharacterController;
  let service: CharacterService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CharacterController],
      providers: [
        {
          provide: CharacterService,
          useValue: {
            getCharacterInfo: jest.fn(),
            updateCharacter: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<CharacterController>(CharacterController);
    service = module.get<CharacterService>(CharacterService);
  });

  describe('getCharacterInfo', () => {
    it('应该正确处理增强的payload', async () => {
      // 模拟增强后的payload
      const mockPayload = {
        characterId: 'char_123',
        serverId: 'server_001',
        // 注入字段
        userId: 'user_456',
        wsContext: {
          timestamp: Date.now(),
          routingStrategy: 'normal',
          messageId: 'msg_789',
        },
        serverContext: {
          serverId: 'server_001',
          characterId: 'char_123',
          serverName: '华东一区',
          characterLevel: 25,
        },
      };

      const mockCharacter = { id: 'char_123', name: '测试角色' };
      jest.spyOn(service, 'getCharacterInfo').mockResolvedValue(mockCharacter);

      const result = await controller.getCharacterInfo(mockPayload);

      expect(service.getCharacterInfo).toHaveBeenCalledWith('char_123', 'user_456');
      expect(result.code).toBe(0);
      expect(result.data).toBe(mockCharacter);
    });

    it('应该处理缺少serverContext的情况', async () => {
      const mockPayload = {
        characterId: 'char_123',
        serverId: 'server_001',
        userId: 'user_456',
        wsContext: {
          timestamp: Date.now(),
          routingStrategy: 'normal',
        },
        // 没有serverContext
      };

      const mockCharacter = { id: 'char_123', name: '测试角色' };
      jest.spyOn(service, 'getCharacterInfo').mockResolvedValue(mockCharacter);

      const result = await controller.getCharacterInfo(mockPayload);

      expect(result.code).toBe(0);
    });
  });
});
```

### 集成测试

```typescript
// 测试WebSocket网关的payload注入
describe('WebSocket Payload Injection (E2E)', () => {
  let app: INestApplication;
  let gateway: WebSocketGateway;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    gateway = app.get(WebSocketGateway);
    await app.init();
  });

  it('应该正确注入payload字段', async () => {
    // 模拟客户端消息
    const clientMessage = {
      id: 'test_001',
      command: 'character.getInfo',
      payload: {
        characterId: 'char_123',
        serverId: 'server_001',
      },
    };

    // 模拟认证上下文
    const mockClient = {
      data: {
        user: {
          id: 'user_456',
          username: 'testuser',
        },
        character: {
          characterId: 'char_123',
          serverId: 'server_001',
        },
      },
    };

    // 测试消息处理
    const result = await gateway.handleMessage(mockClient, clientMessage);

    // 验证payload注入
    expect(result.payload).toMatchObject({
      characterId: 'char_123',
      serverId: 'server_001',
      userId: 'user_456',
      wsContext: expect.objectContaining({
        timestamp: expect.any(Number),
        routingStrategy: 'normal',
      }),
      serverContext: expect.objectContaining({
        serverId: 'server_001',
        characterId: 'char_123',
      }),
    });
  });
});
```

## 📊 性能优化建议

### 缓存策略优化

```typescript
// 多级缓存策略
@MessagePattern('character.getInfo')
@Cacheable({
  // L1缓存：内存缓存，极快访问
  key: 'character:info:#{payload.characterId}',
  repository: 'memory',
  ttl: 300,
})
@Cacheable({
  // L2缓存：Redis缓存，持久化
  key: 'character:info:#{payload.characterId}:#{payload.userId}',
  repository: 'redis',
  serverId: '#{payload.serverContext.serverId}',
  ttl: 3600,
})
async getCharacterInfo(@Payload() payload: EnhancedCharacterPayload) {
  // 多级缓存自动处理
}

// 预热缓存策略
@MessagePattern('character.login')
async loginCharacter(@Payload() payload: { loginDto: LoginCharacterDto }) {
  const result = await this.characterService.loginCharacter(payload.loginDto);

  // 登录后预热相关缓存
  await this.preWarmCache(payload.userId, payload.serverContext?.serverId);

  return result;
}

private async preWarmCache(userId: string, serverId: string) {
  // 预热用户常用数据
  const promises = [
    this.characterService.getCharacterList(userId),
    this.heroService.getHeroList(userId),
    this.formationService.getFormations(userId),
  ];

  await Promise.allSettled(promises);
}
```

### 批量操作优化

```typescript
// 批量处理payload
@MessagePattern('character.getBatch')
@Cacheable({
  key: 'character:batch:#{payload.characterIds}:#{payload.userId}',
  serverId: '#{payload.serverContext.serverId}',
  ttl: 1800,
})
async getBatchCharacters(@Payload() payload: {
  characterIds: string[];
  serverId?: string;
}) {
  // 利用注入的用户上下文进行权限检查
  const allowedCharacters = await this.characterService.filterByPermission(
    payload.characterIds,
    payload.userId
  );

  return await this.characterService.getBatchCharacters(allowedCharacters);
}
```

## 🔒 安全最佳实践

### 权限验证

```typescript
// 基于注入字段的权限验证
@Injectable()
export class PayloadPermissionGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const payload = context.switchToRpc().getData();

    // 验证用户是否有权限访问指定角色
    if (payload.characterId && payload.userId) {
      return this.validateCharacterOwnership(payload.characterId, payload.userId);
    }

    // 验证区服权限
    if (payload.serverContext) {
      return this.validateServerAccess(payload.userId, payload.serverContext.serverId);
    }

    return true;
  }

  private async validateCharacterOwnership(characterId: string, userId: string): Promise<boolean> {
    // 实现角色所有权验证逻辑
    return true;
  }

  private async validateServerAccess(userId: string, serverId: string): Promise<boolean> {
    // 实现区服访问权限验证逻辑
    return true;
  }
}

// 使用权限守卫
@Controller()
@UseGuards(PayloadPermissionGuard)
export class CharacterController {
  // 所有方法都会进行权限验证
}
```

### 数据脱敏

```typescript
// 敏感数据脱敏
@MessagePattern('character.getInfo')
async getCharacterInfo(@Payload() payload: EnhancedCharacterPayload) {
  const character = await this.characterService.getCharacterInfo(payload.characterId);

  // 根据用户权限脱敏数据
  return this.sanitizeCharacterData(character, payload.userId, payload.serverContext);
}

private sanitizeCharacterData(character: any, userId: string, serverContext?: any) {
  // 如果不是角色所有者，隐藏敏感信息
  if (character.userId !== userId) {
    delete character.email;
    delete character.phone;
    delete character.realName;
  }

  // 根据区服权限脱敏
  if (serverContext?.characterLevel < 10) {
    delete character.advancedStats;
  }

  return character;
}
```

## 🚀 部署和运维

### 监控指标

```typescript
// 添加业务监控指标
@Injectable()
export class PayloadMetricsService {
  private readonly metricsRegistry = new Map<string, number>();

  recordPayloadProcessing(payload: EnhancedPayload, duration: number) {
    const key = `${payload.wsContext.routingStrategy}_${payload.userId}`;
    this.metricsRegistry.set(key, duration);

    // 发送到监控系统
    this.sendToMonitoring({
      metric: 'payload_processing_duration',
      value: duration,
      tags: {
        routingStrategy: payload.wsContext.routingStrategy,
        serverId: payload.serverContext?.serverId,
        userId: payload.userId,
      },
    });
  }

  getMetrics() {
    return Array.from(this.metricsRegistry.entries()).map(([key, value]) => ({
      key,
      value,
      timestamp: Date.now(),
    }));
  }
}
```

### 日志规范

```typescript
// 结构化日志记录
@Injectable()
export class PayloadLogger {
  private readonly logger = new Logger(PayloadLogger.name);

  logPayloadProcessing(payload: EnhancedPayload, action: string, result?: any) {
    const logData = {
      action,
      userId: payload.userId,
      characterId: payload.serverContext?.characterId,
      serverId: payload.serverContext?.serverId,
      routingStrategy: payload.wsContext.routingStrategy,
      messageId: payload.wsContext.messageId,
      timestamp: payload.wsContext.timestamp,
      processingTime: Date.now() - payload.wsContext.timestamp,
      success: result ? true : false,
    };

    this.logger.log(`Payload处理: ${JSON.stringify(logData)}`);
  }

  logPayloadError(payload: EnhancedPayload, error: Error) {
    const errorData = {
      error: error.message,
      stack: error.stack,
      userId: payload.userId,
      messageId: payload.wsContext?.messageId,
      timestamp: Date.now(),
    };

    this.logger.error(`Payload处理错误: ${JSON.stringify(errorData)}`);
  }
}
```

---

**文档版本**: v1.0
**最后更新**: 2025-01-02
**维护者**: 开发团队
**适用范围**: 所有微服务模块

## 📚 相关文档

- [微服务开发标准](./microservice-development-standards.md)
- [Redis缓存最佳实践](../redis/redis-cache-best-practices.md)
- [WebSocket网关架构指南](./websocket-gateway-architecture.md)
- [分区分服架构设计](./multi-server-architecture.md)
