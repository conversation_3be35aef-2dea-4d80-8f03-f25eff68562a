/**
 * Formation Schema - 严格基于old项目teamFormations.js重新设计
 * 确保与old项目的数据结构100%一致
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import {SwapHerosDto} from "@character/common/dto/formation.dto";

// 阵容类型枚举 - 基于old项目commonEnum.FORMATION_TYPE
export enum FormationType {
  COMMON = 1,      // 普通阵容
  LEAGUE = 2,      // 联赛阵容
  WAR_OF_FAITH = 3, // 信仰之战阵容
  TOURNAMENT = 4,   // 锦标赛阵容
  FRIENDLY = 5,     // 友谊赛阵容
}

// 小队类型枚举 - 基于old项目TeamType字段
export enum TeamType {
  MAIN = 1,        // 主力队
  SUBSTITUTE = 2,  // 替补队
  RESERVE = 3,     // 预备队
}

// 教练子文档 - 基于old项目Trainers字段
@Schema({ _id: false })
export class FormationTrainer {
  @Prop({ default: '' })
  uid: string;         // 教练UID

  @Prop({ default: 0 })
  resId: number;       // 教练配置ID

  @Prop({ default: 1 })
  type: number;        // 教练类型

  @Prop({ default: 1 })
  level: number;       // 教练等级

  @Prop({ type: [Object], default: [] })
  tactics: any[];      // 教练战术
}

// 位置球员映射 - 严格基于old项目PositionToHerosObject结构
@Schema({ _id: false })
export class PositionToHerosObject {
  @Prop({ type: [String], default: [] })
  GK: string[];        // 门将位置的球员UID列表 (严格对应old项目GK)

  @Prop({ type: [String], default: [] })
  DL: string[];        // 左后卫位置的球员UID列表 (严格对应old项目DL)

  @Prop({ type: [String], default: [] })
  DC: string[];        // 中后卫位置的球员UID列表 (严格对应old项目DC)

  @Prop({ type: [String], default: [] })
  DR: string[];        // 右后卫位置的球员UID列表 (严格对应old项目DR)

  @Prop({ type: [String], default: [] })
  ML: string[];        // 左中场位置的球员UID列表 (严格对应old项目ML)

  @Prop({ type: [String], default: [] })
  MC: string[];        // 中中场位置的球员UID列表 (严格对应old项目MC)

  @Prop({ type: [String], default: [] })
  MR: string[];        // 右中场位置的球员UID列表 (严格对应old项目MR)

  @Prop({ type: [String], default: [] })
  WL: string[];        // 左边锋位置的球员UID列表 (严格对应old项目WL)

  @Prop({ type: [String], default: [] })
  ST: string[];        // 前锋位置的球员UID列表 (严格对应old项目ST)

  @Prop({ type: [String], default: [] })
  WR: string[];        // 右边锋位置的球员UID列表 (严格对应old项目WR)

  @Prop({ type: [String], default: [] })
  AM: string[];        // 前腰位置的球员UID列表 (严格对应old项目AM)

  @Prop({ type: [String], default: [] })
  DM: string[];        // 后腰位置的球员UID列表 (严格对应old项目DM)
}

// 单个阵容 - 严格基于old项目newTeamFormation方法
@Schema({ _id: false })
export class TeamFormation {
  // TODO 待明确与teamId的区别
  @Prop({ required: true })
  uid: string;                    // 阵容UID - 对应old项目的Uid

  @Prop({ required: true })
  resId: number;                  // 配置阵型Id - 对应old项目的ResId

  @Prop({ default: '阵容一' })
  name: string;                   // 阵容名称 - 对应old项目的Name

  // 团队基础属性 - 严格对应old项目
  @Prop({ default: 0 })
  attack: number;                 // 球队进攻值

  @Prop({ default: 0 })
  defend: number;                 // 球队防守值

  @Prop({ default: 0 })
  actualStrength: number;         // 球队的实力

  @Prop({ default: 0 })
  isInitName: number;             // 是否是初始名字 0是 1不是

  @Prop({ default: 0 })
  isInitFormation: number;        // 是否初始阵容一 0不是 1是

  @Prop({ default: 0 })
  isLeague: number;               // 是否为联赛专用阵容 0为不是 1是为

  @Prop({ default: 1 })
  teamId: number;                 // 阵容Id 1,2,3,4 阵容1,2,3,4

  @Prop({ default: 1 })
  teamType: number;               // 小队类型 1主力队 2替补队 3预备队

  // 球员位置映射 - 严格对应old项目PositionToHerosObject
  @Prop({ type: PositionToHerosObject, default: () => ({
    GK: [], DL: [], DC: [], DR: [], ML: [], MC: [],
    MR: [], WL: [], ST: [], WR: [], AM: [], DM: []
  }) })
  positionToHerosObject: PositionToHerosObject;

  @Prop({ type: [String], default: [] })
  scenceUse: string[];            // 阵型使用场景标记

  @Prop({ default: 0 })
  inspireRate: number;            // 鼓舞加成比例,暂定为0

  @Prop({ default: 101 })
  useTactics: number;             // 当前阵容使用的战术

  @Prop({ default: 1101 })
  useDefTactics: number;          // 当前阵容使用防守的战术

  @Prop({ default: '' })
  freeKickHero: string;         // 指定的任意球球员

  @Prop({ default: '' })
  penaltiesHero: string;        // 指定的点球球员

  @Prop({ default: '' })
  cornerKickHero: string;       // 指定的角球球员

  @Prop({ type: [FormationTrainer], default: [] })
  trainers: FormationTrainer[];   // 教练

  @Prop({ default: FormationType.COMMON })
  type: number;                   // 阵容类型(用途)
}

// 主TeamFormations Schema - 严格基于old项目TeamFormations实体
@Schema({ 
  collection: 'teamFormations', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class TeamFormations {
  @Prop({ required: true, index: true })
  uid: string;                    // 玩家UID - 对应old项目的uid

  @Prop({ required: true, index: true })
  characterId: string;            // 角色ID (新增字段，用于微服务架构)

  @Prop({ required: true, index: true })
  serverId: string;               // 服务器ID (新增字段，用于微服务架构)

  // 所有阵容 - 对应old项目的allTeamFormations (Map转为Array)
  @Prop({ type: [TeamFormation], default: [] })
  teamFormations: TeamFormation[];

  @Prop({ default: '' })
  currTeamFormationId: string;    // 当前使用阵容 - 对应old项目

  @Prop({ default: '' })
  leagueTeamFormationId: string;  // 联赛阵容Id - 对应old项目

  @Prop({ default: '' })
  warOfFaithTeamFormationId: string; // 信仰之战阵容id - 对应old项目

  // 所有进攻战术 - 对应old项目的allTactics (Map转为Object)
  @Prop({ type: Object, default: {} })
  allTactics: Record<string, any>;

  // 所有防守战术 - 对应old项目的allDefTactics
  @Prop({ type: Object, default: {} })
  allDefTactics: Record<string, any>;

  // 所有阵型 - 对应old项目的allFormations
  @Prop({ type: [Object], default: [] })
  allFormations: any[];

  @Prop({ default: 0 })
  fixId: number;                  // 对应old项目的fixId
}

export const TeamFormationsSchema = SchemaFactory.createForClass(TeamFormations);

// 定义方法接口 - 基于FormationService的真实业务逻辑
export interface TeamFormationsMethods {
  // 阵容管理方法 - 基于FormationService
  getFormation(formationId: string): TeamFormation | null;
  addFormation(formation: TeamFormation): void;
  removeFormation(formationId: string): boolean;
  updateFormation(formationId: string, updates: Partial<TeamFormation>): boolean;

  // 球员位置管理 - 基于FormationService的位置管理逻辑
  addHeroToPosition(formationId: string, position: string, heroId: string, index?: number): boolean;
  removeHeroFromPosition(formationId: string, position: string, heroId: string): boolean;
  moveHeroPosition(formationId: string, fromPosition: string, toPosition: string, heroId: string): boolean;
  swapHeroPositions(formationId: string, heroId1: string, heroId2: string): boolean;

  // 阵容验证 - 基于FormationService的验证逻辑
  validateFormation(formationId: string): { isValid: boolean; errors: string[] };
  checkHeroExists(formationId: string, heroId: string): boolean;
  getHeroPosition(formationId: string, heroId: string): string | null;

  // 阵容统计 - 基于FormationService的统计需求
  getFormationHeroCount(formationId: string): number;
  getPositionHeroCount(formationId: string, position: string): number;
  getAllHeroIds(formationId: string): string[];

  // 阵容类型管理 - 基于FormationService的类型逻辑
  getFormationsByType(type: FormationType): TeamFormation[];
  setFormationType(formationId: string, type: FormationType): boolean;

  // 当前阵容管理 - 基于FormationService的激活逻辑
  setCurrentFormation(formationId: string): boolean;
  getCurrentFormation(): TeamFormation | null;
  isCurrentFormation(formationId: string): boolean;

  // 数据转换 - 基于FormationService的客户端数据需求
  makeClientFormationList(): any[];
  getFormationSummary(formationId: string): any;
}

// 定义Document类型
export type TeamFormationsDocument = TeamFormations & Document & TeamFormationsMethods;

// 创建索引
TeamFormationsSchema.index({ uid: 1 }, { unique: true });
TeamFormationsSchema.index({ characterId: 1 });
TeamFormationsSchema.index({ serverId: 1 });
TeamFormationsSchema.index({ currTeamFormationId: 1 });

// ==================== 实例方法实现 ====================

/**
 * 获取指定阵容
 * 基于FormationService: 阵容查询逻辑
 */
TeamFormationsSchema.methods.getFormation = function(formationId: string): TeamFormation | null {
  return this.teamFormations.find((formation: TeamFormation) => formation.uid === formationId) || null;
};

/**
 * 添加阵容
 * 基于FormationService: createFormation方法逻辑
 */
TeamFormationsSchema.methods.addFormation = function(formation: TeamFormation): void {
  this.teamFormations.push(formation);

  // 如果是第一个阵容，设为当前阵容
  if (this.teamFormations.length === 1) {
    this.currTeamFormationId = formation.uid;
  }
};

/**
 * 移除阵容
 * 基于FormationService: 阵容删除逻辑
 */
TeamFormationsSchema.methods.removeFormation = function(formationId: string): boolean {
  const index = this.teamFormations.findIndex((formation: TeamFormation) => formation.uid === formationId);
  if (index === -1) {
    return false;
  }

  // 如果删除的是当前阵容，需要重新设置当前阵容
  if (this.currTeamFormationId === formationId) {
    this.currTeamFormationId = this.teamFormations.length > 1 ?
      this.teamFormations[index === 0 ? 1 : 0].uid : '';
  }

  this.teamFormations.splice(index, 1);
  return true;
};

/**
 * 更新阵容
 * 基于FormationService: 阵容更新逻辑
 */
TeamFormationsSchema.methods.updateFormation = function(formationId: string, updates: Partial<TeamFormation>): boolean {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return false;
  }

  Object.assign(formation, updates);
  return true;
};

/**
 * 添加球员到位置
 * 基于FormationService: addHeroToPosition方法
 */
TeamFormationsSchema.methods.addHeroToPosition = function(formationId: string, position: string, heroId: string, index?: number): boolean {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return false;
  }

  // 检查球员是否已在阵容中（基于FormationService的checkHeroExists逻辑）
  if (this.checkHeroExists(formationId, heroId)) {
    return false;
  }

  // 检查位置是否有效
  const positionKey = position as keyof typeof formation.positionToHerosObject;
  if (formation.positionToHerosObject[positionKey] === undefined) {
    return false;
  }

  // 添加球员到指定位置
  if (index !== undefined && index >= 0) {
    formation.positionToHerosObject[positionKey].splice(index, 0, heroId);
  } else {
    formation.positionToHerosObject[positionKey].push(heroId);
  }

  return true;
};

/**
 * 从位置移除球员
 * 基于FormationService: removeHeroFromPosition方法
 */
TeamFormationsSchema.methods.removeHeroFromPosition = function(formationId: string, position: string, heroId: string): boolean {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return false;
  }

  const positionKey = position as keyof typeof formation.positionToHerosObject;
  if (formation.positionToHerosObject[positionKey] === undefined) {
    return false;
  }

  const heroList = formation.positionToHerosObject[positionKey];
  const index = heroList.indexOf(heroId);
  if (index === -1) {
    return false;
  }

  heroList.splice(index, 1);
  return true;
};

/**
 * 移动球员位置
 * 基于FormationService: 球员位置调整逻辑
 */
TeamFormationsSchema.methods.moveHeroPosition = function(formationId: string, fromPosition: string, toPosition: string, heroId: string): boolean {
  // 先从原位置移除
  if (!this.removeHeroFromPosition(formationId, fromPosition, heroId)) {
    return false;
  }

  // 再添加到新位置
  if (!this.addHeroToPosition(formationId, toPosition, heroId)) {
    // 如果添加失败，恢复到原位置
    this.addHeroToPosition(formationId, fromPosition, heroId);
    return false;
  }

  return true;
};

/**
 * 交换球员位置
 * 基于FormationService: 球员位置交换逻辑
 */
TeamFormationsSchema.methods.swapHeroPositions = function(formationId: string, heroId1: string, heroId2: string): boolean {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return false;
  }

  // 找到两个球员的位置
  const position1 = this.getHeroPosition(formationId, heroId1);
  const position2 = this.getHeroPosition(formationId, heroId2);

  if (!position1 || !position2) {
    return false;
  }

  // 执行位置交换
  this.removeHeroFromPosition(formationId, position1, heroId1);
  this.removeHeroFromPosition(formationId, position2, heroId2);
  this.addHeroToPosition(formationId, position1, heroId2);
  this.addHeroToPosition(formationId, position2, heroId1);

  return true;
};

/**
 * 验证阵容
 * 基于FormationService: 阵容验证逻辑
 */
TeamFormationsSchema.methods.validateFormation = function(formationId: string): { isValid: boolean; errors: string[] } {
  const formation = this.getFormation(formationId);
  const errors: string[] = [];

  if (!formation) {
    return { isValid: false, errors: ['阵容不存在'] };
  }

  // 检查门将数量（必须有且只能有1个）
  const gkCount = formation.positionToHerosObject.GK.length;
  if (gkCount === 0) {
    errors.push('缺少门将');
  } else if (gkCount > 1) {
    errors.push('门将数量过多');
  }

  // 检查场上球员总数（应该是11人）
  const totalPlayers = this.getFormationHeroCount(formationId);
  if (totalPlayers < 11) {
    errors.push(`场上球员不足，当前${totalPlayers}人，需要11人`);
  } else if (totalPlayers > 11) {
    errors.push(`场上球员过多，当前${totalPlayers}人，最多11人`);
  }

  // 检查是否有重复球员
  const allHeroIds = this.getAllHeroIds(formationId);
  const uniqueHeroIds = [...new Set(allHeroIds)];
  if (allHeroIds.length !== uniqueHeroIds.length) {
    errors.push('存在重复的球员');
  }

  return { isValid: errors.length === 0, errors };
};

/**
 * 检查球员是否存在于阵容中
 * 基于FormationService: checkHeroExists方法
 */
TeamFormationsSchema.methods.checkHeroExists = function(formationId: string, heroId: string): boolean {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return false;
  }

  // 遍历所有位置查找球员
  for (const position in formation.positionToHerosObject) {
    const heroList = formation.positionToHerosObject[position as keyof typeof formation.positionToHerosObject];
    if (heroList.includes(heroId)) {
      return true;
    }
  }

  return false;
};

/**
 * 获取球员在阵容中的位置
 * 基于FormationService: 位置查询逻辑
 */
TeamFormationsSchema.methods.getHeroPosition = function(formationId: string, heroId: string): string | null {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return null;
  }

  // 遍历所有位置查找球员
  for (const position in formation.positionToHerosObject) {
    const heroList = formation.positionToHerosObject[position as keyof typeof formation.positionToHerosObject];
    if (heroList.includes(heroId)) {
      return position;
    }
  }

  return null;
};

/**
 * 获取阵容球员总数
 * 基于FormationService: 统计逻辑
 */
TeamFormationsSchema.methods.getFormationHeroCount = function(formationId: string): number {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return 0;
  }

  let count = 0;
  for (const position in formation.positionToHerosObject) {
    const heroList = formation.positionToHerosObject[position as keyof typeof formation.positionToHerosObject];
    count += heroList.length;
  }

  return count;
};

/**
 * 获取指定位置的球员数量
 * 基于FormationService: 位置统计逻辑
 */
TeamFormationsSchema.methods.getPositionHeroCount = function(formationId: string, position: string): number {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return 0;
  }

  const positionKey = position as keyof typeof formation.positionToHerosObject;
  return formation.positionToHerosObject[positionKey]?.length || 0;
};

/**
 * 获取阵容中所有球员ID
 * 基于FormationService: 球员列表获取逻辑
 */
TeamFormationsSchema.methods.getAllHeroIds = function(formationId: string): string[] {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return [];
  }

  const allHeroIds: string[] = [];
  for (const position in formation.positionToHerosObject) {
    const heroList = formation.positionToHerosObject[position as keyof typeof formation.positionToHerosObject];
    allHeroIds.push(...heroList);
  }

  return allHeroIds;
};

/**
 * 根据类型获取阵容列表
 * 基于FormationService: 阵容类型管理逻辑
 */
TeamFormationsSchema.methods.getFormationsByType = function(type: FormationType): TeamFormation[] {
  return this.teamFormations.filter((formation: TeamFormation) => formation.type === type);
};

/**
 * 设置阵容类型
 * 基于FormationService: 阵容类型设置逻辑
 */
TeamFormationsSchema.methods.setFormationType = function(formationId: string, type: FormationType): boolean {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return false;
  }

  formation.type = type;
  return true;
};

/**
 * 设置当前阵容
 * 基于FormationService: setActiveFormation方法
 */
TeamFormationsSchema.methods.setCurrentFormation = function(formationId: string): boolean {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return false;
  }

  this.currTeamFormationId = formationId;
  return true;
};

/**
 * 获取当前阵容
 * 基于FormationService: 当前阵容获取逻辑
 */
TeamFormationsSchema.methods.getCurrentFormation = function(): TeamFormation | null {
  if (!this.currTeamFormationId) {
    return null;
  }

  return this.getFormation(this.currTeamFormationId);
};

/**
 * 检查是否为当前阵容
 * 基于FormationService: 当前阵容判断逻辑
 */
TeamFormationsSchema.methods.isCurrentFormation = function(formationId: string): boolean {
  return this.currTeamFormationId === formationId;
};

/**
 * 生成客户端阵容列表
 * 基于FormationService: 客户端数据转换需求
 */
TeamFormationsSchema.methods.makeClientFormationList = function(): any[] {
  return this.teamFormations.map((formation: TeamFormation) => ({
    uid: formation.uid,
    resId: formation.resId,
    name: formation.name,
    attack: formation.attack,
    defend: formation.defend,
    actualStrength: formation.actualStrength,
    teamId: formation.teamId,
    teamType: formation.teamType,
    type: formation.type,
    isLeague: formation.isLeague,
    isCurrent: this.isCurrentFormation(formation.uid),
    heroCount: this.getFormationHeroCount(formation.uid),
    isValid: this.validateFormation(formation.uid).isValid,
    useTactics: formation.useTactics,
    useDefTactics: formation.useDefTactics,
    freeKickHero: formation.freeKickHero,
    penaltiesHero: formation.penaltiesHero,
    cornerKickHero: formation.cornerKickHero
  }));
};

/**
 * 获取阵容摘要信息
 * 基于FormationService: 阵容详情获取需求
 */
TeamFormationsSchema.methods.getFormationSummary = function(formationId: string): any {
  const formation = this.getFormation(formationId);
  if (!formation) {
    return null;
  }

  const validation = this.validateFormation(formationId);
  const heroCount = this.getFormationHeroCount(formationId);

  return {
    uid: formation.uid,
    name: formation.name,
    resId: formation.resId,
    attack: formation.attack,
    defend: formation.defend,
    actualStrength: formation.actualStrength,
    heroCount: heroCount,
    isValid: validation.isValid,
    validationErrors: validation.errors,
    isCurrent: this.isCurrentFormation(formationId),
    type: formation.type,
    positionCounts: {
      GK: this.getPositionHeroCount(formationId, 'GK'),
      DL: this.getPositionHeroCount(formationId, 'DL'),
      DC: this.getPositionHeroCount(formationId, 'DC'),
      DR: this.getPositionHeroCount(formationId, 'DR'),
      ML: this.getPositionHeroCount(formationId, 'ML'),
      MC: this.getPositionHeroCount(formationId, 'MC'),
      MR: this.getPositionHeroCount(formationId, 'MR'),
      WL: this.getPositionHeroCount(formationId, 'WL'),
      ST: this.getPositionHeroCount(formationId, 'ST'),
      WR: this.getPositionHeroCount(formationId, 'WR'),
      AM: this.getPositionHeroCount(formationId, 'AM'),
      DM: this.getPositionHeroCount(formationId, 'DM')
    }
  };
};
