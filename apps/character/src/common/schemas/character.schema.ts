/**
 * 角色实体Schema
 * 基于旧项目player.js迁移而来，适配新的微服务架构
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

// 游戏进度枚举
export enum CreateRoleStep {
  WATCH_RECORD = 1,
  CREATE_TEAM = 2,
  COMPLETE = 3,
}

// 角色基础信息子文档
@Schema({ _id: false })
export class GameProgress {
  @Prop({ default: CreateRoleStep.WATCH_RECORD })
  createRoleStep: number;

  @Prop({ default: true })
  isNewer: boolean;

  @Prop({ default: false })
  isRobot: boolean;

  @Prop({ default: 0 })
  activeDay: number;

  @Prop({ default: 0 })
  recentActiveDay: number;
}

// 登录信息子文档
@Schema({ _id: false })
export class LoginInfo {
  @Prop({ default: 0 })
  loginTime: number;

  @Prop({ default: 0 })
  leaveTime: number;

  @Prop({ default: 0 })
  createTime: number;

  @Prop({ default: '' })
  ip: string;

  @Prop()
  frontendId?: string;

  @Prop()
  sessionId?: string;
}

// 体力信息子文档
@Schema({ _id: false })
export class EnergyInfo {
  @Prop({ default: 1 })
  buyEnergyCount: number;

  @Prop({ default: 1 })
  buyTime: number;

  @Prop({ default: 1 })
  freeTime: number;

  @Prop({ default: 0 })
  firstFree: number;

  @Prop({ default: 0 })
  secondFree: number;

  @Prop({ default: 0 })
  isEnergyFull: number;

  @Prop({ default: 0 })
  energyRecoverTime: number;
}

// VIP信息子文档
@Schema({ _id: false })
export class VipInfo {
  @Prop({ default: 0 })
  vip: number;

  @Prop({ default: 0 })
  vipExp: number;

  @Prop({ default: [] })
  isFirstRecharge: boolean[];

  @Prop({ default: 0 })
  firstRechargeRefreshTime: number;

  @Prop({ default: 0 })
  totalRecharge: number;
}

// 信仰信息子文档
@Schema({ _id: false })
export class BeliefInfo {
  // 信仰ID
  @Prop({ default: 0 })
  beliefId: number;

  @Prop({ default: 0 })
  honor: number;

  @Prop({ default: 0 })
  beliefNum: number;

  @Prop({ default: 0 })
  beliefLiveness: number;

  @Prop({ default: false })
  lockBelief: boolean;

  @Prop({ default: 0 })
  reChangeBeliefTime: number;

  @Prop({ default: [] })
  beliefChangeRecord: any[];
}

// 主角色Schema
@Schema({ 
  collection: 'characters', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Character {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  characterId: string;

  // 账号信息
  @Prop({ required: true, index: true })
  userId: string;

  // 区服信息
  @Prop({ required: true, index: true })
  serverId: string;

  // 平台外部ID
  @Prop({ required: true })
  openId: string;

  // 角色名称
  @Prop({ required: true, maxlength: 20 })
  name: string;

  // 外观信息
  @Prop({ default: '' })
  avatar: string;

  // 头像信息
  @Prop({ default: 10600011 })
  faceIcon: number;

  @Prop({ default: '' })
  faceUrl: string;

  @Prop({ type: [Number], default: [] })
  clubFaceIcon: number[];

  @Prop({ type: [Number], default: [] })
  countryFaceIcon: number[];

  // 等级和经验
  @Prop({ default: 1, min: 1, max: 100 })
  level: number;

  // 现金
  @Prop({ default: 10000, min: 0 })
  cash: number;

  // 金币
  @Prop({ default: 0, min: 0 })
  gold: number;

  // 体力
  @Prop({ default: 100, min: 0, max: 10000 })
  energy: number;

  // 声望和奖杯
  @Prop({ default: 0, min: 0 })
  fame: number;

  @Prop({ default: 0, min: 0 })
  allFame: number;

  // 奖杯
  @Prop({ default: 0, min: 0 })
  trophy: number;

  // 其他货币
  @Prop({ default: 0, min: 0 })
  worldCoin: number;

  // 合约碎片
  @Prop({ default: 0, min: 0 })
  chip: number;

  // 转播积分
  @Prop({ default: 0, min: 0 })
  integral: number;

  // 球场信息
  @Prop({ default: 0, min: 0 })
  fieldLevel: number;

  @Prop({ default: 0 })
  qualified: number;

  // 联赛信息
  @Prop({ default: 0 })
  league: number;

  // 嵌套文档
  @Prop({ type: GameProgress, default: () => ({}) })
  gameProgress: GameProgress;

  @Prop({ type: LoginInfo, default: () => ({}) })
  loginInfo: LoginInfo;

  @Prop({ type: EnergyInfo, default: () => ({}) })
  energyInfo: EnergyInfo;

  @Prop({ type: VipInfo, default: () => ({}) })
  vipInfo: VipInfo;

  @Prop({ type: BeliefInfo, default: () => ({}) })
  beliefInfo: BeliefInfo;

  // 信仰系统
  @Prop({ default: 0 })
  beliefId: number;     // 信仰ID

  // 兑换码系统
  @Prop({ type: Object, default: {} })
  deemCodeList: Record<string, string>; // 兑换码列表 Group => CodeId

  // 持续buff系统
  @Prop({ default: 0 })
  continuedBuffStartTime: number; // 持续buff开始时间

  @Prop({ default: 0 })
  continuedBuffEndTime: number;   // 持续buff结束时间

  @Prop({ default: 0 })
  lastBuffUpdateTime: number;     // 上一次更新buff的时间

  @Prop({ default: 0 })
  alreadyAddEnergyCount: number;  // 已添加体力的次数

  // 购买记录
  @Prop({ default: false })
  isBuyedCoach: boolean;

  @Prop({ default: 0 })
  buyBestFootballerNum: number;

  @Prop({ default: 0 })
  bestFootballerTime: number;

  // 战术显示
  @Prop({ default: 0 })
  isShowTactics: number;

  // 申请列表
  @Prop({ type: [String], default: [] })
  leagueList: string[];

  @Prop({ type: [Number], default: [] })
  exchangeNumList: number[];

  @Prop({ type: [Object], default: [] })
  associationSponsorReward: any[];

  // 注册时间
  @Prop({ default: 0 })
  regTime: number;

  // ==================== old项目缺失的关键字段 ====================

  // 首次奖励标识
  @Prop({ default: 0 })
  isFristGetReward: number; // 是否第一次领取1亿欧元奖励

  // 阵容激活状态
  @Prop({ default: 0 })
  formationAct: number; // 阵容激活状态

  // 球探系统（基于old项目Scout实体）
  @Prop({
    type: {
      scoutRp: { type: Number, default: 0 }, // 球探RP值
      scoutEnergy: { type: Number, default: 100 }, // 球探体力
      scoutGroup: [{
        type: { type: Number, required: true }, // 球探类型 (1=初级, 2=高级, 3=顶级)
        count: { type: Number, default: 0 }, // 可用次数
        getTime: { type: Number, default: 1 }, // 获取时间
      }],
      scoutPack: [{ type: MongooseSchema.Types.Mixed }], // 球探包中的球员数据
      isFrist: { type: Number, default: 0 }, // 是否第一次抽 (0=是, 1=不是)
      reTime: { type: Number, default: Date.now }, // 刷新时间
      lastEnergyRegenTime: { type: Date, default: Date.now }, // 最后体力恢复时间
      lastSearchTime: { type: Number, default: 0 }, // 最后搜索时间
      maxScoutPackSize: { type: Number, default: 10 }, // 球探包最大容量
    },
    default: () => ({
      scoutRp: 0,
      scoutEnergy: 100,
      scoutGroup: [
        { type: 1, count: 5, getTime: 1 }, // 初级球探组
        { type: 2, count: 1, getTime: 1 }, // 高级球探组
        { type: 3, count: 1, getTime: 1 }, // 顶级球探组
      ],
      scoutPack: [],
      isFrist: 0,
      reTime: Date.now(),
      lastEnergyRegenTime: new Date(),
      lastSearchTime: 0,
      maxScoutPackSize: 10,
    })
  })
  scoutData: {
    scoutRp: number;
    scoutEnergy: number;
    scoutGroup: Array<{
      type: number;
      count: number;
      getTime: number;
    }>;
    scoutPack: any[];
    isFrist: number;
    reTime: number;
    lastEnergyRegenTime: Date;
    lastSearchTime: number;
    maxScoutPackSize: number;
  };

  // 虚拟字段：是否在线
  get isOnline(): boolean {
    return !!(this.loginInfo?.frontendId && this.loginInfo?.sessionId);
  }

  // 虚拟字段：当前阵容ID（通过微服务获取）
  currentFormationId?: string;

  // 虚拟字段：球员数量（通过微服务获取）
  heroCount?: number;
}

export const CharacterSchema = SchemaFactory.createForClass(Character);


// 创建索引
CharacterSchema.index({ characterId: 1 }, { unique: true });
CharacterSchema.index({ userId: 1 });
CharacterSchema.index({ serverId: 1 });
CharacterSchema.index({ openId: 1 });
CharacterSchema.index({ level: 1 });
CharacterSchema.index({ 'vipInfo.vip': 1 });
CharacterSchema.index({ 'loginInfo.loginTime': 1 });
CharacterSchema.index({ 'loginInfo.createTime': 1 });

// 添加虚拟字段
CharacterSchema.virtual('isOnline').get(function() {
  return !!(this.loginInfo?.frontendId && this.loginInfo?.sessionId);
});

// 添加实例方法
CharacterSchema.methods.toCharacterInfo = function() {
  return {
    characterId: this.characterId,
    name: this.name,
    level: this.level,
    fame: this.fame,
    cash: this.cash,
    gold: this.gold,
    energy: this.energy,
    trophy: this.trophy,
    faceIcon: this.faceIcon,
    faceUrl: this.faceUrl,
    vip: this.vipInfo.vip,
    isOnline: this.isOnline,
  };
};

CharacterSchema.methods.getBattleData = function() {
  return {
    characterId: this.characterId,
    name: this.name,
    level: this.level,
    fieldLevel: this.fieldLevel,
    faceIcon: this.faceIcon,
    faceUrl: this.faceUrl,
    // 关联数据通过微服务获取
    // heroes: 通过HeroService获取
    // formations: 通过FormationService获取
    // items: 通过InventoryService获取
  };
};

// 定义方法接口 - 基于CharacterService的真实业务逻辑
export interface CharacterMethods {
  // 基础信息方法
  toCharacterInfo(): any;
  getBattleData(): any;

  // 货币管理方法 - 基于CharacterService的货币操作逻辑
  addCurrency(currencyType: string, amount: number): boolean;
  subtractCurrency(currencyType: string, amount: number): boolean;
  getCurrencyBalance(currencyType: string): number;
  hasSufficientCurrency(currencyType: string, amount: number): boolean;

  // 等级和经验管理 - 基于CharacterService的升级逻辑
  canLevelUp(): boolean;
  getNextLevelRequirement(): number;
  addExperience(experience: number): boolean;

  // VIP系统管理 - 基于CharacterService的VIP逻辑
  isVip(): boolean;
  getVipLevel(): number;
  getVipExpireTime(): number;
  isVipExpired(): boolean;

  // 体力系统管理 - 基于CharacterService的体力逻辑
  addEnergy(amount: number): boolean;
  subtractEnergy(amount: number): boolean;
  isEnergyFull(): boolean;
  getEnergyRegenTime(): number;

  // 球探系统管理 - 基于CharacterService的球探逻辑
  getScoutEnergy(): number;
  addScoutEnergy(amount: number): boolean;
  subtractScoutEnergy(amount: number): boolean;
  canUseScout(scoutType: number): boolean;
  updateScoutEnergyRegen(): void;

  // 登录和活跃度管理 - 基于CharacterService的登录逻辑
  updateLoginTime(): void;
  isOnlineNow(): boolean;
  getDaysActive(): number;
  updateActiveDay(): void;

  // 游戏进度管理 - 基于CharacterService的进度逻辑
  isNewPlayer(): boolean;
  completeNewPlayerGuide(): void;
  updateCreateRoleStep(step: number): void;

  // 数据统计和分析
  getTotalWealth(): number;
  getAccountAge(): number;
  getLastLoginDays(): number;
}

// 重新定义Document类型
export type CharacterDocument = Character & Document & CharacterMethods;

// ==================== 实例方法实现 ====================

/**
 * 添加货币
 * 基于CharacterService: addCurrencyInternal方法逻辑
 */
CharacterSchema.methods.addCurrency = function(currencyType: string, amount: number): boolean {
  if (amount <= 0) return false;

  const currentBalance = this[currencyType] || 0;
  this[currencyType] = currentBalance + amount;
  return true;
};

/**
 * 扣除货币
 * 基于CharacterService: subtractCurrencyInternal方法逻辑
 */
CharacterSchema.methods.subtractCurrency = function(currencyType: string, amount: number): boolean {
  if (amount <= 0) return false;

  const currentBalance = this[currencyType] || 0;
  if (currentBalance < amount) return false;

  this[currencyType] = currentBalance - amount;
  return true;
};

/**
 * 获取货币余额
 * 基于CharacterService: 货币查询逻辑
 */
CharacterSchema.methods.getCurrencyBalance = function(currencyType: string): number {
  return this[currencyType] || 0;
};

/**
 * 检查货币是否足够
 * 基于CharacterService: 货币验证逻辑
 */
CharacterSchema.methods.hasSufficientCurrency = function(currencyType: string, amount: number): boolean {
  const currentBalance = this[currencyType] || 0;
  return currentBalance >= amount;
};

/**
 * 检查是否可以升级
 * 基于CharacterService: 升级条件检查逻辑
 */
CharacterSchema.methods.canLevelUp = function(): boolean {
  const maxLevel = 100; // 基于CharacterService中的最大等级
  return this.level < maxLevel;
};

/**
 * 获取下一级所需经验
 * 基于CharacterService: 等级需求计算逻辑
 */
CharacterSchema.methods.getNextLevelRequirement = function(): number {
  if (!this.canLevelUp()) return 0;

  // 简化的经验计算公式
  return this.level * 1000;
};

/**
 * 添加经验
 * 基于CharacterService: 经验管理逻辑
 */
CharacterSchema.methods.addExperience = function(experience: number): boolean {
  if (experience <= 0) return false;

  // 注意：Character schema中没有experience字段，这里只是示例逻辑
  // 实际项目中可能通过其他方式管理经验
  return true;
};

/**
 * 检查是否为VIP
 * 基于CharacterService: VIP状态检查逻辑
 */
CharacterSchema.methods.isVip = function(): boolean {
  return this.vipInfo && this.vipInfo.vip > 0;
};

/**
 * 获取VIP等级
 * 基于CharacterService: VIP等级获取逻辑
 */
CharacterSchema.methods.getVipLevel = function(): number {
  return this.vipInfo?.vip || 0;
};

/**
 * 获取VIP过期时间
 * 基于CharacterService: VIP时间管理逻辑
 */
CharacterSchema.methods.getVipExpireTime = function(): number {
  return this.vipInfo?.vipExpireTime || 0;
};

/**
 * 检查VIP是否过期
 * 基于CharacterService: VIP状态验证逻辑
 */
CharacterSchema.methods.isVipExpired = function(): boolean {
  if (!this.isVip()) return true;

  const expireTime = this.getVipExpireTime();
  return expireTime > 0 && expireTime < Date.now();
};

/**
 * 添加体力
 * 基于CharacterService: 体力管理逻辑
 */
CharacterSchema.methods.addEnergy = function(amount: number): boolean {
  if (amount <= 0) return false;

  const maxEnergy = 10000; // 基于schema中的最大值
  this.energy = Math.min(maxEnergy, this.energy + amount);
  return true;
};

/**
 * 扣除体力
 * 基于CharacterService: 体力消耗逻辑
 */
CharacterSchema.methods.subtractEnergy = function(amount: number): boolean {
  if (amount <= 0 || this.energy < amount) return false;

  this.energy = Math.max(0, this.energy - amount);
  return true;
};

/**
 * 检查体力是否满
 * 基于CharacterService: 体力状态检查逻辑
 */
CharacterSchema.methods.isEnergyFull = function(): boolean {
  const maxEnergy = 10000;
  return this.energy >= maxEnergy;
};

/**
 * 获取体力恢复时间
 * 基于CharacterService: 体力恢复机制
 */
CharacterSchema.methods.getEnergyRegenTime = function(): number {
  if (this.isEnergyFull()) return 0;

  // 假设每5分钟恢复1点体力
  const regenRate = 5 * 60 * 1000; // 5分钟
  const maxEnergy = 10000;
  const missingEnergy = maxEnergy - this.energy;

  return missingEnergy * regenRate;
};

/**
 * 获取球探体力
 * 基于CharacterService: 球探系统管理逻辑
 */
CharacterSchema.methods.getScoutEnergy = function(): number {
  return this.scoutData?.scoutEnergy || 0;
};

/**
 * 添加球探体力
 * 基于CharacterService: 球探体力管理逻辑
 */
CharacterSchema.methods.addScoutEnergy = function(amount: number): boolean {
  if (amount <= 0 || !this.scoutData) return false;

  const maxScoutEnergy = 100;
  this.scoutData.scoutEnergy = Math.min(maxScoutEnergy, this.scoutData.scoutEnergy + amount);
  return true;
};

/**
 * 扣除球探体力
 * 基于CharacterService: 球探体力消耗逻辑
 */
CharacterSchema.methods.subtractScoutEnergy = function(amount: number): boolean {
  if (amount <= 0 || !this.scoutData || this.scoutData.scoutEnergy < amount) return false;

  this.scoutData.scoutEnergy = Math.max(0, this.scoutData.scoutEnergy - amount);
  return true;
};

/**
 * 检查是否可以使用球探
 * 基于CharacterService: 球探使用条件检查逻辑
 */
CharacterSchema.methods.canUseScout = function(scoutType: number): boolean {
  if (!this.scoutData) return false;

  // 检查体力是否足够（不同类型球探消耗不同体力）
  const energyCost = scoutType === 1 ? 10 : scoutType === 2 ? 20 : 30;
  if (this.scoutData.scoutEnergy < energyCost) return false;

  // 检查球探组是否有可用次数
  const scoutGroup = this.scoutData.scoutGroup.find(group => group.type === scoutType);
  return scoutGroup ? scoutGroup.count > 0 : false;
};

/**
 * 更新球探体力恢复
 * 基于CharacterService: updateScoutEnergyRegen方法逻辑
 */
CharacterSchema.methods.updateScoutEnergyRegen = function(): void {
  if (!this.scoutData) return;

  const now = new Date();
  const lastRegenTime = new Date(this.scoutData.lastEnergyRegenTime);
  const timeDiff = now.getTime() - lastRegenTime.getTime();

  // 每5分钟恢复1点体力
  const regenInterval = 5 * 60 * 1000;
  const regenPoints = Math.floor(timeDiff / regenInterval);

  if (regenPoints > 0 && this.scoutData.scoutEnergy < 100) {
    this.scoutData.scoutEnergy = Math.min(100, this.scoutData.scoutEnergy + regenPoints);
    this.scoutData.lastEnergyRegenTime = now;
  }
};

/**
 * 更新登录时间
 * 基于CharacterService: 登录时间管理逻辑
 */
CharacterSchema.methods.updateLoginTime = function(): void {
  const now = Date.now();
  this.loginInfo.loginTime = now;
  this.loginInfo.lastLoginTime = this.loginInfo.loginTime || now;
};

/**
 * 检查是否在线
 * 基于CharacterService: 在线状态检查逻辑
 */
CharacterSchema.methods.isOnlineNow = function(): boolean {
  return !!(this.loginInfo?.frontendId && this.loginInfo?.sessionId);
};

/**
 * 获取活跃天数
 * 基于CharacterService: 活跃度统计逻辑
 */
CharacterSchema.methods.getDaysActive = function(): number {
  return this.gameProgress?.activeDay || 0;
};

/**
 * 更新活跃天数
 * 基于CharacterService: 活跃度更新逻辑
 */
CharacterSchema.methods.updateActiveDay = function(): void {
  if (!this.gameProgress) return;

  const today = new Date().toDateString();
  const lastActiveDate = new Date(this.gameProgress.recentActiveDay || 0).toDateString();

  if (today !== lastActiveDate) {
    this.gameProgress.activeDay += 1;
    this.gameProgress.recentActiveDay = Date.now();
  }
};

/**
 * 检查是否为新玩家
 * 基于CharacterService: 新手状态检查逻辑
 */
CharacterSchema.methods.isNewPlayer = function(): boolean {
  return this.gameProgress?.isNewer || false;
};

/**
 * 完成新手引导
 * 基于CharacterService: 新手引导完成逻辑
 */
CharacterSchema.methods.completeNewPlayerGuide = function(): void {
  if (this.gameProgress) {
    this.gameProgress.isNewer = false;
    this.gameProgress.createRoleStep = CreateRoleStep.COMPLETE;
  }
};

/**
 * 更新创建角色步骤
 * 基于CharacterService: 角色创建进度管理逻辑
 */
CharacterSchema.methods.updateCreateRoleStep = function(step: number): void {
  if (this.gameProgress) {
    this.gameProgress.createRoleStep = step;
  }
};

/**
 * 获取总财富
 * 基于CharacterService: 财富统计逻辑
 */
CharacterSchema.methods.getTotalWealth = function(): number {
  return (this.cash || 0) + (this.gold || 0) * 100 + (this.worldCoin || 0) * 10;
};

/**
 * 获取账号年龄（天数）
 * 基于CharacterService: 账号统计逻辑
 */
CharacterSchema.methods.getAccountAge = function(): number {
  const createTime = this.loginInfo?.createTime || Date.now();
  const daysDiff = Math.floor((Date.now() - createTime) / (24 * 60 * 60 * 1000));
  return Math.max(0, daysDiff);
};

/**
 * 获取距离上次登录的天数
 * 基于CharacterService: 登录统计逻辑
 */
CharacterSchema.methods.getLastLoginDays = function(): number {
  const lastLoginTime = this.loginInfo?.lastLoginTime || Date.now();
  const daysDiff = Math.floor((Date.now() - lastLoginTime) / (24 * 60 * 60 * 1000));
  return Math.max(0, daysDiff);
};
