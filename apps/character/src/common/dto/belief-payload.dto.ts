import { IsString, IsN<PERSON>ber, IsOptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ength } from 'class-validator';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

/**
 * 创建信仰载荷DTO
 * 基于old项目: 创建新的信仰组织
 */
export class CreateBeliefPayloadDto extends BasePayloadDto {
  @IsString()
  @MaxLength(20, { message: '信仰名称不能超过20个字符' })
  name: string; // 信仰名称

  @IsOptional()
  @IsString()
  @MaxLength(500, { message: '信仰公告不能超过500个字符' })
  notice?: string; // 信仰公告
}

/**
 * 加入信仰载荷DTO
 * 基于old项目: 玩家申请加入指定信仰
 */
export class JoinBeliefPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId: number; // 信仰ID
}

/**
 * 退出信仰载荷DTO
 * 基于old项目: 玩家主动退出当前信仰
 */
export class LeaveBeliefPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: '退出原因不能超过100个字符' })
  reason?: string; // 退出原因
}

/**
 * 信仰捐献载荷DTO
 * 基于old项目: donateBeliefGold接口
 */
export class DonateBeliefPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '捐献类型必须为1或2' })
  @Max(2, { message: '捐献类型必须为1或2' })
  type: number; // 捐献类型：1-现金捐献，2-球币捐献

  @IsNumber()
  @Min(1, { message: '捐献数量必须大于0' })
  amount: number; // 捐献数量
}

/**
 * 更新信仰公告载荷DTO
 * 基于old项目: 董事长更新信仰公告
 */
export class UpdateBeliefNoticePayloadDto extends BasePayloadDto {
  @IsString()
  @MaxLength(500, { message: '信仰公告不能超过500个字符' })
  notice: string; // 新的信仰公告
}

/**
 * 获取信仰信息载荷DTO
 * 基于old项目: 获取指定信仰的详细信息
 */
export class GetBeliefInfoPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId: number; // 信仰ID
}

/**
 * 获取信仰列表载荷DTO
 * 基于old项目: 获取信仰列表（支持分页和搜索）
 */
export class GetBeliefListPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number; // 页码，默认1

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number; // 每页数量，默认20

  @IsOptional()
  @IsString()
  @MaxLength(50, { message: '搜索关键词不能超过50个字符' })
  keyword?: string; // 搜索关键词

  @IsOptional()
  @IsString()
  sortBy?: string; // 排序字段：level, memberCount, createTime

  @IsOptional()
  @IsString()
  sortOrder?: string; // 排序方向：asc, desc
}

/**
 * 设置信仰领导者载荷DTO
 * 基于old项目: 董事长任命或撤销领导职位
 */
export class SetBeliefLeaderPayloadDto extends BasePayloadDto {
  @IsString()
  targetPlayerId: string; // 目标玩家ID

  @IsNumber()
  @Min(1, { message: '职位必须在1-4之间' })
  @Max(4, { message: '职位必须在1-4之间' })
  position: number; // 职位：1-董事长，2-副董事长，3-总经理，4-总监

  @IsBoolean()
  isAppoint: boolean; // true-任命，false-撤销
}

/**
 * 获取信仰成员列表载荷DTO
 * 基于old项目: 获取信仰的所有成员信息
 */
export class GetBeliefMembersPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId?: number; // 信仰ID，不传则获取自己信仰的成员

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number; // 页码，默认1

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number; // 每页数量，默认50

  @IsOptional()
  @IsString()
  sortBy?: string; // 排序字段：contribution, joinTime, lastActiveTime

  @IsOptional()
  @IsString()
  sortOrder?: string; // 排序方向：asc, desc
}

/**
 * 获取信仰排行载荷DTO
 * 基于old项目: 获取信仰等级和活跃度排行
 */
export class GetBeliefRankPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsString()
  rankType?: string; // 排行类型：level, activity, donation

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number; // 页码，默认1

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number; // 每页数量，默认50
}

/**
 * 升级信仰技能载荷DTO
 * 基于old项目: upgradeBeliefSkill接口
 */
export class UpgradeBeliefSkillPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '技能ID必须大于0' })
  skillId: number; // 技能ID
}

/**
 * 获取信仰技能列表载荷DTO
 * 基于old项目: 获取角色的所有信仰技能信息
 */
export class GetBeliefSkillListPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId，无需额外参数
}

/**
 * 踢出信仰成员载荷DTO
 * 基于old项目: 董事长踢出信仰成员
 */
export class KickBeliefMemberPayloadDto extends BasePayloadDto {
  @IsString()
  targetPlayerId: string; // 目标玩家ID

  @IsOptional()
  @IsString()
  @MaxLength(100, { message: '踢出原因不能超过100个字符' })
  reason?: string; // 踢出原因
}

/**
 * 解散信仰载荷DTO
 * 基于old项目: 董事长解散信仰
 */
export class DisbandBeliefPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: '解散原因不能超过100个字符' })
  reason?: string; // 解散原因
}

/**
 * 转让董事长载荷DTO
 * 基于old项目: 董事长转让职位给其他成员
 */
export class TransferChairmanPayloadDto extends BasePayloadDto {
  @IsString()
  targetPlayerId: string; // 目标玩家ID
}

/**
 * 获取信仰动态载荷DTO
 * 基于old项目: 获取信仰的最新动态消息
 */
export class GetBeliefNotificationsPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId?: number; // 信仰ID，不传则获取自己信仰的动态

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number; // 页码，默认1

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number; // 每页数量，默认20

  @IsOptional()
  @IsString()
  messageType?: string; // 消息类型：system, player, battle
}

/**
 * 搜索信仰载荷DTO
 * 基于old项目: 按名称搜索信仰
 */
export class SearchBeliefPayloadDto extends BasePayloadDto {
  @IsString()
  @MaxLength(50, { message: '搜索关键词不能超过50个字符' })
  keyword: string; // 搜索关键词

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(50, { message: '每页数量不能超过50' })
  limit?: number; // 返回数量限制，默认20
}

/**
 * 获取信仰统计载荷DTO
 * 基于old项目: 获取信仰的各种统计数据
 */
export class GetBeliefStatsPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId?: number; // 信仰ID，不传则获取自己信仰的统计

  @IsOptional()
  @IsString()
  statsType?: string; // 统计类型：overview, members, donations, battles
}
