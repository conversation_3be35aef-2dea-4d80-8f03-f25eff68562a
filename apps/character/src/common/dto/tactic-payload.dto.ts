/**
 * Tactic模块的Payload DTO定义
 * 
 * 为tactic.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsBoolean, Min, Max, Length, IsArray, ValidateNested } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 战术基础查询相关 ====================

/**
 * 获取角色战术Payload DTO
 * @MessagePattern('tactic.getTactics')
 */
export class GetTacticsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 获取战术列表Payload DTO
 * @MessagePattern('tactic.getTacticList')
 */
export class GetTacticListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 获取战术信息Payload DTO
 * @MessagePattern('tactic.getTacticInfo')
 */
export class GetTacticInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '战术键值', example: 'tactic_attack_001' })
  @Expose()
  @IsString({ message: '战术键值必须是字符串' })
  @Length(1, 50, { message: '战术键值长度必须在1-50个字符之间' })
  tacticKey: string;
}

// ==================== 2. 战术操作相关 ====================

/**
 * 激活战术Payload DTO
 * @MessagePattern('tactic.activateTactic')
 */
export class ActivateTacticPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '战术键值', example: 'tactic_attack_001' })
  @Expose()
  @IsString({ message: '战术键值必须是字符串' })
  @Length(1, 50, { message: '战术键值长度必须在1-50个字符之间' })
  tacticKey: string;
}

/**
 * 升级战术Payload DTO
 * @MessagePattern('tactic.upgradeTactic')
 */
export class UpgradeTacticPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '战术键值', example: 'tactic_attack_001' })
  @Expose()
  @IsString({ message: '战术键值必须是字符串' })
  @Length(1, 50, { message: '战术键值长度必须在1-50个字符之间' })
  tacticKey: string;
}

/**
 * 解锁战术Payload DTO
 * @MessagePattern('tactic.unlockTactic')
 */
export class UnlockTacticPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '战术键值', example: 'tactic_attack_001' })
  @Expose()
  @IsString({ message: '战术键值必须是字符串' })
  @Length(1, 50, { message: '战术键值长度必须在1-50个字符之间' })
  tacticKey: string;
}

// ==================== 3. 阵容战术配置相关 ====================

/**
 * 设置阵容战术Payload DTO
 * @MessagePattern('tactic.setFormationTactics')
 */
export class SetFormationTacticsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '阵容ID', example: 'formation_67890' })
  @Expose()
  @IsString({ message: '阵容ID必须是字符串' })
  @Length(1, 50, { message: '阵容ID长度必须在1-50个字符之间' })
  formationId: string;

  @ApiProperty({ description: '攻击战术ID', example: 'tactic_attack_001' })
  @Expose()
  @IsString({ message: '攻击战术ID必须是字符串' })
  @Length(1, 50, { message: '攻击战术ID长度必须在1-50个字符之间' })
  attackTacticId: string;

  @ApiProperty({ description: '防守战术ID', example: 'tactic_defense_001' })
  @Expose()
  @IsString({ message: '防守战术ID必须是字符串' })
  @Length(1, 50, { message: '防守战术ID长度必须在1-50个字符之间' })
  defenseTacticId: string;
}

/**
 * 获取阵容战术Payload DTO
 * @MessagePattern('tactic.getFormationTactics')
 */
export class GetFormationTacticsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '阵容ID', example: 'formation_67890' })
  @Expose()
  @IsString({ message: '阵容ID必须是字符串' })
  @Length(1, 50, { message: '阵容ID长度必须在1-50个字符之间' })
  formationId: string;
}

// ==================== 4. 战术计算相关 ====================

/**
 * 计算战术效果Payload DTO
 * @MessagePattern('tactic.calculateTacticEffects')
 */
export class CalculateTacticEffectsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '战术键值', example: 'tactic_attack_001' })
  @Expose()
  @IsString({ message: '战术键值必须是字符串' })
  @Length(1, 50, { message: '战术键值长度必须在1-50个字符之间' })
  tacticKey: string;

  @ApiProperty({ description: '战术等级', example: 5 })
  @Expose()
  @IsNumber({}, { message: '战术等级必须是数字' })
  @Min(1, { message: '战术等级不能小于1' })
  @Max(100, { message: '战术等级不能大于100' })
  level: number;
}

/**
 * 计算球员加成Payload DTO
 * @MessagePattern('tactic.calculateHeroBonus')
 */
export class CalculateHeroBonusPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '球员ID', example: 'hero_11111' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '阵容ID', example: 'formation_67890' })
  @Expose()
  @IsString({ message: '阵容ID必须是字符串' })
  @Length(1, 50, { message: '阵容ID长度必须在1-50个字符之间' })
  formationId: string;
}

// ==================== 5. 战术统计相关 ====================

/**
 * 更新战术使用统计Payload DTO
 * @MessagePattern('tactic.updateTacticUsage')
 */
export class UpdateTacticUsagePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '战术键值', example: 'tactic_attack_001' })
  @Expose()
  @IsString({ message: '战术键值必须是字符串' })
  @Length(1, 50, { message: '战术键值长度必须在1-50个字符之间' })
  tacticKey: string;

  @ApiProperty({ description: '是否胜利', example: true })
  @Expose()
  @IsBoolean({ message: '是否胜利必须是布尔值' })
  isWin: boolean;
}

// ==================== 6. 批量操作相关 ====================

/**
 * 批量操作项DTO
 */
export class TacticOperationDto {
  @ApiProperty({ description: '操作类型', example: 'activate', enum: ['activate', 'upgrade', 'unlock'] })
  @Expose()
  @IsString({ message: '操作类型必须是字符串' })
  @Length(1, 20, { message: '操作类型长度必须在1-20个字符之间' })
  type: 'activate' | 'upgrade' | 'unlock';

  @ApiProperty({ description: '战术键值', example: 'tactic_attack_001' })
  @Expose()
  @IsString({ message: '战术键值必须是字符串' })
  @Length(1, 50, { message: '战术键值长度必须在1-50个字符之间' })
  tacticKey: string;
}

/**
 * 批量操作战术Payload DTO
 * @MessagePattern('tactic.batchOperateTactics')
 */
export class BatchOperateTacticsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '操作列表', type: [TacticOperationDto] })
  @Expose()
  @IsArray({ message: '操作列表必须是数组' })
  @ValidateNested({ each: true, message: '操作列表中的每个项目格式不正确' })
  @Type(() => TacticOperationDto)
  operations: TacticOperationDto[];
}
