/**
 * Ground模块的Payload DTO定义
 * 
 * 为ground.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsBoolean, Min, Max, Length } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 场地训练相关 ====================

/**
 * 获取场地训练信息Payload DTO
 * @MessagePattern('ground.getTrainInfo')
 */
export class GetTrainInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 场地训练Payload DTO
 * @MessagePattern('ground.train')
 */
export class TrainPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '球员ID', example: 'hero_67890' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '训练位置索引', example: 0 })
  @Expose()
  @IsNumber({}, { message: '训练位置索引必须是数字' })
  @Min(0, { message: '训练位置索引不能小于0' })
  @Max(20, { message: '训练位置索引不能大于20' })
  index: number;

  @ApiProperty({ description: '训练类型', example: 1 })
  @Expose()
  @IsNumber({}, { message: '训练类型必须是数字' })
  @Min(1, { message: '训练类型不能小于1' })
  @Max(10, { message: '训练类型不能大于10' })
  type: number;

  @ApiPropertyOptional({ description: '是否锁定训练（可选）', example: false })
  @Expose()
  @IsOptional()
  @IsBoolean({ message: '是否锁定训练必须是布尔值' })
  isLock?: boolean;
}

/**
 * 获取场地训练奖励Payload DTO
 * @MessagePattern('ground.getReward')
 */
export class GetRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '训练位置索引', example: 0 })
  @Expose()
  @IsNumber({}, { message: '训练位置索引必须是数字' })
  @Min(0, { message: '训练位置索引不能小于0' })
  @Max(20, { message: '训练位置索引不能大于20' })
  index: number;
}

// ==================== 2. 名人堂相关 ====================

/**
 * 名人堂入驻Payload DTO
 * @MessagePattern('ground.inputNotable')
 */
export class InputNotablePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '球员ID', example: 'hero_67890' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 获取名人堂列表Payload DTO
 * @MessagePattern('ground.getNotableList')
 */
export class GetNotableListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

// ==================== 3. 医疗中心相关 ====================

/**
 * 获取医疗中心信息Payload DTO
 * @MessagePattern('ground.getHospitalInfo')
 */
export class GetHospitalInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 医疗中心治疗Payload DTO
 * @MessagePattern('ground.inputHospital')
 */
export class InputHospitalPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '球员ID', example: 'hero_67890' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '医疗位置索引', example: 0 })
  @Expose()
  @IsNumber({}, { message: '医疗位置索引必须是数字' })
  @Min(0, { message: '医疗位置索引不能小于0' })
  @Max(10, { message: '医疗位置索引不能大于10' })
  index: number;
}

/**
 * 获取医疗奖励Payload DTO
 * @MessagePattern('ground.getHospitalReward')
 */
export class GetHospitalRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '医疗位置索引', example: 0 })
  @Expose()
  @IsNumber({}, { message: '医疗位置索引必须是数字' })
  @Min(0, { message: '医疗位置索引不能小于0' })
  @Max(10, { message: '医疗位置索引不能大于10' })
  index: number;
}

// ==================== 4. 球迷信息相关 ====================

/**
 * 获取球迷信息Payload DTO
 * @MessagePattern('ground.getFansInfo')
 */
export class GetFansInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}
