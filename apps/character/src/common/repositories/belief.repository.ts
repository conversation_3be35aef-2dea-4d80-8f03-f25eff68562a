import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseRepository } from '@libs/common/repository';
import { Belief } from '@character/modules/belief/schemas/belief.schema';
import { XResult, XResultUtils } from '@libs/common/types';
import { GameConfigFacade, BeliefDefinition } from '@libs/game-config';

@Injectable()
export class BeliefRepository extends BaseRepository<Belief> implements OnModuleInit {
  constructor(
    @InjectModel(Belief.name) model: Model<Belief>,
    private readonly gameConfigFacade: GameConfigFacade,
  ) {
    super(model, 'BeliefRepository');
  }

  async onModuleInit() {
    await this.initializeBeliefs();
  }

  /**
   * @description 服务启动时，初始化所有信仰数据
   */
  async initializeBeliefs(): Promise<XResult<void>> {
    this.logger.log('Initializing beliefs data...');
    const beliefConfigs = await this.gameConfigFacade.belief.getAll();
    const beliefIdsInDbResult = await this.findManyLean({}, { select: 'beliefId' });

    if (XResultUtils.isFailure(beliefIdsInDbResult)) {
      this.logger.error('Failed to get belief IDs from DB', beliefIdsInDbResult);
      return beliefIdsInDbResult;
    }

    const beliefIdsInDb = beliefIdsInDbResult.data.map((b: any) => b.beliefId);
    const beliefsToCreate: { beliefId: number }[] = [];

    for (const config of beliefConfigs) {
      if (!beliefIdsInDb.includes(config.id)) {
        beliefsToCreate.push({ beliefId: config.id });
      }
    }

    if (beliefsToCreate.length > 0) {
      const createResult = await this.createMany(beliefsToCreate);
      if (XResultUtils.isFailure(createResult)) {
        this.logger.error('Failed to create new beliefs', createResult);
        return createResult;
      }
      this.logger.log(`Created ${beliefsToCreate.length} new beliefs.`);
    }

    this.logger.log('Beliefs data initialization complete.');
    return XResultUtils.ok(undefined);
  }

  /**
   * @description 增加信仰资金
   * @param beliefId 信仰ID
   * @param amount 增加的数量
   */
  async addBeliefGold(beliefId: number, amount: number): Promise<XResult<Belief | null>> {
    return this.increment({ beliefId }, { beliefGold: amount });
  }
}
