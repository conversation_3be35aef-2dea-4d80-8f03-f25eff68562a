/**
 * Hero微服务类型定义
 * 替代game-config中已废弃的类型定义
 * 
 * 基于真实的配置数据结构定义类型
 */
// 导入统一的位置定义
import { HeroPosition } from '@libs/game-constants';

/**
 * 球员品质枚举
 */
export enum HeroQuality {
  WHITE = 1,   // 白色
  GREEN = 2,   // 绿色
  BLUE = 3,    // 蓝色
  PURPLE = 4,  // 紫色
  ORANGE = 5,  // 橙色
  RED = 6,     // 红色
}

/**
 * 技能类型枚举
 */
export enum SkillType {
  PASSIVE = 1,  // 被动技能
  ACTIVE = 2,   // 主动技能
  TRIGGER = 3,  // 触发技能
  AURA = 4,     // 光环技能
}

/**
 * 技能位置枚举
 */
export enum SkillPosition {
  ALL = 0,      // 全位置
  GK = 1,       // 门将
  DEF = 2,      // 后卫
  MID = 3,      // 中场
  FWD = 4,      // 前锋
}

/**
 * 位置映射表（字符串到枚举的映射）- 基于old项目位置标识
 */
export const POSITION_MAP: Record<string, HeroPosition> = {
  'GK': HeroPosition.GK,
  'DC': HeroPosition.DC,
  'DL': HeroPosition.DL,
  'DR': HeroPosition.DR,
  'DM': HeroPosition.DM,
  'MC': HeroPosition.MC,
  'ML': HeroPosition.ML,
  'MR': HeroPosition.MR,
  'AM': HeroPosition.AM,
  'ST': HeroPosition.ST,
  'WL': HeroPosition.WL,
  'WR': HeroPosition.WR,
};

/**
 * 品质映射表（数字到枚举的映射）
 */
export const QUALITY_MAP: Record<number, HeroQuality> = {
  1: HeroQuality.WHITE,
  2: HeroQuality.GREEN,
  3: HeroQuality.BLUE,
  4: HeroQuality.PURPLE,
  5: HeroQuality.ORANGE,
  6: HeroQuality.RED,
};

/**
 * 位置中文名称映射 - 基于old项目位置标识
 */
export const POSITION_NAMES: Record<HeroPosition, string> = {
  [HeroPosition.GK]: '门将',
  [HeroPosition.DC]: '中后卫',
  [HeroPosition.DL]: '左后卫',
  [HeroPosition.DR]: '右后卫',
  [HeroPosition.DM]: '后腰',
  [HeroPosition.MC]: '中前卫',
  [HeroPosition.ML]: '左前卫',
  [HeroPosition.MR]: '右前卫',
  [HeroPosition.AM]: '前腰',
  [HeroPosition.ST]: '中锋',
  [HeroPosition.WL]: '左边锋',
  [HeroPosition.WR]: '右边锋',
};

/**
 * 品质中文名称映射
 */
export const QUALITY_NAMES: Record<HeroQuality, string> = {
  [HeroQuality.WHITE]: '白色',
  [HeroQuality.GREEN]: '绿色',
  [HeroQuality.BLUE]: '蓝色',
  [HeroQuality.PURPLE]: '紫色',
  [HeroQuality.ORANGE]: '橙色',
  [HeroQuality.RED]: '红色',
};

/**
 * 技能类型中文名称映射
 */
export const SKILL_TYPE_NAMES: Record<SkillType, string> = {
  [SkillType.PASSIVE]: '被动技能',
  [SkillType.ACTIVE]: '主动技能',
  [SkillType.TRIGGER]: '触发技能',
  [SkillType.AURA]: '光环技能',
};

/**
 * 技能位置中文名称映射
 */
export const SKILL_POSITION_NAMES: Record<SkillPosition, string> = {
  [SkillPosition.ALL]: '全位置',
  [SkillPosition.GK]: '门将',
  [SkillPosition.DEF]: '后卫',
  [SkillPosition.MID]: '中场',
  [SkillPosition.FWD]: '前锋',
};

/**
 * 工具函数：将字符串位置转换为枚举
 */
export function stringToPosition(position: string): HeroPosition | null {
  return POSITION_MAP[position.toUpperCase()] || null;
}

/**
 * 工具函数：将数字品质转换为枚举
 */
export function numberToQuality(quality: number): HeroQuality | null {
  return QUALITY_MAP[quality] || null;
}

/**
 * 工具函数：检查位置是否为门将
 */
export function isGoalkeeperPosition(position: string | HeroPosition): boolean {
  if (typeof position === 'string') {
    return position.toUpperCase() === 'GK';
  }
  return position === HeroPosition.GK;
}

/**
 * 工具函数：检查位置是否为后卫
 */
export function isDefenderPosition(position: string | HeroPosition): boolean {
  const defenderPositions = [HeroPosition.DC, HeroPosition.DL, HeroPosition.DR];
  if (typeof position === 'string') {
    const pos = stringToPosition(position);
    return pos ? defenderPositions.includes(pos) : false;
  }
  return defenderPositions.includes(position);
}

/**
 * 工具函数：检查位置是否为中场
 */
export function isMidfielderPosition(position: string | HeroPosition): boolean {
  const midfielderPositions = [HeroPosition.DM, HeroPosition.MC, HeroPosition.AM, HeroPosition.ML, HeroPosition.MR];
  if (typeof position === 'string') {
    const pos = stringToPosition(position);
    return pos ? midfielderPositions.includes(pos) : false;
  }
  return midfielderPositions.includes(position);
}

/**
 * 工具函数：检查位置是否为前锋
 */
export function isForwardPosition(position: string | HeroPosition): boolean {
  const forwardPositions = [HeroPosition.WL, HeroPosition.WR, HeroPosition.ST];
  if (typeof position === 'string') {
    const pos = stringToPosition(position);
    return pos ? forwardPositions.includes(pos) : false;
  }
  return forwardPositions.includes(position);
}
