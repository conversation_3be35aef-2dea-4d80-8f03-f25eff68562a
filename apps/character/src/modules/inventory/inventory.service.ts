/**
 * 统一的角色物品背包业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 合并ItemService和InventoryService的功能，提供完整的物品管理能力
 */

import { Injectable } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import { InventoryRepository } from '../../common/repositories/inventory.repository';
import {
  InventoryDocument,
  Item,
  InventorySummary,
  ItemUseResult
} from '../../common/schemas/inventory.schema';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 统一的角色物品背包业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和微服务调用能力
 *
 * 🎯 核心功能：
 * - 物品添加、移除、查询
 * - 背包页签管理和扩展
 * - 物品使用和奖励处理
 * - 物品堆叠和排序
 * - 过期物品清理
 * - 跨服务业务逻辑（货币扣除、奖励发放等）
 *
 * 🚀 性能优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的缓存机制
 * - 批量操作减少数据库访问
 * - 事务支持确保数据一致性
 * - 智能堆叠算法优化存储
 * - 微服务调用的错误处理和重试机制
 *
 * 🔗 微服务依赖：
 * - Economy服务：背包扩展时扣除货币
 * - Activity服务：物品使用时可能触发活动奖励
 * - Hero服务：装备类物品可能影响英雄属性
 */
@Injectable()
export class InventoryService extends BaseService {
  constructor(
    private readonly inventoryRepository: InventoryRepository,
    microserviceClient?: MicroserviceClientService
  ) {
    super('InventoryService', microserviceClient);
  }

  // ========== 内部调用方法 ==========

  /**
   * 内部道具操作方法 - 添加道具
   * 供其他业务方法内部调用，无需DTO包装
   * 参考character.service.ts中的addCurrencyInternal实现
   *
   * @param characterId 角色ID
   * @param configId 道具配置ID
   * @param quantity 数量
   * @param reason 操作原因
   * @returns 操作结果
   */
  async addItemInternal(
    characterId: string,
    configId: number,
    quantity: number,
    reason: string = '添加道具'
  ): Promise<XResult<{
    characterId: string;
    configId: number;
    quantity: number;
    addedIds: string[];
    slotsUsed: number;
    reason: string;
  }>> {
    // 1. 参数验证
    if (!characterId || !configId || quantity <= 0) {
      return XResultUtils.error('参数无效：characterId、configId和quantity必须有效', 'INVALID_PARAMETERS');
    }

    // 2. 获取角色背包数据（如果不存在会自动初始化）
    const inventoryResult = await this.getInventory(characterId);
    if (XResultUtils.isFailure(inventoryResult)) {
      return XResultUtils.error(`获取角色背包失败: ${inventoryResult.message}`, inventoryResult.code);
    }

    // 3. 添加道具到背包
    const addResult = await this.inventoryRepository.addItem(characterId, configId, quantity);
    if (XResultUtils.isFailure(addResult)) {
      return XResultUtils.error(`添加道具失败: ${addResult.message}`, addResult.code || 'ADD_ITEM_FAILED');
    }

    const result = {
      characterId,
      configId,
      quantity,
      addedIds: addResult.data.addedIds,
      slotsUsed: addResult.data.slotsUsed,
      reason,
    };

    this.logger.log(`添加道具成功: ${characterId}, 道具${configId}: +${quantity}, 新增实例: ${result.addedIds.length}个, 使用槽位: ${result.slotsUsed}`);
    return XResultUtils.ok(result);
  }

  /**
   * 内部道具操作方法 - 扣除道具
   * 供其他业务方法内部调用，无需DTO包装
   * 参考character.service.ts中的deductCurrencyInternal实现
   *
   * @param characterId 角色ID
   * @param configId 道具配置ID
   * @param quantity 扣除数量
   * @param reason 操作原因
   * @param allowInsufficient 是否允许数量不足时部分扣除（默认false）
   * @returns 操作结果
   */
  async deductItemInternal(
    characterId: string,
    configId: number,
    quantity: number,
    reason: string = '扣除道具',
    allowInsufficient: boolean = false
  ): Promise<XResult<{
    characterId: string;
    configId: number;
    requestedQuantity: number;
    deductedQuantity: number;
    remainingQuantity: number;
    reason: string;
  }>> {
    // 1. 参数验证
    if (!characterId || !configId || quantity <= 0) {
      return XResultUtils.error('参数无效：characterId、configId和quantity必须有效', 'INVALID_PARAMETERS');
    }

    // 2. 获取角色背包数据
    const inventoryResult = await this.getInventory(characterId);
    if (XResultUtils.isFailure(inventoryResult)) {
      return XResultUtils.error(`获取角色背包失败: ${inventoryResult.message}`, inventoryResult.code);
    }

    // 3. 检查道具数量是否足够
    const currentQuantityResult = await this.getItemQuantity(characterId, configId);
    if (XResultUtils.isFailure(currentQuantityResult)) {
      return XResultUtils.error(`获取道具数量失败: ${currentQuantityResult.message}`, currentQuantityResult.code);
    }

    const availableQuantity = currentQuantityResult.data;
    if (!allowInsufficient && availableQuantity < quantity) {
      return XResultUtils.error(
        `道具数量不足，当前：${availableQuantity}，需要：${quantity}`,
        'INSUFFICIENT_ITEM_QUANTITY'
      );
    }

    // 4. 计算实际扣除数量
    const actualDeductQuantity = allowInsufficient ? Math.min(availableQuantity, quantity) : quantity;

    if (actualDeductQuantity <= 0) {
      return XResultUtils.error('没有可扣除的道具', 'NO_ITEM_TO_DEDUCT');
    }

    // 5. 执行道具扣除
    const deductResult = await this.inventoryRepository.deductItemByConfigId(characterId, configId, actualDeductQuantity);
    if (XResultUtils.isFailure(deductResult)) {
      return XResultUtils.error(`扣除道具失败: ${deductResult.message}`, deductResult.code || 'DEDUCT_ITEM_FAILED');
    }

    // 6. 计算剩余数量
    const remainingQuantity = availableQuantity - actualDeductQuantity;

    const result = {
      characterId,
      configId,
      requestedQuantity: quantity,
      deductedQuantity: actualDeductQuantity,
      remainingQuantity,
      reason,
    };

    this.logger.log(`扣除道具成功: ${characterId}, 道具${configId}: -${actualDeductQuantity}, 剩余: ${remainingQuantity}`);
    return XResultUtils.ok(result);
  }

  /**
   * 批量添加道具内部方法
   * 支持事务，确保所有道具都添加成功或全部失败
   * 基于hero-cultivation.service.ts中批量道具操作的业务需求
   *
   * @param characterId 角色ID
   * @param items 道具列表，每个道具数量为1
   * @param reason 操作原因
   * @returns 批量操作结果
   */
  async batchAddItemsInternal(
    characterId: string,
    items: Array<{ configId: number; quantity?: number }>,
    reason: string = '批量添加道具'
  ): Promise<XResult<{
    characterId: string;
    totalItems: number;
    addedItems: Array<{ configId: number; quantity: number; addedIds: string[] }>;
    totalSlotsUsed: number;
    reason: string;
  }>> {
    // 1. 参数验证
    if (!characterId || !items || items.length === 0) {
      return XResultUtils.error('参数无效：characterId和items必须有效', 'INVALID_PARAMETERS');
    }

    // 2. 使用事务确保数据一致性
    return this.inventoryRepository.withTransaction(async (session) => {
      const addedItems: Array<{ configId: number; quantity: number; addedIds: string[] }> = [];
      let totalSlotsUsed = 0;

      try {
        // 3. 逐个添加道具
        for (const item of items) {
          const quantity = item.quantity || 1; // 默认数量为1

          const addResult = await this.inventoryRepository.addItem(
            characterId,
            item.configId,
            quantity,
            undefined, // itemConfig
            session
          );

          if (XResultUtils.isFailure(addResult)) {
            throw new Error(`添加道具失败: 配置ID${item.configId}, ${addResult.message}`);
          }

          addedItems.push({
            configId: item.configId,
            quantity: quantity,
            addedIds: addResult.data.addedIds
          });

          totalSlotsUsed += addResult.data.slotsUsed;
        }

        const result = {
          characterId,
          totalItems: items.length,
          addedItems,
          totalSlotsUsed,
          reason,
        };

        this.logger.log(`批量添加道具成功: ${characterId}, 道具数量: ${items.length}, 使用槽位: ${totalSlotsUsed}`);
        return XResultUtils.ok(result);

      } catch (error) {
        this.logger.error(`批量添加道具失败: ${characterId}, ${error.message}`, error.stack);
        return XResultUtils.error(`批量添加道具失败: ${error.message}`, 'BATCH_ADD_ITEMS_FAILED');
      }
    });
  }

  /**
   * 批量扣除道具内部方法
   * 支持事务，如果任何一个道具扣除失败，则全部回滚
   * 基于hero-cultivation.service.ts中checkAndDeductItems的业务需求
   *
   * @param characterId 角色ID
   * @param items 道具列表，支持configId或itemId
   * @param reason 操作原因
   * @returns 批量操作结果
   */
  async batchDeductItemsInternal(
    characterId: string,
    items: Array<{
      configId?: number;
      itemId?: string;
      quantity?: number
    }>,
    reason: string = '批量扣除道具'
  ): Promise<XResult<{
    characterId: string;
    totalItems: number;
    deductedItems: Array<{
      configId?: number;
      itemId?: string;
      quantity: number;
      deductedQuantity: number
    }>;
    reason: string;
  }>> {
    // 1. 参数验证
    if (!characterId || !items || items.length === 0) {
      return XResultUtils.error('参数无效：characterId和items必须有效', 'INVALID_PARAMETERS');
    }

    // 2. 使用事务确保数据一致性
    return this.inventoryRepository.withTransaction(async (session) => {
      const deductedItems: Array<{
        configId?: number;
        itemId?: string;
        quantity: number;
        deductedQuantity: number
      }> = [];

      try {
        // 3. 先检查所有道具数量是否足够
        for (const item of items) {
          const quantity = item.quantity || 1; // 默认数量为1

          if (item.configId) {
            // 按配置ID检查
            const currentQuantityResult = await this.getItemQuantity(characterId, item.configId);
            if (XResultUtils.isFailure(currentQuantityResult)) {
              throw new Error(`获取道具数量失败: 配置ID${item.configId}, ${currentQuantityResult.message}`);
            }

            const availableQuantity = currentQuantityResult.data;
            if (availableQuantity < quantity) {
              throw new Error(`道具数量不足: 配置ID${item.configId}, 需要${quantity}, 当前${availableQuantity}`);
            }
          } else if (item.itemId) {
            // 按物品ID检查
            const inventoryResult = await this.getInventory(characterId);
            if (XResultUtils.isFailure(inventoryResult)) {
              throw new Error(`获取背包失败: ${inventoryResult.message}`);
            }

            const inventory = inventoryResult.data;
            const itemInstance = inventory.getItem(item.itemId);
            if (!itemInstance) {
              throw new Error(`道具不存在: 物品ID${item.itemId}`);
            }

            if (itemInstance.quantity < quantity) {
              throw new Error(`道具数量不足: 物品ID${item.itemId}, 需要${quantity}, 当前${itemInstance.quantity}`);
            }
          } else {
            throw new Error('必须提供configId或itemId');
          }
        }

        // 4. 所有检查通过后，执行扣除操作
        for (const item of items) {
          const quantity = item.quantity || 1;
          let deductResult;

          if (item.configId) {
            // 按配置ID扣除
            deductResult = await this.inventoryRepository.deductItemByConfigId(
              characterId,
              item.configId,
              quantity,
              session
            );
          } else if (item.itemId) {
            // 按物品ID扣除
            deductResult = await this.inventoryRepository.removeItem(
              characterId,
              item.itemId,
              quantity,
              session
            );
          }

          if (XResultUtils.isFailure(deductResult)) {
            throw new Error(`扣除道具失败: ${item.configId ? `配置ID${item.configId}` : `物品ID${item.itemId}`}, ${deductResult.message}`);
          }

          deductedItems.push({
            configId: item.configId,
            itemId: item.itemId,
            quantity: quantity,
            deductedQuantity: deductResult.data.deductedQuantity || deductResult.data.removedQuantity || quantity
          });
        }

        const result = {
          characterId,
          totalItems: items.length,
          deductedItems,
          reason,
        };

        this.logger.log(`批量扣除道具成功: ${characterId}, 道具数量: ${items.length}`);
        return XResultUtils.ok(result);

      } catch (error) {
        this.logger.error(`批量扣除道具失败: ${characterId}, ${error.message}`, error.stack);
        return XResultUtils.error(`批量扣除道具失败: ${error.message}`, 'BATCH_DEDUCT_ITEMS_FAILED');
      }
    });
  }

  // ========== 角色物品数据管理 ==========

  /**
   * 初始化角色背包数据
   * 为新角色创建默认的背包配置
   * 使用Result模式，无需try/catch包装
   */
  async initializeInventory(characterId: string): Promise<XResult<InventoryDocument>> {
    // 检查是否已存在
    const existingResult = await this.inventoryRepository.findById(characterId);
    if (XResultUtils.isSuccess(existingResult) && existingResult.data) {
      this.logger.log(`角色背包已存在: ${characterId}`);
      return existingResult;
    }

    // 创建新的背包数据
    const createResult = await this.inventoryRepository.initializeInventory(characterId);
    return this.handleRepositoryResult(createResult, '初始化角色背包失败');
  }

  /**
   * 获取角色背包数据
   * 返回完整的角色物品数据，不存在时自动初始化
   * 使用Result模式，无需try/catch包装
   */
  async getInventory(characterId: string): Promise<XResult<InventoryDocument>> {
    const result = await this.inventoryRepository.findById(characterId);

    if (XResultUtils.isFailure(result)) {
      // Repository层错误，直接传递
      return this.handleRepositoryResult(result, '获取角色背包数据失败');
    }

    if (!result.data) {
      // 如果不存在，自动初始化
      this.logger.log(`角色背包不存在，自动初始化: ${characterId}`);
      return this.initializeInventory(characterId);
    }

    return result;
  }

  /**
   * 获取角色背包列表（客户端格式）
   * 返回优化的客户端显示数据
   * 使用Result模式，无需try/catch包装
   */
  async getInventorySummaries(characterId: string): Promise<XResult<InventorySummary[]>> {
    const result = await this.inventoryRepository.getInventorySummaries(characterId);

    if (XResultUtils.isSuccess(result)) {
      this.logger.debug(`获取角色背包列表成功: ${characterId}, 页签数量: ${result.data.length}`);
    }

    return this.handleRepositoryResult(result, '获取角色背包列表失败');
  }

  // ========== 物品管理方法 ==========

  /**
   * 添加物品到角色背包
   * 支持自动堆叠和背包空间检查
   * 使用Result模式，无需try/catch包装
   */
  async addItem(
    characterId: string,
    configId: number,
    quantity: number,
    itemConfig?: any
  ): Promise<XResult<{ addedIds: string[]; slotsUsed: number }>> {
    // 参数验证
    if (!characterId || !configId || quantity <= 0) {
      return XResultUtils.error('参数无效：characterId、configId和quantity必须有效', 'INVALID_PARAMETERS');
    }

    const result = await this.inventoryRepository.addItem(characterId, configId, quantity, itemConfig);

    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`添加物品成功: 角色${characterId}, 物品${configId}, 数量${quantity}, 使用槽位${result.data.slotsUsed}`);
    }

    return this.handleRepositoryResult(result, '添加物品失败');
  }

  /**
   * 从角色背包移除物品
   * 支持部分移除和完全移除
   * @param characterId 角色ID
   * @param itemId 物品实例ID
   * @param quantity 移除数量
   */
  async removeItem(
    characterId: string,
    itemId: string,
    quantity: number
  ): Promise<XResult<{ removedQuantity: number }>> {
    // 参数验证
    if (!characterId || !itemId) {
      return XResultUtils.error('参数无效：characterId和itemId不能为空', 'INVALID_PARAMETERS');
    }

    if (quantity !== undefined && quantity <= 0) {
      return XResultUtils.error('移除数量必须大于0', 'INVALID_QUANTITY');
    }

    const result = await this.inventoryRepository.removeItem(characterId, itemId, quantity);

    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`移除物品成功: 角色${characterId}, 物品${itemId}, 移除数量${result.data.removedQuantity}`);
    }

    return this.handleRepositoryResult(result, '移除物品失败');
  }

  /**
   * 移动物品到指定页签和位置
   * 支持背包整理和物品重新排列
   * 使用Result模式，无需try/catch包装
   */
  async moveItem(
    characterId: string,
    itemId: string,
    tabId: number,
    slot?: number
  ): Promise<XResult<{ newSlot: number }>> {
    // 参数验证
    if (!characterId || !itemId || !tabId) {
      return XResultUtils.error('参数无效：characterId、itemId和tabId不能为空', 'INVALID_PARAMETERS');
    }

    const result = await this.inventoryRepository.moveItem(characterId, itemId, tabId, slot);

    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`移动物品成功: 角色${characterId}, 物品${itemId}, 页签${tabId}, 位置${result.data.newSlot}`);
    }

    return this.handleRepositoryResult(result, '移动物品失败');
  }

  /**
   * 批量添加物品
   * 优化的批量操作，减少数据库访问次数
   * 使用Result模式，无需try/catch包装
   */
  async batchAddItems(
    characterId: string,
    itemsToAdd: Array<{configId: number, quantity: number}>
  ): Promise<XResult<{ addedIds: string[] }>> {
    // 参数验证
    if (!characterId || !Array.isArray(itemsToAdd) || itemsToAdd.length === 0) {
      return XResultUtils.error('参数无效：characterId和itemsToAdd必须有效', 'INVALID_PARAMETERS');
    }

    // 验证每个物品数据
    for (const item of itemsToAdd) {
      if (!item.configId || item.quantity <= 0) {
        return XResultUtils.error('物品数据无效：configId和quantity必须有效', 'INVALID_ITEM_DATA');
      }
    }

    const result = await this.inventoryRepository.batchAddItems(characterId, itemsToAdd);

    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`批量添加物品成功: 角色${characterId}, 物品种类${itemsToAdd.length}, 实例数量${result.data.addedIds.length}`);
    }

    return this.handleRepositoryResult(result, '批量添加物品失败');
  }

  // ========== 查询和统计方法 ==========

  /**
   * 根据配置ID查询物品数量
   * 返回角色拥有的指定物品总数量
   * 使用Result模式，无需try/catch包装
   */
  async getItemQuantity(characterId: string, configId: number): Promise<XResult<number>> {
    // 参数验证
    if (!characterId || !configId) {
      return XResultUtils.error('参数无效：characterId和configId不能为空', 'INVALID_PARAMETERS');
    }

    const result = await this.inventoryRepository.getItemQuantity(characterId, configId);

    if (XResultUtils.isSuccess(result)) {
      this.logger.debug(`查询物品数量成功: 角色${characterId}, 物品${configId}, 数量${result.data}`);
    }

    return this.handleRepositoryResult(result, '查询物品数量失败');
  }

  /**
   * 检查物品是否充足
   * 验证角色是否拥有足够数量的指定物品
   * 使用Result模式，无需try/catch包装
   */
  async checkItemSufficient(characterId: string, configId: number, needQuantity: number): Promise<XResult<boolean>> {
    // 参数验证
    if (!characterId || !configId || needQuantity <= 0) {
      return XResultUtils.error('参数无效：characterId、configId和needQuantity必须有效', 'INVALID_PARAMETERS');
    }

    const result = await this.inventoryRepository.checkItemSufficient(characterId, configId, needQuantity);

    if (XResultUtils.isSuccess(result)) {
      this.logger.debug(`检查物品充足性: 角色${characterId}, 物品${configId}, 需要${needQuantity}, 充足${result.data}`);
    }

    return this.handleRepositoryResult(result, '检查物品充足性失败');
  }

  /**
   * 根据页签ID获取物品列表
   * 返回指定页签中的所有物品
   * 使用Result模式，无需try/catch包装
   */
  async getItemsByTab(characterId: string, tabId: number): Promise<XResult<Item[]>> {
    // 参数验证
    if (!characterId || !tabId) {
      return XResultUtils.error('参数无效：characterId和tabId不能为空', 'INVALID_PARAMETERS');
    }

    const result = await this.inventoryRepository.getItemsByTab(characterId, tabId);

    if (XResultUtils.isSuccess(result)) {
      this.logger.debug(`获取页签物品成功: 角色${characterId}, 页签${tabId}, 物品数量${result.data.length}`);
    }

    return this.handleRepositoryResult(result, '获取页签物品失败');
  }

  // ========== 背包管理方法 ==========

  /**
   * 扩展背包页签
   * 增加指定页签的容量，需要调用Economy服务扣除货币
   * 这是复杂业务操作，使用executeBusinessOperation进行完整监控
   */
  async expandTab(
    characterId: string,
    tabId: number,
    expandCount: number = 1
  ): Promise<XResult<{ cost: number; newCapacity: number }>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || !tabId || expandCount <= 0) {
        return XResultUtils.error('参数无效：characterId、tabId和expandCount必须有效', 'INVALID_PARAMETERS');
      }

      // 1. 先计算扩展费用（不实际扩展）
      const costResult = await this.inventoryRepository.calculateExpandCost(characterId, tabId, expandCount);
      if (XResultUtils.isFailure(costResult)) {
        return XResultUtils.error(costResult.message || '计算扩展费用失败', costResult.code || 'CALCULATE_COST_FAILED');
      }

      const expandCost = costResult.data;

      // 2. 调用Economy服务检查并扣除货币（如果有微服务客户端）
      if (this.microserviceClient) {
        const deductResult = await this.callMicroservice(
          MICROSERVICE_NAMES.ECONOMY_SERVICE,
          'currency.deduct',
          {
            characterId,
            currencyType: 'gold', // 假设使用金币扩展背包
            amount: expandCost,
            reason: 'expand_inventory_tab'
          }
        );

        if (XResultUtils.isFailure(deductResult)) {
          this.logger.warn(`扣除货币失败，无法扩展背包: 角色${characterId}, 费用${expandCost}`);
          return XResultUtils.error('货币不足，无法扩展背包', 'INSUFFICIENT_CURRENCY');
        }
      }

      // 3. 执行背包扩展
      const expandResult = await this.inventoryRepository.expandTab(characterId, tabId, expandCount);
      if (XResultUtils.isFailure(expandResult)) {
        // 如果扩展失败，需要回退货币扣除（这里简化处理，实际应该使用事务）
        this.logger.error(`背包扩展失败，但货币已扣除，需要人工处理: 角色${characterId}, 费用${expandCost}`);
        return expandResult;
      }

      this.logger.log(`扩展背包成功: 角色${characterId}, 页签${tabId}, 扩展${expandCount}次, 费用${expandCost}, 新容量${expandResult.data.newCapacity}`);
      return XResultUtils.ok(expandResult.data);
    }, { reason: 'expand_inventory_tab', metadata: { tabId, expandCount } });
  }

  /**
   * 背包排序
   * 按指定规则对页签中的物品进行排序
   * 使用Result模式，无需try/catch包装
   */
  async sortTab(
    characterId: string,
    tabId: number,
    sortType: string = 'slot'
  ): Promise<XResult<void>> {
    // 参数验证
    if (!characterId || !tabId) {
      return XResultUtils.error('参数无效：characterId和tabId不能为空', 'INVALID_PARAMETERS');
    }

    const validSortTypes = ['slot', 'configId', 'type', 'quantity', 'acquiredTime'];
    if (!validSortTypes.includes(sortType)) {
      return XResultUtils.error('排序类型无效，支持的类型：' + validSortTypes.join(', '), 'INVALID_SORT_TYPE');
    }

    const result = await this.inventoryRepository.sortTab(characterId, tabId, sortType);

    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`背包排序成功: 角色${characterId}, 页签${tabId}, 排序类型${sortType}`);
    }

    return this.handleRepositoryResult(result, '背包排序失败');
  }

  /**
   * 清理过期物品
   * 自动清理角色背包中的过期物品
   * 使用Result模式，无需try/catch包装
   */
  async cleanExpiredItems(characterId: string): Promise<XResult<{ cleanedCount: number }>> {
    // 参数验证
    if (!characterId) {
      return XResultUtils.error('参数无效：characterId不能为空', 'INVALID_PARAMETERS');
    }

    const result = await this.inventoryRepository.cleanExpiredItems(characterId);

    if (XResultUtils.isSuccess(result) && result.data.cleanedCount > 0) {
      this.logger.log(`清理过期物品成功: 角色${characterId}, 清理${result.data.cleanedCount}个物品`);
    }

    return this.handleRepositoryResult(result, '清理过期物品失败');
  }

  // ========== 物品使用方法 ==========

  /**
   * 使用物品
   * 处理物品使用逻辑，包括消耗品、礼包等
   * 这是复杂业务操作，可能需要调用多个微服务，使用executeBusinessOperation进行完整监控
   */
  async useItem(
    characterId: string,
    itemId: string,
    useCount: number = 1
  ): Promise<XResult<ItemUseResult>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || !itemId || useCount <= 0) {
        return XResultUtils.error('参数无效：characterId、itemId和useCount必须有效', 'INVALID_PARAMETERS');
      }

      // 1. 获取角色背包数据
      const inventoryResult = await this.getInventory(characterId);
      if (XResultUtils.isFailure(inventoryResult)) {
        return XResultUtils.error(inventoryResult.message || '获取角色背包失败', inventoryResult.code || 'GET_INVENTORY_FAILED');
      }

      const inventory = inventoryResult.data;
      const item = inventory.getItem(itemId);

      if (!item) {
        return XResultUtils.error('物品不存在', 'ITEM_NOT_FOUND');
      }

      if (item.quantity < useCount) {
        return XResultUtils.error('物品数量不足', 'INSUFFICIENT_ITEM_QUANTITY');
      }

      // 2. 处理物品使用逻辑（可能需要调用其他服务）
      const useResult = await this.processItemUse(characterId, item, useCount);
      if (XResultUtils.isFailure(useResult)) {
        return useResult;
      }

      // 3. 消耗物品
      const removeResult = await this.removeItem(characterId, itemId, useCount);
      if (XResultUtils.isFailure(removeResult)) {
        this.logger.error(`消耗物品失败，但使用逻辑已执行，需要回滚: 角色${characterId}, 物品${itemId}`);
        return XResultUtils.error('消耗物品失败', 'CONSUME_ITEM_FAILED');
      }

      this.logger.log(`使用物品成功: 角色${characterId}, 物品${itemId}, 使用数量${useCount}`);
      return XResultUtils.ok(useResult.data);
    }, { reason: 'use_item', metadata: { itemId, useCount } });
  }

  /**
   * 处理物品使用的具体逻辑
   * 根据物品类型调用不同的服务处理奖励
   * @private
   */
  private async processItemUse(
    characterId: string,
    item: Item,
    useCount: number
  ): Promise<XResult<ItemUseResult>> {
    // TODO: 集成libs/game-config获取物品配置
    // const itemConfig = await this.gameConfigService.getItemConfig(item.configId);

    // 简化版本：根据物品类型处理
    const useResult: ItemUseResult = {
      success: true,
      code: 0,
      message: '物品使用成功',
      rewards: [],
      consumedItems: [{
        itemId: item.itemId,
        configId: item.configId,
        quantity: useCount
      }],
      updatedCurrency: []
    };

    // 根据物品类型处理不同逻辑
    switch (item.type) {
      case 2: // 消耗品类型
        // 可能需要调用Activity服务触发相关活动
        await this.handleConsumableItem(characterId, item, useCount, useResult);
        break;
      case 3: // 礼包类型
        // 需要调用多个服务发放奖励
        await this.handleGiftPackItem(characterId, item, useCount, useResult);
        break;
      case 4: // 装备类型
        // 可能需要调用Hero服务更新英雄属性
        await this.handleEquipmentItem(characterId, item, useCount, useResult);
        break;
      default:
        // 默认处理
        this.logger.log(`使用普通物品: 类型${item.type}, 配置ID${item.configId}`);
        break;
    }

    return XResultUtils.ok(useResult);
  }

  /**
   * 处理消耗品使用
   * @private
   */
  private async handleConsumableItem(
    characterId: string,
    item: Item,
    useCount: number,
    useResult: ItemUseResult
  ): Promise<void> {
    try {
      // 调用Activity服务检查是否触发相关活动（如果有微服务客户端）
      if (this.microserviceClient) {
        const activityResult = await this.callMicroservice(
          MICROSERVICE_NAMES.ACTIVITY_SERVICE,
          'event.checkItemUse',
          { characterId, itemConfigId: item.configId, useCount }
        );

        if (XResultUtils.isSuccess(activityResult) && activityResult.data.triggered) {
          useResult.message += '，触发了相关活动';
          this.logger.log(`物品使用触发活动: 角色${characterId}, 物品${item.configId}`);
        }
      }
    } catch (error) {
      this.logger.warn(`检查物品使用活动失败，但不影响主流程: ${error.message}`);
    }
  }

  /**
   * 处理礼包物品使用
   * @private
   */
  private async handleGiftPackItem(
    characterId: string,
    item: Item,
    useCount: number,
    useResult: ItemUseResult
  ): Promise<void> {
    try {
      // 调用Economy服务处理礼包奖励（如果有微服务客户端）
      if (this.microserviceClient) {
        const rewardResult = await this.callMicroservice(
          MICROSERVICE_NAMES.ECONOMY_SERVICE,
          'reward.processGiftPack',
          { characterId, giftPackId: item.configId, quantity: useCount }
        );

        if (XResultUtils.isSuccess(rewardResult)) {
          useResult.rewards = rewardResult.data.rewards || [];
          useResult.updatedCurrency = rewardResult.data.currency || [];
          useResult.message = '礼包使用成功，奖励已发放';
          this.logger.log(`礼包奖励发放成功: 角色${characterId}, 礼包${item.configId}`);
        }
      }
    } catch (error) {
      this.logger.error(`处理礼包奖励失败: ${error.message}`);
      useResult.success = false;
      useResult.code = -1;
      useResult.message = '礼包处理失败';
    }
  }

  /**
   * 处理装备物品使用
   * @private
   */
  private async handleEquipmentItem(
    characterId: string,
    item: Item,
    useCount: number,
    useResult: ItemUseResult
  ): Promise<void> {
    try {
      // 调用Hero服务更新装备（如果有微服务客户端）
      if (this.microserviceClient) {
        const equipResult = await this.callMicroservice(
          MICROSERVICE_NAMES.HERO_SERVICE,
          'equipment.equip',
          { characterId, equipmentId: item.configId, quantity: useCount }
        );

        if (XResultUtils.isSuccess(equipResult)) {
          useResult.message = '装备使用成功';
          this.logger.log(`装备使用成功: 角色${characterId}, 装备${item.configId}`);
        }
      }
    } catch (error) {
      this.logger.warn(`处理装备使用失败，但不影响主流程: ${error.message}`);
    }
  }

  /**
   * 批量使用物品
   * 优化的批量使用操作
   * 这是复杂业务操作，使用executeBusinessOperation进行完整监控
   */
  async batchUseItems(
    characterId: string,
    itemsToUse: Array<{itemId: string, useCount: number}>
  ): Promise<XResult<{ results: ItemUseResult[] }>> {
    return this.executeBusinessOperation(async () => {
      // 参数验证
      if (!characterId || !Array.isArray(itemsToUse) || itemsToUse.length === 0) {
        return XResultUtils.error('参数无效：characterId和itemsToUse必须有效', 'INVALID_PARAMETERS');
      }

      // 验证每个物品数据
      for (const item of itemsToUse) {
        if (!item.itemId || item.useCount <= 0) {
          return XResultUtils.error('物品数据无效：itemId和useCount必须有效', 'INVALID_ITEM_DATA');
        }
      }

      // 批量处理物品使用
      const results: ItemUseResult[] = [];
      let successCount = 0;
      let failureCount = 0;

      for (const {itemId, useCount} of itemsToUse) {
        const useResult = await this.useItem(characterId, itemId, useCount);
        if (XResultUtils.isSuccess(useResult)) {
          results.push(useResult.data);
          successCount++;
        } else {
          // 记录失败但继续处理其他物品
          this.logger.warn(`批量使用物品中的单个物品失败: ${itemId}, 错误: ${useResult.message}`);
          results.push({
            success: false,
            code: -1,
            message: useResult.message || '物品使用失败',
            rewards: [],
            consumedItems: [],
            updatedCurrency: []
          });
          failureCount++;
        }
      }

      this.logger.log(`批量使用物品完成: 角色${characterId}, 总数${itemsToUse.length}, 成功${successCount}, 失败${failureCount}`);

      return XResultUtils.ok({
        results: results
      });
    }, { reason: 'batch_use_items', metadata: { itemCount: itemsToUse.length } });
  }
}
