/**
 * 战术计算器
 * 从TacticService中剥离的计算函数
 * 基于old项目的战术计算逻辑，无外部依赖
 */

/**
 * 战术效果类型定义
 */
export interface TacticEffect {
  type: number;
  value: number;
  description?: string;
}

/**
 * 战术效果计算器
 * 基于old项目的战术效果计算逻辑
 */
export class TacticEffectCalculator {
  /**
   * 计算基础效果
   * 基于old项目: Tactic配置表的AddType和AddValue字段
   */
  static calculateBaseEffect(config: any, level: number): TacticEffect | null {
    try {
      // 基于old项目：战术配置表包含AddType1-5和AddValue1-5字段
      // AddType定义效果类型，AddValue定义效果数值
      const effects: TacticEffect[] = [];

      for (let i = 1; i <= 5; i++) {
        const addType = config[`addType${i}`];
        const addValue = config[`addValue${i}`];

        if (addType && addValue) {
          effects.push({
            type: addType,
            value: addValue,
            description: this.getEffectDescription(addType, addValue)
          });
        }
      }

      if (effects.length === 0) {
        return null;
      }

      // 返回主要效果（第一个效果）
      return effects[0];
    } catch (error) {
      return null;
    }
  }

  /**
   * 计算等级加成
   * 基于old项目: 战术等级加成逻辑
   */
  static calculateLevelBonus(config: any, level: number): TacticEffect | null {
    try {
      // 基于old项目：高等级战术有额外加成
      if (level <= 1) {
        return null; // 1级没有额外加成
      }

      // 等级加成计算：每级增加基础效果的10%
      const baseEffect = this.calculateBaseEffect(config, 1);
      if (!baseEffect) {
        return null;
      }

      const levelMultiplier = 1 + (level - 1) * 0.1;
      const bonusValue = Math.floor(baseEffect.value * (levelMultiplier - 1));

      if (bonusValue <= 0) {
        return null;
      }

      return {
        type: baseEffect.type,
        value: bonusValue,
        description: `等级加成: +${bonusValue}`
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * 计算组合效果
   * 基于old项目: 多个战术的组合效果
   */
  static calculateComboEffects(activeTactics: Array<{ key: string; level: number; config: any }>): TacticEffect[] {
    try {
      const comboEffects: TacticEffect[] = [];

      // 基于old项目：特定战术组合有额外效果
      const tacticKeys = activeTactics.map(t => t.key);

      // 攻防组合：同时激活攻击和防守战术
      if (tacticKeys.includes('attack_boost') && tacticKeys.includes('defense_boost')) {
        comboEffects.push({
          type: 999, // 组合效果类型
          value: 10,
          description: '攻防平衡组合: 全属性+10'
        });
      }

      // 速度组合：同时激活多个速度相关战术
      const speedTactics = tacticKeys.filter(key => key.includes('speed'));
      if (speedTactics.length >= 2) {
        comboEffects.push({
          type: 1, // 速度类型
          value: speedTactics.length * 5,
          description: `速度组合: 速度+${speedTactics.length * 5}`
        });
      }

      return comboEffects;
    } catch (error) {
      return [];
    }
  }

  /**
   * 计算战术总效果
   * 合并基础效果、等级加成和组合效果
   */
  static calculateTotalEffect(
    baseEffect: TacticEffect | null,
    levelBonus: TacticEffect | null,
    comboEffects: TacticEffect[]
  ): TacticEffect | null {
    if (!baseEffect) {
      return null;
    }

    let totalValue = baseEffect.value;

    // 添加等级加成
    if (levelBonus && levelBonus.type === baseEffect.type) {
      totalValue += levelBonus.value;
    }

    // 添加组合效果
    for (const combo of comboEffects) {
      if (combo.type === baseEffect.type) {
        totalValue += combo.value;
      }
    }

    return {
      type: baseEffect.type,
      value: totalValue,
      description: `总效果: ${this.getEffectDescription(baseEffect.type, totalValue)}`
    };
  }

  /**
   * 获取效果描述
   * 基于效果类型返回描述文本
   */
  static getEffectDescription(effectType: number, value: number): string {
    const effectDescriptions = {
      1: `速度+${value}`,
      2: `射术+${value}`,
      3: `传球+${value}`,
      4: `防守+${value}`,
      5: `身体+${value}`,
      6: `盘带+${value}`,
      7: `门将+${value}`,
      10: `攻击力+${value}%`,
      11: `防守力+${value}%`,
      12: `体力消耗-${value}%`,
      999: `全属性+${value}`,
    };

    return effectDescriptions[effectType] || `未知效果+${value}`;
  }

  /**
   * 计算战术升级费用
   * 基于当前等级和目标等级
   */
  static calculateUpgradeCost(currentLevel: number, targetLevel: number): number {
    let totalCost = 0;
    
    for (let level = currentLevel; level < targetLevel; level++) {
      // 基础费用随等级递增
      const baseCost = 1000;
      const levelMultiplier = Math.pow(1.5, level);
      const levelCost = Math.floor(baseCost * levelMultiplier);
      
      totalCost += levelCost;
    }

    return totalCost;
  }

  /**
   * 计算战术激活条件
   * 基于角色等级和其他条件
   */
  static calculateActivationRequirements(tacticConfig: any, characterLevel: number): {
    canActivate: boolean;
    requirements: string[];
    missingRequirements: string[];
  } {
    const requirements: string[] = [];
    const missingRequirements: string[] = [];

    // 等级要求
    const requiredLevel = tacticConfig.requiredLevel || 1;
    requirements.push(`角色等级${requiredLevel}级`);
    if (characterLevel < requiredLevel) {
      missingRequirements.push(`角色等级不足（当前${characterLevel}级，需要${requiredLevel}级）`);
    }

    // 前置战术要求
    if (tacticConfig.prerequisiteTactics && tacticConfig.prerequisiteTactics.length > 0) {
      for (const prereq of tacticConfig.prerequisiteTactics) {
        requirements.push(`前置战术: ${prereq}`);
        // 这里需要检查是否已激活前置战术，暂时跳过
      }
    }

    return {
      canActivate: missingRequirements.length === 0,
      requirements,
      missingRequirements
    };
  }
}
