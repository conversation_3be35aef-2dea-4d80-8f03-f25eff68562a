/**
 * 阵容计算器
 * 从FormationService中剥离的计算函数
 * 基于old项目的计算逻辑，合并了配置依赖和纯计算函数
 */

import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 教练属性加成计算器
 * 基于old项目: calcTrainerPropertyAttr方法
 */
export class CoachCalculator {
  /**
   * 计算教练属性加成
   * 基于old项目: calcTrainerPropertyAttr方法
   */
  static calcTrainerPropertyAttr(
    sum: { [key: string]: number },
    trainerAttr: { [key: string]: number },
    teamType: number
  ): void {
    // 根据教练位置类型计算加成（基于old项目逻辑）
    const bonusRate = this.getTrainerBonusRate(teamType);
    
    for (const [attrName, attrValue] of Object.entries(trainerAttr)) {
      sum[attrName] = (sum[attrName] || 0) + Math.floor(attrValue * bonusRate);
    }
  }

  /**
   * 获取教练加成比率
   * 基于old项目的教练位置加成逻辑
   */
  static getTrainerBonusRate(teamType: number): number {
    switch (teamType) {
      case 1: return 0.1;  // 主教练 10%
      case 2: return 0.05; // 进攻教练 5%
      case 3: return 0.05; // 防守教练 5%
      case 4: return 0.03; // 体能教练 3%
      default: return 0;
    }
  }

  /**
   * 计算教练战术加成
   * 基于old项目: calcTrainerTacticsAttr方法
   */
  static calcTrainerTacticsAttr(trainer: any, teamType: number): { attack: number; defend: number } {
    const tacticsBonus = { attack: 0, defend: 0 };

    switch (teamType) {
      case 2: // 进攻教练
        if (trainer.tactics && trainer.tactics.length > 0) {
          const attackTactics = trainer.tactics[0];
          if (attackTactics.type === 1) { // 进攻战术
            tacticsBonus.attack += attackTactics.level || 0;
          }
        }
        break;
      case 3: // 防守教练
        if (trainer.tactics && trainer.tactics.length > 1) {
          const defendTactics = trainer.tactics[1];
          if (defendTactics.type === 2) { // 防守战术
            tacticsBonus.defend += defendTactics.level || 0;
          }
        }
        break;
      default:
        break;
    }

    return tacticsBonus;
  }
}

/**
 * 战术计算器
 * 基于old项目的战术加成计算逻辑
 */
export class TacticsCalculator {
  /**
   * 计算战术加成
   * 基于old项目：从addType和addValue中计算具体的加成值
   */
  static calculateTacticBonus(tacticsConfig: any, bonusType: 'attack' | 'defend'): number {
    let bonus = 0;
    
    // 遍历所有addType和addValue字段
    for (let i = 1; i <= 5; i++) {
      const addType = tacticsConfig[`addType${i}`];
      const addValue = tacticsConfig[`addValue${i}`];
      
      if (!addType || !addValue) continue;
      
      // 根据addType判断是否为对应的加成类型
      // 这里需要根据old项目的具体逻辑来映射addType到加成类型
      if (this.isMatchingBonusType(addType, bonusType)) {
        bonus += addValue;
      }
    }
    
    return bonus;
  }

  /**
   * 判断addType是否匹配指定的加成类型
   * 基于old项目的addType定义
   */
  static isMatchingBonusType(addType: number, bonusType: 'attack' | 'defend'): boolean {
    // 基于old项目的addType映射逻辑
    // 这里需要根据实际的addType定义来映射
    if (bonusType === 'attack') {
      // 攻击相关的addType（需要根据old项目确认具体数值）
      return [1, 2, 3, 4, 5].includes(addType);
    } else if (bonusType === 'defend') {
      // 防守相关的addType（需要根据old项目确认具体数值）
      return [6, 7, 8, 9, 10].includes(addType);
    }
    
    return false;
  }
}

/**
 * 技能计算器
 * 基于old项目的技能加成计算逻辑
 */
export class SkillCalculator {
  /**
   * 计算教练技能加成
   * 基于old项目：从addType和addValue中计算属性加成
   */
  static calculateCoachSkillBonus(skillConfig: any): { [attributeName: string]: number } {
    const bonus: { [attributeName: string]: number } = {};
    
    // 遍历所有addType和addValue字段
    for (let i = 1; i <= 18; i++) {
      const addType = skillConfig[`addType${i}`];
      const addValue = skillConfig[`addValue${i}`];
      
      if (!addType || !addValue) continue;
      
      // 根据addType映射到具体的属性名
      const attributeName = this.mapAddTypeToAttribute(addType);
      if (attributeName) {
        bonus[attributeName] = (bonus[attributeName] || 0) + addValue;
      }
    }
    
    return bonus;
  }

  /**
   * 将addType映射到属性名
   * 基于old项目的addType定义
   */
  static mapAddTypeToAttribute(addType: number): string | null {
    // 基于old项目的addType映射表
    const typeToAttributeMap: { [key: number]: string } = {
      1: 'speed',        // 速度
      2: 'shooting',     // 射术
      3: 'passing',      // 传球
      4: 'defending',    // 防守
      5: 'physical',     // 身体
      6: 'dribbling',    // 盘带
      7: 'finishing',    // 射门
      8: 'longPassing',  // 长传
      9: 'standingTackle', // 抢断
      10: 'slidingTackle', // 铲球
      11: 'save',        // 扑救
      // 更多映射根据old项目的实际定义添加
    };
    
    return typeToAttributeMap[addType] || null;
  }
}
