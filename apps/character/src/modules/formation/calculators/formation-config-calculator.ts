/**
 * 阵容配置计算器
 * 从FormationService中剥离的配置依赖计算函数
 * 基于old项目的计算逻辑，依赖游戏配置但不依赖业务服务
 */

import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { GameConfigFacade } from '@libs/game-config';

/**
 * 位置系数计算器
 * 基于old项目的位置系数获取逻辑
 */
export class PositionRateCalculator {
  constructor(private readonly gameConfig: GameConfigFacade) {}

  /**
   * 获取位置进攻系数
   * 基于old项目: getAttackRate方法
   */
  async getAttackRate(formationResId: number, position: string): Promise<XResult<number>> {
    try {
      // 获取阵型配置
      const formationConfig = await this.gameConfig.teamFormation?.get(formationResId);
      if (!formationConfig) {
        return XResultUtils.error(`阵型配置不存在: ${formationResId}`, 'FORMATION_CONFIG_NOT_FOUND');
      }

      // 基于old项目：根据位置获取进攻系数
      const positionId = this.getPositionId(position);
      const attackRateField = `attackArg${positionId}` as keyof typeof formationConfig;
      const attackRate = formationConfig[attackRateField] as number || 1000;
      return XResultUtils.ok(attackRate);
    } catch (error) {
      return XResultUtils.error(`获取位置进攻系数失败: ${error.message}`, 'GET_ATTACK_RATE_ERROR');
    }
  }

  /**
   * 获取位置防守系数
   * 基于old项目: getDefendRate方法
   */
  async getDefendRate(formationResId: number, position: string): Promise<XResult<number>> {
    try {
      // 获取阵型配置
      const formationConfig = await this.gameConfig.teamFormation?.get(formationResId);
      if (!formationConfig) {
        return XResultUtils.error(`阵型配置不存在: ${formationResId}`, 'FORMATION_CONFIG_NOT_FOUND');
      }

      // 基于old项目：根据位置获取防守系数
      const positionId = this.getPositionId(position);
      const defendRateField = `defendArg${positionId}` as keyof typeof formationConfig;
      const defendRate = formationConfig[defendRateField] as number || 1000;
      return XResultUtils.ok(defendRate);
    } catch (error) {
      return XResultUtils.error(`获取位置防守系数失败: ${error.message}`, 'GET_DEFEND_RATE_ERROR');
    }
  }

  /**
   * 获取位置ID
   * 基于old项目: TEAM_FORMATION_CONFIG_POSITION_TO_ID映射
   */
  private getPositionId(position: string): number {
    const positionMapping = {
      'GK': 1,   // 门将
      'DL': 2,   // 左后卫
      'DC': 3,   // 中后卫
      'DR': 4,   // 右后卫
      'ML': 5,   // 左中场
      'MC': 6,   // 中场
      'MR': 7,   // 右中场
      'WL': 8,   // 左边锋
      'ST': 9,   // 前锋
      'WR': 10,  // 右边锋
      'AM': 11,  // 前腰
      'DM': 12,  // 后腰
    };

    return positionMapping[position] || 1;
  }
}

/**
 * 阵容属性计算器
 * 基于old项目的阵容属性计算逻辑
 */
export class FormationAttributeCalculator {
  /**
   * 计算阵容基础进攻值
   * 基于old项目的公式：
   * 上场球员1球员_进攻值当前值*上场球员1阵型位置进攻系数/1000
   * +上场球员2球员_进攻值当前值*上场球员2阵型位置进攻系数/1000
   * +...
   * +上场球员11球员_进攻值当前值*上场球员11阵型位置进攻系数/1000
   * +经理等级*10
   */
  static calculateTeamBaseAttack(
    heroesData: Array<{ heroId: string; position: string; attackValue: number }>,
    positionRates: { [position: string]: number },
    managerLevel: number = 1
  ): number {
    let totalAttack = 0;

    // 累加每个球员的进攻值 * 位置系数
    for (const hero of heroesData) {
      const attackRate = positionRates[hero.position] || 1000;
      const heroAttackContribution = (hero.attackValue * attackRate) / 1000;
      totalAttack += heroAttackContribution;
    }

    // 加上经理等级加成
    const managerBonus = managerLevel * 10;
    totalAttack += managerBonus;

    return Math.round(totalAttack);
  }

  /**
   * 计算阵容基础防守值
   * 基于old项目的公式：
   * 上场球员1球员_防守值当前值*上场球员1阵型位置防守系数/1000
   * +上场球员2球员_防守值当前值*上场球员2阵型位置防守系数/1000
   * +...
   * +经理等级*8
   */
  static calculateTeamBaseDefend(
    heroesData: Array<{ heroId: string; position: string; defendValue: number }>,
    positionRates: { [position: string]: number },
    managerLevel: number = 1
  ): number {
    let totalDefend = 0;

    // 累加每个球员的防守值 * 位置系数
    for (const hero of heroesData) {
      const defendRate = positionRates[hero.position] || 1000;
      const heroDefendContribution = (hero.defendValue * defendRate) / 1000;
      totalDefend += heroDefendContribution;
    }

    // 加上经理等级加成
    const managerBonus = managerLevel * 8;
    totalDefend += managerBonus;

    return Math.round(totalDefend);
  }

  /**
   * 计算实际战力
   * 基于old项目: calcTotalRating方法
   */
  static calculateTotalRating(
    heroesData: Array<{ heroId: string; position: string; rating: number }>
  ): number {
    let totalRating = 0;

    // 遍历每个位置的球员，累加实力值
    for (const hero of heroesData) {
      totalRating += hero.rating || 0;
    }

    return Math.round(totalRating);
  }

  /**
   * 计算球队身价
   * 基于old项目: calcTeamValue方法
   */
  static calculateTeamValue(
    heroesData: Array<{ heroId: string; marketValue: number }>
  ): number {
    let totalValue = 0;

    // 遍历每个位置的球员，累加身价
    for (const hero of heroesData) {
      totalValue += hero.marketValue || 0;
    }

    return totalValue;
  }
}

/**
 * 战术和教练加成计算器
 * 基于old项目的加成计算逻辑
 */
export class BonusCalculator {
  /**
   * 计算战术加成
   * 基于old项目逻辑
   */
  static calculateTacticsBonus(formation: any): { attack: number; defend: number } {
    const tacticsBonus = { attack: 0, defend: 0 };

    // 获取阵容使用的战术配置
    const useTactics = formation.useTactics;
    const useDefTactics = formation.useDefTactics;

    // 这里需要根据实际的战术配置来计算加成
    // 暂时返回基础值，实际实现需要根据配置表计算

    return tacticsBonus;
  }

  /**
   * 计算教练加成
   * 基于old项目逻辑
   */
  static calculateCoachBonus(formation: any): { 
    attack: number; 
    defend: number; 
    speed: number; 
    passing: number; 
  } {
    const coachBonus = { attack: 0, defend: 0, speed: 0, passing: 0 };

    // 遍历阵容中的所有教练
    for (const trainer of formation.trainers || []) {
      if (!trainer.uid || trainer.uid === '') {
        continue;
      }

      // 这里需要根据教练数据计算加成
      // 暂时返回基础值，实际实现需要获取教练数据
    }

    return coachBonus;
  }
}
