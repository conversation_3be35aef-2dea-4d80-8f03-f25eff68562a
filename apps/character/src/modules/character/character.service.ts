import { Injectable, forwardRef, Inject } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import { Character, CharacterDocument } from '@character/common/schemas/character.schema';
import { CharacterRepository } from '@character/common/repositories/character.repository';
import { FormationService } from '@character/modules/formation/formation.service';
import {
  CreateCharacterDto,
  UpdateCharacterDto,
  LoginCharacterDto,
  CurrencyOperationDto,
  BuyEnergyDto,
  LevelUpDto,
  CharacterInfoDto,
  LoginResultDto,
  LevelUpResultDto,
  BuyEnergyResultDto,
  GetCharacterListDto
} from '@character/common/dto/character.dto';
import {
  CreateCharacterPayloadDto,
  LoginCharacterPayloadDto,
  LogoutCharacterPayloadDto,
  GetCharacterInfoPayloadDto,
  UpdateCharacterPayloadDto,
  GetCharacterListPayloadDto,
  AddCurrencyPayloadDto,
  SubtractCurrencyPayloadDto,
  BuyEnergyPayloadDto,
  LevelUpPayloadDto,
  CompleteStepPayloadDto,
  FinishGuidePayloadDto,
  SetBeliefPayloadDto,
  UseRedeemCodePayloadDto,
  UpdateBuffPayloadDto,
  SearchByNamePayloadDto,
  GetPersonInfoPayloadDto,
  CreateRolePayloadDto,
  ModifyCharacterNamePayloadDto,
  AddResourcePayloadDto,
  GetEnergyRewardPayloadDto,
  CostCashTaskPayloadDto,
  DeductResourcePayloadDto,
  GetScoutDataPayloadDto,
  UpdateScoutDataPayloadDto,
  InitializeFromAuthPayloadDto
} from '@character/common/dto/character-payload.dto';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { GAME_CONSTANTS } from '@libs/game-constants';
import { GameConfigFacade } from '@libs/game-config';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 角色业务服务层
 * 继承BaseService，提供统一的业务逻辑处理和Result模式集成
 *
 * 🎯 核心功能：
 * - 角色生命周期管理（创建、登录、登出）
 * - 角色属性和资源管理
 * - 角色升级和成长系统
 * - 货币系统集成
 * - 新手引导和进度管理
 * - 球探系统集成
 * - old项目兼容性API
 *
 * 🚀 优化特性：
 * - 完整的Result模式错误处理
 * - 统一的业务逻辑验证
 * - 自动的性能监控和日志记录
 * - 标准化的微服务调用
 * - 业务规则验证框架
 * - 操作追踪和审计日志
 */
@Injectable()
export class CharacterService extends BaseService {
  constructor(
    private readonly characterRepository: CharacterRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient: MicroserviceClientService,
    @Inject(forwardRef(() => FormationService))
    private readonly formationService: FormationService,
  ) {
    super('CharacterService', microserviceClient);
  }

  /**
   * 创建新角色
   * 使用BaseService的业务操作框架，应用Result模式最佳实践
   * 这是复杂业务操作，使用executeBusinessOperation进行完整监控
   */
  async createCharacter(dto: CreateCharacterPayloadDto): Promise<XResult<CharacterDocument>> {
    return this.executeBusinessOperation(async () => {
      // 1. 检查角色名是否已存在
      const nameExistsResult = await this.characterRepository.existsByName(
        dto.name,
        dto.serverId
      );

      if (XResultUtils.isFailure(nameExistsResult)) {
        return XResultUtils.error(nameExistsResult.message || '检查角色名失败', nameExistsResult.code || 'CHECK_NAME_FAILED');
      }

      if (nameExistsResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN], ErrorCode.CHARACTER_NAME_TAKEN);
      }

      // 2. 创建角色数据
      const characterData = {
        characterId: dto.characterId,
        userId: dto.userId,
        serverId: dto.serverId,
        openId: dto.openId,
        name: dto.name,
        avatar: dto.avatar || '',
        faceIcon: dto.faceIcon || GAME_CONSTANTS.CHARACTER.DEFAULT_FACE_ICON,
        cash: GAME_CONSTANTS.CHARACTER.INITIAL_CASH,
        gold: GAME_CONSTANTS.CHARACTER.INITIAL_GOLD,
        energy: GAME_CONSTANTS.CHARACTER.INITIAL_ENERGY,
        loginInfo: {
          createTime: Date.now(),
          loginTime: Date.now(),
        },
      };

      // 3. 保存角色到数据库
      const createResult = await this.characterRepository.create(characterData);
      if (XResultUtils.isFailure(createResult)) {
        return XResultUtils.error(createResult.message || '创建角色失败', createResult.code || 'CREATE_CHARACTER_FAILED');
      }

      this.logger.log(`角色创建成功: ${dto.characterId}, 用户: ${dto.userId}`);
      return XResultUtils.ok(createResult.data);
    }, { reason: 'create_character', metadata: { userId: dto.userId, name: dto.name } });
  }

  /**
   * 从Auth服务初始化角色数据（新架构）
   * 这是Auth服务创建角色后调用的初始化方法
   * 使用BaseService的业务操作框架，应用Result模式最佳实践
   */
  async initializeFromAuth(dto: InitializeFromAuthPayloadDto): Promise<XResult<CharacterDocument>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`🎮 从Auth服务初始化角色数据: ${dto.characterId}`);

      // 1. 检查角色是否已经存在
      const existingResult = await this.characterRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(existingResult)) {
        return XResultUtils.error(existingResult.message || '检查角色存在性失败', existingResult.code || 'CHECK_CHARACTER_FAILED');
      }

      if (existingResult.data) {
        this.logger.log(`角色已存在，跳过初始化: ${dto.characterId}`);
        return XResultUtils.ok(existingResult.data);
      }

      // 2. 创建角色游戏数据
      const characterData = {
        characterId: dto.characterId,
        userId: dto.userId,
        serverId: dto.serverId,
        openId: dto.initialData?.openId || 'default_openid',
        name: dto.characterName,
        avatar: dto.initialData?.avatar || 'default_avatar.png',
        faceIcon: dto.initialData?.faceIcon || GAME_CONSTANTS.CHARACTER.DEFAULT_FACE_ICON,
        cash: GAME_CONSTANTS.CHARACTER.INITIAL_CASH,
        gold: GAME_CONSTANTS.CHARACTER.INITIAL_GOLD,
        energy: GAME_CONSTANTS.CHARACTER.INITIAL_ENERGY,
        loginInfo: {
          createTime: Date.now(),
          loginTime: Date.now(),
        },
      };

      // 3. 保存角色到数据库
      const createResult = await this.characterRepository.create(characterData);
      if (XResultUtils.isFailure(createResult)) {
        return XResultUtils.error(createResult.message || '创建角色游戏数据失败', createResult.code || 'CREATE_GAME_DATA_FAILED');
      }

      this.logger.log(`✅ 角色游戏数据初始化成功: ${dto.characterId}`);
      return XResultUtils.ok(createResult.data);
    }, { reason: 'initialize_from_auth', metadata: { characterId: dto.characterId, userId: dto.userId } });
  }

  /**
   * 角色登录（增强版 - 支持数据初始化检查）
   * 使用BaseService的业务操作框架，应用Result模式最佳实践
   * 这是复杂业务操作，使用executeBusinessOperation进行完整监控
   */
  async loginCharacter(dto: LoginCharacterPayloadDto): Promise<XResult<LoginResultDto>> {
    return this.executeBusinessOperation(async () => {
      // 1. 查找角色
      let characterResult = await this.characterRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(characterResult.message || '查找角色失败', characterResult.code || 'FIND_CHARACTER_FAILED');
      }

      let character = characterResult.data;

      // 2. 如果角色在Character服务中不存在，但Auth服务已验证角色存在，则需要初始化
      if (!character) {
        this.logger.log(`🔄 角色在Character服务中不存在，执行初始化: ${dto.characterId}`);

        const initResult = await this.initializeFromAuth({
          characterId: dto.characterId,
          userId: dto.userId,
          serverId: dto.serverId,
          characterName: `Player_${dto.characterId.slice(-8)}`, // 使用角色ID后8位作为默认名称
          initialData: {
            level: 1,
            avatar: 'default_avatar.png',
            faceIcon: 1,
          },
        });

        if (XResultUtils.isFailure(initResult)) {
          return XResultUtils.error(initResult.message || '角色初始化失败', initResult.code || 'INITIALIZE_CHARACTER_FAILED');
        }

        character = initResult.data;
      }

      // 3. 验证角色归属
      if (character.userId !== dto.userId || character.serverId !== dto.serverId) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }

      // 4. 更新登录信息
      const updateData = {
        'loginInfo.loginTime': Date.now(),
        'loginInfo.ip': '',
        'loginInfo.frontendId': dto.frontendId,
        'loginInfo.sessionId': dto.sessionId,
      };

      const now = Date.now();
      const lastLogin = character.loginInfo.loginTime;
      const oneDayMs = 24 * 60 * 60 * 1000;

      if (now - lastLogin > oneDayMs) {
        updateData['gameProgress.activeDay'] = character.gameProgress.activeDay + 1;

        // 检查是否连续登录
        if (now - lastLogin > 2 * oneDayMs) {
          updateData['gameProgress.recentActiveDay'] = 1;
        } else {
          updateData['gameProgress.recentActiveDay'] = character.gameProgress.recentActiveDay + 1;
        }
      }

      // 5. 保存更新
      const updateResult = await this.characterRepository.update(character.characterId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(updateResult.message || '更新登录信息失败', updateResult.code || 'UPDATE_LOGIN_INFO_FAILED');
      }

      const updatedCharacter = updateResult.data;

      // 6. 构建登录结果（删除冗余的success字段）
      const result: LoginResultDto = {
        character: this.toCharacterInfoDto(updatedCharacter),
        isNewCharacter: updatedCharacter.gameProgress.isNewer,
        initData: {
          needCreateTeam: updatedCharacter.gameProgress.createRoleStep < 2,
          needGuide: updatedCharacter.gameProgress.isNewer,
          hasInitHeroes: updatedCharacter.gameProgress.createRoleStep >= 1,
        },
      };

      this.logger.log(`角色登录成功: ${character.characterId}`);
      return XResultUtils.ok(result);
    }, { reason: 'login_character', metadata: { characterId: dto.characterId, userId: dto.userId } });
  }

  /**
   * 角色登出
   * 使用Result模式，无需try/catch包装
   */
  async logoutCharacter(dto: LogoutCharacterPayloadDto): Promise<XResult<void>> {
    // 查找角色
    const characterResult = await this.characterRepository.findById(dto.characterId);
    const character = this.handleRepositoryResult(characterResult, '查找角色失败');
    if (XResultUtils.isFailure(character)) {
      return character;
    }

    if (character.data) {
      // 更新登出信息
      const updateResult = await this.characterRepository.update(dto.characterId, {
        'loginInfo.leaveTime': Date.now(),
        'loginInfo.frontendId': null,
        'loginInfo.sessionId': null,
      });

      const updateHandled = this.handleRepositoryResult(updateResult, '更新登出信息失败');
      if (XResultUtils.isFailure(updateHandled)) {
        return updateHandled;
      }
    }

    this.logger.log(`角色登出: ${dto.characterId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 获取角色信息
   * 使用Result模式，无需try/catch包装
   */
  async getCharacterInfo(characterId: string): Promise<XResult<CharacterInfoDto>> {
    // 查找角色
    const characterResult = await this.characterRepository.findById(characterId);
    const character = this.handleRepositoryResult(characterResult, '查找角色失败');
    if (XResultUtils.isFailure(character)) {
      return character;
    }

    if (!character.data) {
      return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
    }

    const characterInfo = this.toCharacterInfoDto(character.data);
    return XResultUtils.ok(characterInfo);
  }

  /**
   * 更新角色信息
   * 使用BaseService的业务操作框架，应用Result模式最佳实践
   * 这是复杂业务操作，使用executeBusinessOperation进行完整监控
   *
   * 🔧 类型安全优化：
   * - 使用extractSafeFields确保只更新允许的字段
   * - 防止DTO中的载荷字段污染数据库
   * - 提供完整的字段验证和转换
   */
  async updateCharacter(dto: UpdateCharacterPayloadDto): Promise<XResult<CharacterDocument>> {
    return this.executeBusinessOperation(async () => {
      // 1. 查找角色
      const characterResult = await this.characterRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(characterResult.message || '查找角色失败', characterResult.code || 'FIND_CHARACTER_FAILED');
      }

      if (!characterResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }

      const character = characterResult.data;

      // 2. 如果更新名称，检查是否重复
      if (dto.name && dto.name !== character.name) {
        const nameExistsResult = await this.characterRepository.existsByName(
          dto.name,
          character.serverId,
          dto.characterId
        );

        if (XResultUtils.isFailure(nameExistsResult)) {
          return XResultUtils.error(nameExistsResult.message || '检查角色名失败', nameExistsResult.code || 'CHECK_NAME_FAILED');
        }

        if (nameExistsResult.data) {
          return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN], ErrorCode.CHARACTER_NAME_TAKEN);
        }
      }

      // 3. 类型安全的字段提取（简化版）
      const updateData = this.extractSafeFields(dto, ['name', 'faceIcon', 'level']);

      // 4. 检查是否有有效的更新数据
      if (Object.keys(updateData).length === 0) {
        return XResultUtils.error('没有有效的更新数据', 'NO_VALID_UPDATE_DATA');
      }

      // 5. 执行数据库更新
      const updateResult = await this.characterRepository.update(dto.characterId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        this.logger.error('更新角色信息失败', updateResult.message);
      }

      this.logger.log(`角色信息更新成功: ${dto.characterId}`, {
        updatedFields: Object.keys(updateData),
        characterId: dto.characterId
      });

      return updateResult;
    }, { reason: 'update_character', metadata: { characterId: dto.characterId } });
  }

  /**
   * 获取角色列表
   * // TODO 应该调用auth服务获取，评估存在的必要性
   * 使用Result模式，无需try/catch包装
   */
  async getCharacterList(dto: GetCharacterListDto): Promise<XResult<{
    list: CharacterInfoDto[];
    total: number;
    page: number;
    limit: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }>> {
    // 构建查询过滤器
    const filter: any = {};
    if (dto.userId) {
      filter.userId = dto.userId;
    }
    if (dto.serverId) {
      filter.serverId = dto.serverId;
    }

    // 查询角色列表
    const result = await this.characterRepository.findWithPagination(
      filter,
      dto.page || 1,
      dto.limit || 20
    );

    const paginationResult = this.handleRepositoryResult(result, '获取角色列表失败');
    if (XResultUtils.isFailure(paginationResult)) {
      return paginationResult;
    }

    const response = {
      list: paginationResult.data.data.map(char => this.toCharacterInfoDto(char)),
      total: paginationResult.data.total,
      page: paginationResult.data.page,
      limit: paginationResult.data.limit,
      pages: paginationResult.data.pages,
      hasNext: paginationResult.data.hasNext,
      hasPrev: paginationResult.data.hasPrev,
    };

    return XResultUtils.ok(response);
  }

  // ========== 角色验证工具方法 ==========

  /**
   * 验证并获取角色（通用方法）
   * 消除重复的角色存在性验证代码
   *
   * @param characterId 角色ID
   * @param repository 角色Repository实例
   * @param errorContext 错误上下文描述
   * @returns 角色文档或错误结果
   */
  protected async validateAndGetCharacter<T = CharacterDocument>(
    characterId: string,
    repository: { findById: (id: string) => Promise<XResult<T | null>> },
    errorContext: string = '操作'
  ): Promise<XResult<T>> {
    if (!characterId) {
      return XResultUtils.error('角色ID不能为空', 'INVALID_CHARACTER_ID');
    }

    // 查找角色
    const characterResult = await repository.findById(characterId);
    if (XResultUtils.isFailure(characterResult)) {
      this.logger.error(`${errorContext} - 查找角色失败: ${characterResult.message}`, { characterId });
      return XResultUtils.error(
        characterResult.message || `${errorContext} - 查找角色失败`,
        characterResult.code || 'FIND_CHARACTER_FAILED'
      );
    }

    // 检查角色是否存在
    if (!characterResult.data) {
      this.logger.warn(`${errorContext} - 角色不存在: ${characterId}`);
      return XResultUtils.error(`${errorContext} - 角色不存在`, 'CHARACTER_NOT_FOUND');
    }

    return XResultUtils.ok(characterResult.data);
  }

  // ========== 内部货币操作方法（通用） ==========

  /**
   * 内部货币操作方法 - 增加货币
   * 供其他业务方法内部调用，无需DTO包装
   *
   * @param characterId 角色ID
   * @param currencyType 货币类型
   * @param amount 数量
   * @param reason 操作原因
   * @returns 操作结果
   */
  async addCurrencyInternal(
    characterId: string,
    currencyType: string,
    amount: number,
    reason: string = '增加货币'
  ): Promise<XResult<{
    characterId: string;
    currencyType: string;
    amount: number;
    newBalance: number;
    reason: string;
  }>> {
    // 1. 验证并获取角色
    const characterResult = await this.validateAndGetCharacter(
      characterId,
      this.characterRepository,
      '增加货币'
    );
    if (XResultUtils.isFailure(characterResult)) {
      return characterResult;
    }

    const character = characterResult.data;

    // 2. 计算新余额
    const currentBalance = character[currencyType] || 0;
    const newBalance = currentBalance + amount;

    const updateData: any = {};
    updateData[currencyType] = newBalance;

    // 3. 更新角色数据
    const updateResult = await this.characterRepository.update(characterId, updateData);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(updateResult.message || '更新货币失败', updateResult.code || 'UPDATE_CURRENCY_FAILED');
    }

    const result = {
      characterId,
      currencyType,
      amount,
      newBalance: updateResult.data[currencyType],
      reason,
    };

    this.logger.log(`增加货币成功: ${characterId}, ${currencyType}: +${amount}, 新余额: ${result.newBalance}`);
    return XResultUtils.ok(result);
  }

  /**
   * 内部货币操作方法 - 扣除货币
   * 供其他业务方法内部调用，无需DTO包装
   *
   * @param characterId 角色ID
   * @param currencyType 货币类型
   * @param amount 数量
   * @param reason 操作原因
   * @param allowNegative 是否允许负数余额（默认false）
   * @returns 操作结果
   */
  async subtractCurrencyInternal(
    characterId: string,
    currencyType: string,
    amount: number,
    reason: string = '扣除货币',
    allowNegative: boolean = false
  ): Promise<XResult<{
    characterId: string;
    currencyType: string;
    amount: number;
    newBalance: number;
    reason: string;
  }>> {
    // 1. 验证并获取角色
    const characterResult = await this.validateAndGetCharacter(
      characterId,
      this.characterRepository,
      '扣除货币'
    );
    if (XResultUtils.isFailure(characterResult)) {
      return characterResult;
    }

    const character = characterResult.data;

    // 2. 检查余额是否足够
    const currentBalance = character[currencyType] || 0;
    if (!allowNegative && currentBalance < amount) {
      return XResultUtils.error(`${currencyType}余额不足，当前：${currentBalance}，需要：${amount}`, 'INSUFFICIENT_CURRENCY');
    }

    // 3. 计算新余额
    const newBalance = currentBalance - amount;
    const updateData: any = {};
    updateData[currencyType] = newBalance;

    // 4. 更新角色数据
    const updateResult = await this.characterRepository.update(characterId, updateData);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(updateResult.message || '更新货币失败', updateResult.code || 'UPDATE_CURRENCY_FAILED');
    }

    const result = {
      characterId,
      currencyType,
      amount,
      newBalance: updateResult.data[currencyType],
      reason,
    };

    this.logger.log(`扣除货币成功: ${characterId}, ${currencyType}: -${amount}, 新余额: ${result.newBalance}`);
    return XResultUtils.ok(result);
  }

  /**
   * 批量货币操作方法
   * 支持一次性进行多种货币的增减操作
   *
   * @param characterId 角色ID
   * @param operations 操作列表
   * @param reason 操作原因
   * @returns 操作结果
   */
  async batchCurrencyOperation(
    characterId: string,
    operations: Array<{
      currencyType: string;
      amount: number; // 正数为增加，负数为扣除
    }>,
    reason: string = '批量货币操作'
  ): Promise<XResult<{
    characterId: string;
    operations: Array<{
      currencyType: string;
      amount: number;
      newBalance: number;
    }>;
    reason: string;
  }>> {
    // 1. 验证并获取角色
    const characterResult = await this.validateAndGetCharacter(
      characterId,
      this.characterRepository,
      '批量货币操作'
    );
    if (XResultUtils.isFailure(characterResult)) {
      return characterResult;
    }

    const character = characterResult.data;
    const updateData: any = {};
    const results: Array<{
      currencyType: string;
      amount: number;
      newBalance: number;
    }> = [];

    // 2. 计算所有货币的新余额
    for (const operation of operations) {
      const { currencyType, amount } = operation;
      const currentBalance = character[currencyType] || 0;
      const newBalance = currentBalance + amount;

      // 检查扣除操作是否会导致负余额
      if (amount < 0 && newBalance < 0) {
        return XResultUtils.error(
          `${currencyType}余额不足，当前：${currentBalance}，需要：${Math.abs(amount)}`,
          'INSUFFICIENT_CURRENCY'
        );
      }

      updateData[currencyType] = newBalance;
      results.push({
        currencyType,
        amount,
        newBalance,
      });
    }

    // 3. 批量更新角色数据
    const updateResult = await this.characterRepository.update(characterId, updateData);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(updateResult.message || '批量更新货币失败', updateResult.code || 'BATCH_UPDATE_CURRENCY_FAILED');
    }

    const result = {
      characterId,
      operations: results,
      reason,
    };

    this.logger.log(`批量货币操作成功: ${characterId}, 操作数量: ${operations.length}`);
    return XResultUtils.ok(result);
  }

  // ========== Controller层货币操作方法（保持兼容） ==========

  /**
   * 增加货币 - Controller层接口
   * 使用BaseService的业务操作框架，应用Result模式最佳实践
   * 这是重要的经济操作，使用executeBusinessOperation进行完整监控
   */
  async addCurrency(dto: AddCurrencyPayloadDto): Promise<XResult<{
    characterId: string;
    currencyType: string;
    amount: number;
    newBalance: number;
    reason: string;
  }>> {
    return this.executeBusinessOperation(async () => {
      // 使用内部方法处理货币增加
      return this.addCurrencyInternal(
        dto.characterId,
        dto.currencyType,
        dto.amount,
        dto.reason || '增加货币'
      );
    }, { reason: 'add_currency', metadata: { currencyType: dto.currencyType, amount: dto.amount } });
  }

  /**
   * 扣除货币
   * 使用BaseService的业务操作框架，应用Result模式最佳实践
   * 这是重要的经济操作，使用executeBusinessOperation进行完整监控
   */
  async subtractCurrency(dto: SubtractCurrencyPayloadDto): Promise<XResult<{
    characterId: string;
    currencyType: string;
    amount: number;
    newBalance: number;
    reason: string;
  }>> {
    return this.executeBusinessOperation(async () => {
      // 使用内部方法处理货币扣除
      return this.subtractCurrencyInternal(
        dto.characterId,
        dto.currencyType,
        dto.amount,
        dto.reason || '扣除货币'
      );
    }, { reason: 'subtract_currency', metadata: { currencyType: dto.currencyType, amount: dto.amount } });
  }

  /**
   * 购买体力
   * 使用BaseService的业务操作框架，应用Result模式最佳实践
   * 这是重要的经济操作，使用executeBusinessOperation进行完整监控
   */
  async buyEnergy(dto: BuyEnergyPayloadDto): Promise<XResult<BuyEnergyResultDto>> {
    return this.executeBusinessOperation(async () => {
      // 1. 查找角色
      const characterResult = await this.characterRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(characterResult.message || '查找角色失败', characterResult.code || 'FIND_CHARACTER_FAILED');
      }

      if (!characterResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }

      const character = characterResult.data;

      // 2. 计算购买成本和收益
      const count = dto.count;
      const goldCost = this.calculateEnergyCost(character.energyInfo.buyEnergyCount, count);
      const energyGained = 50 * count; // 每次购买50体力

      // 3. 检查金币是否足够
      if (character.gold < goldCost) {
        return XResultUtils.error(ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY], ErrorCode.INSUFFICIENT_CURRENCY);
      }

      // 4. 更新角色数据
      const newEnergy = Math.min(character.energy + energyGained, GAME_CONSTANTS.CHARACTER.MAX_ENERGY);
      const newBuyCount = character.energyInfo.buyEnergyCount + count;

      const updateData = {
        gold: character.gold - goldCost,
        energy: newEnergy,
        'energyInfo.buyEnergyCount': newBuyCount,
        'energyInfo.buyTime': Date.now(),
      };

      const updateResult = await this.characterRepository.update(dto.characterId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(updateResult.message || '更新角色数据失败', updateResult.code || 'UPDATE_CHARACTER_FAILED');
      }

      // 5. 构建结果（删除冗余的success字段）
      const result: BuyEnergyResultDto = {
        goldCost,
        energyGained,
        currentEnergy: newEnergy,
        todayBuyCount: newBuyCount,
        nextBuyCost: this.calculateEnergyCost(newBuyCount, 1),
      };

      this.logger.log(`购买体力成功: ${dto.characterId}, 消耗金币: ${goldCost}, 获得体力: ${energyGained}, 当前体力: ${newEnergy}`);
      return XResultUtils.ok(result);
    }, { reason: 'buy_energy', metadata: { count: dto.count } });
  }

  /**
   * 角色升级
   * 使用BaseService的业务操作框架，应用Result模式最佳实践
   * 这是重要的游戏进度操作，使用executeBusinessOperation进行完整监控
   */
  async levelUp(dto: LevelUpPayloadDto): Promise<XResult<LevelUpResultDto>> {
    return this.executeBusinessOperation(async () => {
      // 1. 查找角色
      const characterResult = await this.characterRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(characterResult.message || '查找角色失败', characterResult.code || 'FIND_CHARACTER_FAILED');
      }

      if (!characterResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }

      const character = characterResult.data;

      // 2. 验证升级参数
      const oldLevel = character.level;
      const targetLevel = dto.targetLevel || oldLevel + 1;

      if (targetLevel <= oldLevel || targetLevel > GAME_CONSTANTS.CHARACTER.MAX_LEVEL) {
        return XResultUtils.error('升级参数无效：目标等级必须大于当前等级且不超过最大等级', ErrorCode.INVALID_PARAMETER);
      }

      // 3. 计算升级奖励
      const rewards = this.calculateLevelUpRewards(oldLevel, targetLevel);

      // 4. 更新角色等级和奖励
      const updateData: any = {
        level: targetLevel,
      };

      rewards.forEach(reward => {
        if (updateData[reward.type]) {
          updateData[reward.type] += reward.amount;
        } else {
          updateData[reward.type] = character[reward.type] + reward.amount;
        }
      });

      const updateResult = await this.characterRepository.update(dto.characterId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(updateResult.message || '更新角色等级失败', updateResult.code || 'UPDATE_LEVEL_FAILED');
      }

      // 5. 构建结果（删除冗余的leveledUp字段）
      const result: LevelUpResultDto = {
        oldLevel,
        newLevel: targetLevel,
        rewards,
      };

      this.logger.log(`角色升级成功: ${dto.characterId}, ${oldLevel} -> ${targetLevel}, 奖励数量: ${rewards.length}`);
      return XResultUtils.ok(result);
    }, { reason: 'level_up', metadata: { targetLevel: dto.targetLevel } });
  }

  /**
   * 完成创角步骤
   * 使用Result模式，无需try/catch包装
   */
  async completeCreateStep(dto: CompleteStepPayloadDto): Promise<XResult<{
    characterId: string;
    completedStep: number;
    nextStep: number;
  }>> {
    // 1. 查找角色
    const characterResult = await this.characterRepository.findById(dto.characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return XResultUtils.error(characterResult.message || '查找角色失败', characterResult.code || 'FIND_CHARACTER_FAILED');
    }

    if (!characterResult.data) {
      return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
    }

    // 2. 更新创角步骤
    const updateData = {
      'gameProgress.createRoleStep': dto.step,
    };

    const updateResult = await this.characterRepository.update(dto.characterId, updateData);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(updateResult.message || '更新创角步骤失败', updateResult.code || 'UPDATE_CREATE_STEP_FAILED');
    }

    const result = {
      characterId: dto.characterId,
      completedStep: dto.step,
      nextStep: dto.step + 1,
    };

    this.logger.log(`完成创角步骤: ${dto.characterId}, 步骤: ${dto.step}`);
    return XResultUtils.ok(result);
  }

  /**
   * 完成新手引导
   * 使用Result模式，无需try/catch包装
   */
  async finishGuide(dto: FinishGuidePayloadDto): Promise<XResult<{
    characterId: string;
    isNewer: boolean;
    rewards: Array<{ type: string; amount: number }>;
  }>> {
    // 1. 查找角色
    const characterResult = await this.characterRepository.findById(dto.characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return XResultUtils.error(characterResult.message || '查找角色失败', characterResult.code || 'FIND_CHARACTER_FAILED');
    }

    if (!characterResult.data) {
      return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
    }

    const character = characterResult.data;

    // 2. 定义新手引导奖励
    const rewards = [
      { type: 'cash', amount: 5000 },
      { type: 'gold', amount: 100 },
    ];

    // 3. 更新角色数据
    const updateData = {
      'gameProgress.isNewer': false,
      cash: character.cash + 5000,
      gold: character.gold + 100,
    };

    const updateResult = await this.characterRepository.update(dto.characterId, updateData);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(updateResult.message || '完成新手引导失败', updateResult.code || 'FINISH_GUIDE_FAILED');
    }

    const result = {
      characterId: dto.characterId,
      isNewer: false,
      rewards,
    };

    this.logger.log(`完成新手引导: ${dto.characterId}, 奖励: 现金${5000}, 金币${100}`);
    return XResultUtils.ok(result);
  }

  /**
   * 转换为DTO
   */
  private toCharacterInfoDto(character: Character): CharacterInfoDto {
    return {
      characterId: character.characterId,
      userId: character.userId,
      serverId: character.serverId,
      openId: character.openId,
      name: character.name,
      avatar: character.avatar,
      faceIcon: character.faceIcon,
      faceUrl: character.faceUrl,
      level: character.level,
      cash: character.cash,
      gold: character.gold,
      energy: character.energy,
      fame: character.fame,
      allFame: character.allFame,
      trophy: character.trophy,
      worldCoin: character.worldCoin,
      chip: character.chip,
      integral: character.integral,
      vip: character.vipInfo.vip,
      vipExp: character.vipInfo.vipExp,
      fieldLevel: character.fieldLevel,
      league: character.league,
      isOnline: character.isOnline,
      isNewer: character.gameProgress.isNewer,
      createRoleStep: character.gameProgress.createRoleStep,
      activeDay: character.gameProgress.activeDay,
      createTime: character.loginInfo.createTime,
      lastLoginTime: character.loginInfo.loginTime,
      beliefId: character.beliefInfo.beliefId,
      honor: character.beliefInfo.honor,
    };
  }



  /**
   * 计算体力购买费用
   */
  private calculateEnergyCost(currentBuyCount: number, buyCount: number): number {
    let totalCost = 0;
    for (let i = 0; i < buyCount; i++) {
      const cost = Math.min(100 + (currentBuyCount + i) * 50, 1000); // 最高1000金币
      totalCost += cost;
    }
    return totalCost;
  }

  /**
   * 计算升级奖励
   */
  private calculateLevelUpRewards(oldLevel: number, newLevel: number): Array<{ type: string; amount: number }> {
    const rewards = [];
    const levelDiff = newLevel - oldLevel;

    // 每级奖励1000欧元和10体力
    rewards.push({ type: 'cash', amount: levelDiff * 1000 });
    rewards.push({ type: 'energy', amount: levelDiff * 10 });

    // 每5级额外奖励100金币
    const fiveLevelRewards = Math.floor(newLevel / 5) - Math.floor(oldLevel / 5);
    if (fiveLevelRewards > 0) {
      rewards.push({ type: 'gold', amount: fiveLevelRewards * 100 });
    }

    return rewards;
  }

  /**
   * 设置角色信仰
   * 使用Result模式，无需try/catch包装
   */
  async setCharacterBelief(dto: SetBeliefPayloadDto): Promise<XResult<CharacterDocument>> {
    // 1. 查找角色
    const characterResult = await this.characterRepository.findById(dto.characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return XResultUtils.error(characterResult.message || '查找角色失败', characterResult.code || 'FIND_CHARACTER_FAILED');
    }

    if (!characterResult.data) {
      return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
    }

    // 2. 更新角色信仰
    const updateResult = await this.characterRepository.update(dto.characterId, { beliefId: dto.beliefId });
    if (XResultUtils.isFailure(updateResult)) {
      return this.handleRepositoryResult(updateResult, '设置角色信仰失败');
    }

    this.logger.log(`角色信仰设置成功: ${dto.characterId}, 信仰ID: ${dto.beliefId}`);
    return updateResult;
  }

  /**
   * 使用兑换码
   * 使用BaseService的业务操作框架，应用Result模式最佳实践
   * 这是复杂业务操作，使用executeBusinessOperation进行完整监控
   */
  async useRedeemCode(dto: UseRedeemCodePayloadDto): Promise<XResult<{
    rewards: any[];
  }>> {
    return this.executeBusinessOperation(async () => {
      // 1. 查找角色
      const characterResult = await this.characterRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(characterResult.message || '查找角色失败', characterResult.code || 'FIND_CHARACTER_FAILED');
      }

      if (!characterResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }

      const character = characterResult.data;

      // 2. 检查兑换码是否已使用
      if (character.deemCodeList[dto.group] === dto.codeId) {
        return XResultUtils.error(ErrorMessages[ErrorCode.REDEEM_CODE_ALREADY_USED], ErrorCode.REDEEM_CODE_ALREADY_USED);
      }

      // 3. 处理兑换码奖励
      const rewardsResult = await this.processRedeemCodeRewards(dto.characterId, dto.group, dto.codeId);
      if (XResultUtils.isFailure(rewardsResult)) {
        return XResultUtils.error(rewardsResult.message || '处理兑换码奖励失败', rewardsResult.code || 'PROCESS_REWARDS_FAILED');
      }

      // 4. 记录兑换码使用
      const updateData = {
        [`deemCodeList.${dto.group}`]: dto.codeId
      };

      const updateResult = await this.characterRepository.update(dto.characterId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(updateResult.message || '记录兑换码使用失败', updateResult.code || 'UPDATE_REDEEM_CODE_FAILED');
      }

      const result = {
        rewards: rewardsResult.data,
      };

      this.logger.log(`兑换码使用成功: ${dto.characterId}, 组: ${dto.group}, 码: ${dto.codeId}, 奖励数量: ${rewardsResult.data.length}`);
      return XResultUtils.ok(result);
    }, { reason: 'use_redeem_code', metadata: { group: dto.group, codeId: dto.codeId } });
  }

  /**
   * 更新持续buff
   * 使用Result模式，无需try/catch包装
   */
  async updateContinuedBuff(dto: UpdateBuffPayloadDto): Promise<XResult<CharacterDocument>> {
    // 1. 查找角色
    const characterResult = await this.characterRepository.findById(dto.characterId);
    const character = this.handleRepositoryResult(characterResult, '查找角色失败');
    if (XResultUtils.isFailure(character)) {
      return character;
    }

    if (!character.data) {
      return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
    }

    // 2. 更新buff时间
    const now = Date.now();
    const updateData = {
      continuedBuffStartTime: now,
      continuedBuffEndTime: now + (dto.buffDuration * 1000),
      lastBuffUpdateTime: now,
    };

    const updateResult = await this.characterRepository.update(dto.characterId, updateData);
    if (XResultUtils.isFailure(updateResult)) {
      return this.handleRepositoryResult(updateResult, '更新持续buff失败');
    }

    this.logger.log(`持续buff更新成功: ${dto.characterId}, 持续时间: ${dto.buffDuration}秒`);
    return updateResult;
  }

  // ==================== old项目核心方法 ====================

  /**
   * 获取个人信息
   * 严格基于old项目: getPersonInfo
   */
  async getPersonInfo(dto: GetPersonInfoPayloadDto): Promise<XResult<{
    personInfo: any;
    totalValue: number;
  }>> {
    this.logger.log(`获取个人信息: ${dto.characterId}`);

    // 1. 查找角色
    const characterResult = await this.characterRepository.findById(dto.characterId);
    const character = this.handleRepositoryResult(characterResult, '查找角色失败');
    if (XResultUtils.isFailure(character)) {
      return character;
    }

    if (!character.data) {
      return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
    }

    // 2. 构建个人信息（基于old项目的getPersonInfo返回结构）
    const personInfo = {
      name: character.data.name,
      level: character.data.level,
      fame: character.data.fame,
      energy: character.data.energy,
      formationAct: character.data.formationAct || 0,
      league: character.data.league,
      faceIcon: character.data.faceIcon,
      faceUrl: character.data.faceUrl,
      vip: character.data.vipInfo?.vip || 0,
      vipExp: character.data.vipInfo?.vipExp || 0,
      beliefId: character.data.beliefId,
    };

    // 3. 计算队伍总价值
    const totalValueResult = await this.calcTeamValue(dto.characterId);
    const totalValue = XResultUtils.isSuccess(totalValueResult) ? totalValueResult.data : 0;

    const result = {
      personInfo,
      totalValue
    };

    this.logger.log(`获取个人信息成功: ${dto.characterId}`);
    return XResultUtils.ok(result);
  }

  /**
   * 创建角色
   * 严格基于old项目: createRole
   */
  async createRole(dto: CreateRolePayloadDto): Promise<XResult<{
    heroList: any[];
    oneTeam: any;
  }>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`创建角色: ${dto.characterId}, 参数: ${JSON.stringify(dto)}`);

      const { qualified, name, faceIcon } = dto;

      // 1. 查找角色
      const characterResult = await this.characterRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return this.handleRepositoryResult(characterResult, '查找角色失败');
      }

      if (!characterResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }

      // 2. 更新角色信息
      const updateData = {
        name,
        faceIcon,
        qualified,
        'gameProgress.isNewer': false // 完成新手
      };

      const updateResult = await this.characterRepository.update(dto.characterId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return this.handleRepositoryResult(updateResult, '更新角色信息失败');
      }

      // 3. 创建初始球员（基于old项目createInitBallerAndTeam逻辑）
      const initialHeroesResult = await this.createInitialHeroes(dto.characterId, qualified);
      const initialHeroes = XResultUtils.isSuccess(initialHeroesResult) ? initialHeroesResult.data : [];

      // 4. 创建初始阵容
      const initialFormationResult = await this.createInitialFormation(dto.characterId, initialHeroes);
      const initialFormation = XResultUtils.isSuccess(initialFormationResult) ? initialFormationResult.data : {};

      // 5. 触发新手任务系统
      const taskResult = await this.triggerNewbieTasks(dto.characterId);
      if (XResultUtils.isFailure(taskResult)) {
        this.logger.warn(`触发新手任务失败: ${taskResult.message}`);
      }

      const result = {
        heroList: initialHeroes, // 创建的初始球员列表
        oneTeam: initialFormation,  // 创建的初始阵容
      };

      this.logger.log(`角色创建成功: ${dto.characterId}, 名称: ${dto.name}, 球员数: ${initialHeroes.length}`);
      return XResultUtils.ok(result);
    }, { reason: 'create_role', metadata: { name: dto.name, qualified: dto.qualified } });
  }

  /**
   * 修改角色名称
   * 基于old项目: modifyPlayerName
   * 使用BaseService的业务操作框架，应用Result模式最佳实践
   * 这是重要的角色信息修改操作，使用executeBusinessOperation进行完整监控
   */
  async modifyCharacterName(dto: ModifyCharacterNamePayloadDto): Promise<XResult<CharacterDocument>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`修改角色名称: ${dto.characterId}, 新名称: ${dto.name}`);

      const { name } = dto;

      // 1. 查找角色
      const characterResult = await this.characterRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return this.handleRepositoryResult(characterResult, '查找角色失败');
      }

      if (!characterResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }

      const character = characterResult.data;

      // 2. 检查名称是否已被使用
      const nameExistsResult = await this.characterRepository.existsByName(name.trim(), character.serverId);
      if (XResultUtils.isFailure(nameExistsResult)) {
        return this.handleRepositoryResult(nameExistsResult, '检查角色名称失败');
      }

      if (nameExistsResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NAME_TAKEN], ErrorCode.CHARACTER_NAME_TAKEN);
      }

      // 3. 更新角色名称
      const updateResult = await this.characterRepository.update(dto.characterId, { name: name.trim() });
      if (XResultUtils.isFailure(updateResult)) {
        return this.handleRepositoryResult(updateResult, '更新角色名称失败');
      }

      this.logger.log(`角色名称修改成功: ${dto.characterId}, 新名称: ${name.trim()}`);
      return XResultUtils.ok(updateResult.data);
    }, { reason: 'modify_character_name', metadata: { newName: dto.name } });
  }

  /**
   * 添加资源
   * 严格基于old项目: addResource
   */
  async addResource(dto: AddResourcePayloadDto): Promise<XResult<{
    message: string;
    newValue: number;
  }>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`添加资源: ${dto.characterId}, 类型: ${dto.resourceType}, 数量: ${dto.amount}, 原因: ${dto.reason}`);

      // 1. 查找角色
      const characterResult = await this.characterRepository.findById(dto.characterId);
      const character = this.handleRepositoryResult(characterResult, '查找角色失败');
      if (XResultUtils.isFailure(character)) {
        return character;
      }

      if (!character.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }
      const updateData: any = {};

      // 2. 基于old项目的资源类型处理
      switch (dto.resourceType) {
        case 'cash':
          updateData.cash = Math.max(0, character.data.cash + dto.amount);
          break;
        case 'gold':
          updateData.gold = Math.max(0, character.data.gold + dto.amount);
          break;
        case 'energy':
          updateData.energy = Math.max(0, Math.min(GAME_CONSTANTS.CHARACTER.MAX_ENERGY, character.data.energy + dto.amount));
          break;
        case 'fame':
          updateData.fame = Math.max(0, character.data.fame + dto.amount);
          updateData.allFame = Math.max(0, character.data.allFame + Math.max(0, dto.amount));
          break;
        case 'trophy':
          updateData.trophy = Math.max(0, character.data.trophy + dto.amount);
          break;
        case 'worldCoin':
          updateData.worldCoin = Math.max(0, character.data.worldCoin + dto.amount);
          break;
        case 'chip':
          updateData.chip = Math.max(0, character.data.chip + dto.amount);
          break;
        case 'integral':
          updateData.integral = Math.max(0, character.data.integral + dto.amount);
          break;
        default:
          return XResultUtils.error('未知资源类型', ErrorCode.INVALID_PARAMETER);
      }

      // 3. 更新角色数据
      const updateResult = await this.characterRepository.update(dto.characterId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return this.handleRepositoryResult(updateResult, '更新资源失败');
      }

      const result = {
        message: '资源添加成功',
        newValue: updateResult.data[dto.resourceType],
      };

      this.logger.log(`资源添加成功: ${dto.characterId}, ${dto.resourceType}: +${dto.amount}, 新值: ${result.newValue}`);
      return XResultUtils.ok(result);
    }, { reason: 'add_resource', metadata: { resourceType: dto.resourceType, amount: dto.amount, reason: dto.reason } });
  }

  /**
   * 获取体力奖励
   * 严格基于old项目: getEnergyReward
   */
  async getEnergyReward(dto: GetEnergyRewardPayloadDto): Promise<XResult<{ energy: number }>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`获取体力奖励: ${dto.characterId}, 类型: ${dto.type}`);

      // 1. 查找角色
      const characterResult = await this.characterRepository.findById(dto.characterId);
      const character = this.handleRepositoryResult(characterResult, '查找角色失败');
      if (XResultUtils.isFailure(character)) {
        return character;
      }

      if (!character.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }
      const updateData: any = {};

      // 2. 基于old项目的体力奖励逻辑
      switch (dto.type) {
        case 1: // 第一次免费领取
          if (character.data.energyInfo.firstFree > 0) {
            return XResultUtils.error('今日第一次体力奖励已领取', 'ENERGY_REWARD_ALREADY_CLAIMED');
          }

          // 检查时间限制（12:00-14:00）
          const hour = new Date().getHours();
          if (hour < 12 || hour >= 14) {
            return XResultUtils.error('体力奖励领取时间为12:00-14:00', 'ENERGY_REWARD_TIME_INVALID');
          }

          updateData['energyInfo.firstFree'] = 1;
          updateData.energy = Math.min(GAME_CONSTANTS.CHARACTER.MAX_ENERGY, character.data.energy + 20);
          break;

        case 2: // 第二次免费领取
          if (character.data.energyInfo.secondFree > 0) {
            return XResultUtils.error('今日第二次体力奖励已领取', 'ENERGY_REWARD_ALREADY_CLAIMED');
          }

          // 检查时间限制（18:00-20:00）
          const hour2 = new Date().getHours();
          if (hour2 < 18 || hour2 >= 20) {
            return XResultUtils.error('体力奖励领取时间为18:00-20:00', 'ENERGY_REWARD_TIME_INVALID');
          }

          updateData['energyInfo.secondFree'] = 1;
          updateData.energy = Math.min(GAME_CONSTANTS.CHARACTER.MAX_ENERGY, character.data.energy + 20);
          break;

        default:
          return XResultUtils.error('无效的体力奖励类型', ErrorCode.INVALID_PARAMETER);
      }

      // 3. 更新角色数据
      const updateResult = await this.characterRepository.update(dto.characterId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return this.handleRepositoryResult(updateResult, '更新体力奖励失败');
      }

      const result = { energy: updateResult.data.energy };

      this.logger.log(`体力奖励领取成功: ${dto.characterId}, 类型: ${dto.type}, 当前体力: ${result.energy}`);
      return XResultUtils.ok(result);
    }, { reason: 'get_energy_reward', metadata: { type: dto.type } });
  }

  /**
   * 消耗金币任务
   * 严格基于old项目: costCashTask
   */
  async costCashTask(dto: CostCashTaskPayloadDto): Promise<XResult<{
    message: string;
    remainingCash: number;
  }>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`消耗金币: ${dto.characterId}, 数量: ${dto.amount}, 原因: ${dto.reason}`);

      // 1. 查找角色
      const characterResult = await this.characterRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return this.handleRepositoryResult(characterResult, '查找角色失败');
      }

      if (!characterResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }

      const character = characterResult.data;

      // 2. 检查金币是否足够
      if (character.cash < dto.amount) {
        return XResultUtils.error(ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY], ErrorCode.INSUFFICIENT_CURRENCY);
      }

      // 3. 扣除金币
      const newCash = character.cash - dto.amount;
      const updateResult = await this.characterRepository.update(dto.characterId, { cash: newCash });
      if (XResultUtils.isFailure(updateResult)) {
        return this.handleRepositoryResult(updateResult, '扣除金币失败');
      }

      const result = {
        message: '金币消耗成功',
        remainingCash: updateResult.data.cash,
      };

      this.logger.log(`金币消耗成功: ${dto.characterId}, 消耗: ${dto.amount}, 剩余: ${result.remainingCash}`);
      return XResultUtils.ok(result);
    }, { reason: 'cost_cash_task', metadata: { amount: dto.amount, reason: dto.reason } });
  }

  /**
   * 获取资源数量
   */
  async getResourceQuantity(characterId: string, resourceType: string): Promise<XResult<number>> {
    this.logger.log(`获取资源数量: ${characterId}, 类型: ${resourceType}`);

    // 1. 查找角色
    const characterResult = await this.characterRepository.findById(characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return this.handleRepositoryResult(characterResult, '查找角色失败');
    }

    if (!characterResult.data) {
      return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
    }

    const character = characterResult.data;
    // 检查是否合法的资源类型
    if (!['cash', 'gold', 'energy', 'worldCoin', 'chip', 'integral'].includes(resourceType)) {
      return XResultUtils.error('未知资源类型', ErrorCode.INVALID_PARAMETER);
    }

    const quantity = character[resourceType] || 0;

    this.logger.log(`获取资源数量成功: ${characterId}, 类型: ${resourceType}, 数量: ${quantity}`);
    return XResultUtils.ok(quantity);
  }

  /**
   * 检查资源是否足够
   * 基于old项目: checkResourceIsEnough
   */
  async checkResourceSufficient(characterId: string, resourceType: string, amount: number): Promise<XResult<boolean>> {
    // 通过getResourceQuantity获取数量
    const quantityResult = await this.getResourceQuantity(characterId, resourceType);
    if (XResultUtils.isFailure(quantityResult)) {
      return quantityResult;
    }

    const sufficient = quantityResult.data >= amount;
    return XResultUtils.ok(sufficient);
  }

  /**
   * 扣除资源
   * 基于old项目: deductResource
   */
  async deductResource(dto: DeductResourcePayloadDto): Promise<XResult<{
    message: string;
    newValue: number;
  }>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`扣除资源: ${dto.characterId}, 类型: ${dto.resourceType}, 数量: ${dto.amount}, 原因: ${dto.reason}`);

      // 1. 检查资源是否足够
      const isEnoughResult = await this.checkResourceSufficient(dto.characterId, dto.resourceType, dto.amount);
      if (XResultUtils.isFailure(isEnoughResult)) {
        return XResultUtils.error(isEnoughResult.message || '检查资源失败', isEnoughResult.code || 'CHECK_RESOURCE_FAILED');
      }

      if (!isEnoughResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.INSUFFICIENT_CURRENCY], ErrorCode.INSUFFICIENT_CURRENCY);
      }

      // 2. 查找角色（为了获取当前值）
      const characterResult = await this.characterRepository.findById(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return this.handleRepositoryResult(characterResult, '查找角色失败');
      }

      if (!characterResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
      }

      const character = characterResult.data;
      const updateData: any = {};

      // 3. 扣除资源
      switch (dto.resourceType) {
        case 'cash':
          updateData.cash = character.cash - dto.amount;
          break;
        case 'gold':
          updateData.gold = character.gold - dto.amount;
          break;
        case 'energy':
          updateData.energy = character.energy - dto.amount;
          break;
        case 'worldCoin':
          updateData.worldCoin = character.worldCoin - dto.amount;
          break;
        case 'chip':
          updateData.chip = character.chip - dto.amount;
          break;
        case 'integral':
          updateData.integral = character.integral - dto.amount;
          break;
        default:
          return XResultUtils.error('未知资源类型', ErrorCode.INVALID_PARAMETER);
      }

      // 4. 更新角色数据
      const updateResult = await this.characterRepository.update(dto.characterId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return this.handleRepositoryResult(updateResult, '扣除资源失败');
      }

      const result = {
        message: '资源扣除成功',
        newValue: updateResult.data[dto.resourceType],
      };

      this.logger.log(`资源扣除成功: ${dto.characterId}, ${dto.resourceType}: -${dto.amount}, 新值: ${result.newValue}`);
      return XResultUtils.ok(result);
    }, { reason: 'deduct_resource', metadata: { resourceType: dto.resourceType, amount: dto.amount, reason: dto.reason } });
  }



  // ==================== 微服务通信参考代码实现 ====================
  // 以下方法提供完整的微服务通信参考代码，包含详细的使用说明
  // 开启方法：将对应的TODO注释取消，调用这些方法

  /**
   * 计算队伍总价值 - 微服务通信参考实现
   * 基于old项目: calcTeamValue方法
   *
   * 使用说明：
   * 1. 确保Hero服务已启动并可通信
   * 2. 在上面的TODO位置取消注释: const totalValue = await this.calcTeamValue(characterId);
   * 3. 确保MicroserviceKitModule已正确配置Hero服务连接
   */
  private async calcTeamValue(characterId: string): Promise<XResult<number>> {
    this.logger.log(`计算队伍总价值: ${characterId}`);

    // 如果有微服务客户端，调用Hero服务
    if (this.microserviceClient) {
      const heroesResult = await this.callMicroservice(
        MICROSERVICE_NAMES.HERO_SERVICE,
        'hero.getCharacterHeroes',
        { characterId }
      );

      if (XResultUtils.isSuccess(heroesResult)) {
        const heroes = heroesResult.data || [];
        let totalValue = 0;

        // 累计所有球员的市场价值
        for (const hero of heroes) {
          totalValue += hero.marketValue || 0;
        }

        this.logger.log(`队伍总价值计算完成: ${characterId}, 价值: ${totalValue}`);
        return XResultUtils.ok(totalValue);
      } else {
        this.logger.warn(`获取角色球员失败，使用默认值: ${heroesResult.message}`);
      }
    }

    // 暂时返回模拟值
    const defaultValue = 1000000; // 100万
    this.logger.log(`使用默认队伍价值: ${characterId}, 价值: ${defaultValue}`);
    return XResultUtils.ok(defaultValue);
  }

  /**
   * 创建初始球员
   * 基于old项目: createInitBallerAndTeam中的球员创建逻辑
   *
   * 实现逻辑：
   * 1. 根据CreateBaller配置表获取初始球员配置
   * 2. 调用Hero服务创建每个初始球员
   * 3. 设置新手球员标记(IsNewer = 1)
   * 4. 返回创建的球员列表供阵容使用
   */
  private async createInitialHeroes(characterId: string, qualified: number): Promise<XResult<any[]>> {
    this.logger.log(`创建初始球员: ${characterId}, 资质: ${qualified}`);

    // 1. 获取CreateBaller配置表
    const createBallerConfigs = await this.gameConfig.createBaller?.getAll() || [];
    if (createBallerConfigs.length === 0) {
      this.logger.error('CreateBaller配置表为空');
      return XResultUtils.ok([]); // 返回空数组而不是错误
    }

    const createdHeroes = [];
    let heroCount = 0;

    // 2. 基于old项目逻辑：只创建Mode为INIT_BALLER的球员
    for (const config of createBallerConfigs) {
      if (config.mode === 1) { // commonEnum.CREATE_INIT_BALLER_MODE.INIT_BALLER = 1
        const heroCreateResult = await this.createSingleInitialHero(
          characterId,
          config.ballerId,
          qualified
        );

        if (XResultUtils.isSuccess(heroCreateResult)) {
          createdHeroes.push(heroCreateResult.data);
          heroCount++;
          this.logger.log(`初始球员创建成功: ${heroCreateResult.data.heroId}, ResId: ${config.ballerId}`);
        } else {
          this.logger.error(`初始球员创建失败: ResId=${config.ballerId}`, heroCreateResult.message);
        }
      }
    }

    // 3. 检查创建的球员数量（基于old项目：至少需要11个球员）
    if (heroCount < 11) {
      this.logger.warn(`初始球员数量不足: ${heroCount}/11, 配置表可能有误`);
      // 不返回错误，允许继续创建角色，但记录警告
    }

    this.logger.log(`初始球员创建完成: ${characterId}, 成功创建: ${heroCount}个`);
    return XResultUtils.ok(createdHeroes);
  }

  /**
   * 创建单个初始球员
   * 基于old项目: addHeroNewer方法
   */
  private async createSingleInitialHero(characterId: string, resId: number, qualified: number): Promise<XResult<any>> {
    // 如果有微服务客户端，调用Hero服务
    if (this.microserviceClient) {
      const result = await this.callMicroservice(
        MICROSERVICE_NAMES.HERO_SERVICE,
        'hero.createHero',
        {
          characterId,
          resId,
          source: 'initial_creation',
          isNewer: 1, // 新手球员标记
          qualified
        }
      );

      if (XResultUtils.isSuccess(result)) {
        return XResultUtils.ok(result.data);
      } else {
        this.logger.warn(`调用Hero服务创建球员失败，使用模拟数据: ${result.message}`);
      }
    }

    // 暂时返回模拟数据
    const mockHero = {
      heroId: `hero_${Date.now()}_${Math.random()}`,
      resId,
      characterId,
      isNewer: 1,
      level: 1,
      position: this.getPositionByResId(resId),
    };

    this.logger.log(`创建模拟初始球员: ${mockHero.heroId}, ResId: ${resId}`);
    return XResultUtils.ok(mockHero);
  }

  /**
   * 创建初始阵容
   * 基于old项目: createInitBallerAndTeam中的阵容创建逻辑
   *
   * 实现逻辑：
   * 1. 根据CreateTeamFormation配置表获取阵型配置
   * 2. 调用Formation服务创建阵容
   * 3. 将球员按位置添加到阵容中
   * 4. 设置为当前主力阵容
   */
  private async createInitialFormation(characterId: string, heroes: any[]): Promise<XResult<any>> {
    this.logger.log(`创建初始阵容: ${characterId}, 球员数: ${heroes.length}`);
    
    // 1. 获取CreateTeamFormation配置表
    const teamFormationConfigs = await this.gameConfig.createTeamFormation?.getAll() || [];
    if (teamFormationConfigs.length === 0) {
      this.logger.error('CreateTeamFormation配置表为空');
      return XResultUtils.ok({}); // 返回空对象而不是错误
    }

    // 2. 使用第一个配置创建初始阵容
    const config = teamFormationConfigs[0];
    const formationResult = await this.createFormationWithConfig(characterId, config, heroes);

    if (XResultUtils.isSuccess(formationResult)) {
      const formation = formationResult.data;

      // 3. 设置为当前主力阵容
      const setMainResult = await this.setAsMainFormation(characterId, formation.formationId);
      if (XResultUtils.isFailure(setMainResult)) {
        this.logger.warn(`设置主力阵容失败: ${setMainResult.message}`);
      }

      // 4. 初始化战术数据
      const tacticsResult = await this.initializeTactics(characterId, formation.formationId);
      if (XResultUtils.isFailure(tacticsResult)) {
        this.logger.warn(`初始化战术失败: ${tacticsResult.message}`);
      }

      this.logger.log(`初始阵容创建成功: ${formation.formationId}`);
      return XResultUtils.ok(formation);
    } else {
      this.logger.error('初始阵容创建失败', formationResult.message);
      return XResultUtils.ok({}); // 返回空对象，允许角色创建继续
    }
  }

  /**
   * 根据配置创建阵容
   * 基于old项目: addTeamFormation + addTeamFormationHero逻辑
   */
  private async createFormationWithConfig(characterId: string, config: any, heroes: any[]): Promise<XResult<any>> {
    this.logger.log(`根据配置创建阵容: ${characterId}, 配置ID: ${config.id}`);

    // 1. 调用FormationService创建阵容
    const formationResult = await this.formationService.createFormation(
      characterId,
      config.formationId, // 使用配置表中的FormationId
      1 // FormationType.COMMON
    );

    if (XResultUtils.isFailure(formationResult)) {
      return XResultUtils.error(formationResult.message || '创建阵容失败', formationResult.code || 'CREATE_FORMATION_FAILED');
    }

    const formation = formationResult.data;
    const formationUid = formation.uid;

    // 2. 根据配置表的位置和球员ID添加球员到阵容（基于old项目addTeamFormationHero）
    const positions = config.position; // 位置数组：["GK", "DC", "DC", "DL", "DR", "MC", "MC", "ML", "MR", "ST", "ST"]
    const heroIds = config.heroId;     // 球员ID数组：[1183, 736, 803, 672, 1140, 268, 918, 555, 738, 1040, 1226]

    // 3. 为每个位置添加对应的球员（使用现有的formation.addHeroToPosition）
    for (let i = 0; i < positions.length && i < heroIds.length && i < heroes.length; i++) {
      const position = positions[i];
      const configHeroId = heroIds[i];

      // 从创建的球员中找到对应配置ID的球员
      const hero = heroes.find(h => h.resId === configHeroId);
      if (!hero) {
        this.logger.warn(`未找到配置球员: ${configHeroId}, 跳过位置: ${position}`);
        continue;
      }

      // 调用FormationService添加球员到阵容
      const addHeroResult = await this.formationService.addHeroToPosition(
        characterId,
        formationUid,
        position,
        0, // 插入到位置的第一个位置
        hero.uid
      );

      if (XResultUtils.isFailure(addHeroResult)) {
        this.logger.warn(`添加球员到阵容失败: ${hero.uid} -> ${position}, ${addHeroResult.message}`);
      } else {
        this.logger.debug(`球员添加成功: ${hero.name} (${hero.resId}) -> ${position}`);
      }
    }

    const result = {
      formationId: formationUid,
      formation: formation
    };

    this.logger.log(`阵容创建完成: ${formationUid}`);
    return XResultUtils.ok(result);
  }

  /**
   * 设置为主力阵容
   * 基于old项目: setCurrTeamFormationId逻辑
   */
  private async setAsMainFormation(characterId: string, formationId: string): Promise<XResult<void>> {
    // 调用FormationService设置主力阵容
    const result = await this.formationService.setActiveFormation(characterId, formationId);

    if (XResultUtils.isFailure(result)) {
      return XResultUtils.error(result.message || '设置主力阵容失败', result.code || 'SET_MAIN_FORMATION_FAILED');
    }

    this.logger.log(`设置主力阵容成功: ${characterId}, 阵容ID: ${formationId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 初始化战术数据
   * 基于old项目: TEAM_TACTICS_LIST初始化逻辑
   */
  private async initializeTactics(characterId: string, formationId: string): Promise<XResult<void>> {
    // TODO: 调用Formation服务初始化战术
    // const result = await this.formationService.initializeTactics(characterId, formationId);
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(result.message || '初始化战术失败', result.code || 'INITIALIZE_TACTICS_FAILED');
    // }

    this.logger.log(`战术初始化成功: ${characterId}, 阵容ID: ${formationId}`);
    return XResultUtils.ok(undefined);
  }


  /**
   * 触发新手任务
   * 基于old项目: initTaskList + triggerTask逻辑
   *
   * 实现逻辑：
   * 1. 初始化任务列表
   * 2. 触发角色创建相关任务
   * 3. 触发阵容创建相关任务
   * 4. 创建足球场地
   */
  private async triggerNewbieTasks(characterId: string): Promise<XResult<void>> {
    this.logger.log(`触发新手任务: ${characterId}`);

    // 1. 初始化任务列表
    const initResult = await this.initializeTaskList(characterId);
    if (XResultUtils.isFailure(initResult)) {
      this.logger.warn(`初始化任务列表失败: ${initResult.message}`);
    }

    // 2. 触发新手任务（基于old项目的任务类型）
    const newbieTaskTypes = [
      'TARGET_TYPE.ELEVEN',    // 获得11个球员
      'TARGET_TYPE.SIX',       // 获得6个球员
      'TARGET_TYPE.THREE',     // 获得3个球员
      'create_character',      // 创建角色
      'first_formation',       // 首次阵容
      'first_match'           // 首次比赛
    ];

    for (const taskType of newbieTaskTypes) {
      const taskResult = await this.triggerSingleTask(characterId, taskType);
      if (XResultUtils.isFailure(taskResult)) {
        this.logger.warn(`触发任务失败: ${taskType}, ${taskResult.message}`);
      }
    }

    // 3. 创建足球场地（基于old项目footballGround.createFootballGround）
    const groundResult = await this.createFootballGround(characterId);
    if (XResultUtils.isFailure(groundResult)) {
      this.logger.warn(`创建足球场地失败: ${groundResult.message}`);
    }

    this.logger.log(`新手任务触发完成: ${characterId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 初始化任务列表
   * 基于old项目: initTaskList方法
   */
  private async initializeTaskList(characterId: string): Promise<XResult<void>> {
    if (!characterId) {
      return XResultUtils.error('参数无效：characterId不能为空', ErrorCode.INVALID_PARAMETER);
    }

    // TODO: 调用Activity服务初始化任务列表
    // if (this.microserviceClient) {
    //   const result = await this.callMicroservice(
    //     MICROSERVICE_NAMES.ACTIVITY_SERVICE,
    //     'task.initializeTaskList',
    //     { characterId }
    //   );
    //   if (XResultUtils.isFailure(result)) {
    //     return XResultUtils.error(result.message || '初始化任务列表失败', result.code || 'INITIALIZE_TASK_LIST_FAILED');
    //   }
    // }

    this.logger.log(`任务列表初始化成功: ${characterId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 触发单个任务
   * 基于old项目: triggerTask方法
   */
  private async triggerSingleTask(characterId: string, taskType: string): Promise<XResult<void>> {
    if (!characterId || !taskType) {
      return XResultUtils.error('参数无效：characterId和taskType不能为空', ErrorCode.INVALID_PARAMETER);
    }

    // TODO: 调用Activity服务触发任务
    // if (this.microserviceClient) {
    //   const result = await this.callMicroservice(
    //     MICROSERVICE_NAMES.ACTIVITY_SERVICE,
    //     'task.triggerTask',
    //     { characterId, taskType }
    //   );
    //   if (XResultUtils.isFailure(result)) {
    //     return XResultUtils.error(result.message || '触发任务失败', result.code || 'TRIGGER_TASK_FAILED');
    //   }
    // }

    this.logger.log(`任务触发成功: ${characterId}, 类型: ${taskType}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 创建足球场地
   * 基于old项目: footballGround.createFootballGround方法
   */
  private async createFootballGround(characterId: string): Promise<XResult<void>> {
    if (!characterId) {
      return XResultUtils.error('参数无效：characterId不能为空', ErrorCode.INVALID_PARAMETER);
    }

    // TODO: 调用Hero服务创建足球场地
    // if (this.microserviceClient) {
    //   const result = await this.callMicroservice(
    //     MICROSERVICE_NAMES.HERO_SERVICE,
    //     'ground.createFootballGround',
    //     { characterId }
    //   );
    //   if (XResultUtils.isFailure(result)) {
    //     return XResultUtils.error(result.message || '创建足球场地失败', result.code || 'CREATE_GROUND_FAILED');
    //   }
    // }

    this.logger.log(`足球场地创建成功: ${characterId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 根据ResId获取球员位置
   * 基于old项目: Footballer配置表的Position字段
   */
  private getPositionByResId(resId: number): string {
    // 基于old项目的位置映射逻辑
    // 这里简化处理，实际应该从配置表获取
    const positionMap = {
      1001: 'GK',  // 门将
      1002: 'CB',  // 中后卫
      1003: 'CB',  // 中后卫
      1004: 'LB',  // 左后卫
      1005: 'RB',  // 右后卫
      1006: 'CM',  // 中场
      1007: 'CM',  // 中场
      1008: 'LM',  // 左中场
      1009: 'RM',  // 右中场
      1010: 'ST',  // 前锋
      1011: 'ST',  // 前锋
    };

    return positionMap[resId] || 'CM'; // 默认中场
  }

  /**
   * 处理兑换码奖励
   * 基于old项目: 兑换码奖励发放逻辑
   *
   * 实现逻辑：
   * 1. 根据兑换码获取GiftCode配置
   * 2. 根据Type和ItemType确定奖励类型
   * 3. 发放对应的奖励（货币、物品、球员等）
   * 4. 记录奖励发放日志
   */
  private async processRedeemCodeRewards(characterId: string, group: string, codeId: string): Promise<XResult<any[]>> {
    this.logger.log(`处理兑换码奖励: ${characterId}, 组: ${group}, 码: ${codeId}`);

    // 1. 获取兑换码配置（基于old项目GiftCode配置表）
    const giftCodeConfigs = await this.gameConfig.giftCode?.getAll() || [];
    const codeConfig = giftCodeConfigs.find(config => config.gift === codeId);

    if (!codeConfig) {
      this.logger.warn(`兑换码配置不存在: ${codeId}`);
      return XResultUtils.ok([]);
    }

    const rewards = [];

    // 2. 基于old项目逻辑处理不同类型的奖励
    // Type: 1=普通礼包, 2=特殊礼包
    // ItemType: 1=物品奖励, 2=货币奖励, 3=球员奖励

    if (codeConfig.type === 1) {
      // 普通礼包处理
      await this.processNormalGiftRewards(characterId, codeConfig, rewards);
    } else if (codeConfig.type === 2) {
      // 特殊礼包处理
      await this.processSpecialGiftRewards(characterId, codeConfig, rewards);
    }

    this.logger.log(`兑换码奖励处理完成: ${characterId}, 奖励数: ${rewards.length}`);
    return XResultUtils.ok(rewards);
  }

  /**
   * 处理普通礼包奖励
   * 基于old项目: 普通礼包包含3个奖励项目
   */
  private async processNormalGiftRewards(characterId: string, codeConfig: any, rewards: any[]): Promise<XResult<void>> {
    // 基于old项目GiftCode配置：ResId1/Num1, ResId2/Num2, ResId3/Num3
    const rewardItems = [
      { resId: codeConfig.resId1, num: codeConfig.num1 },
      { resId: codeConfig.resId2, num: codeConfig.num2 },
      { resId: codeConfig.resId3, num: codeConfig.num3 },
    ];

    for (const item of rewardItems) {
      if (item.resId && item.num > 0) {
        await this.processRewardItem(characterId, item.resId, item.num, codeConfig.itemType, rewards);
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 处理特殊礼包奖励
   * 基于old项目: 特殊礼包可能有不同的处理逻辑
   */
  private async processSpecialGiftRewards(characterId: string, codeConfig: any, rewards: any[]): Promise<XResult<void>> {
    // 特殊礼包使用相同的奖励结构，但可能有额外的处理逻辑
    await this.processNormalGiftRewards(characterId, codeConfig, rewards);

    // TODO: 特殊礼包的额外处理逻辑
    // 例如：VIP加成、限时奖励等

    return XResultUtils.ok(undefined);
  }

  /**
   * 处理单个奖励项目
   * 基于old项目: 根据ResId判断奖励类型
   */
  private async processRewardItem(characterId: string, resId: number, num: number, itemType: number, rewards: any[]): Promise<XResult<void>> {
    // 基于old项目逻辑：根据ResId范围判断奖励类型
    if (resId >= 90000 && resId <= 90999) {
      // 货币类型 (90000-90999)
      await this.processCurrencyReward(characterId, resId, num, rewards);
    } else if (resId >= 80000 && resId <= 89999) {
      // 物品类型 (80000-89999)
      await this.processItemReward(characterId, resId, num, rewards);
    } else if (resId >= 10000 && resId <= 79999) {
      // 球员类型 (10000-79999)
      await this.processHeroReward(characterId, resId, num, rewards);
    } else {
      this.logger.warn(`未知的奖励类型: ResId=${resId}`);
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 处理货币奖励
   * 基于old项目: 90000=金币, 90001=钻石, 90002=欧元等
   */
  private async processCurrencyReward(characterId: string, resId: number, amount: number, rewards: any[]): Promise<XResult<void>> {
    let currencyType: 'cash' | 'gold' | 'energy' | 'worldCoin' | 'chip' | 'integral';
    let description: string;

    // 基于old项目的货币ResId映射
    switch (resId) {
      case 90000:
        currencyType = 'gold';
        description = `获得${amount}金币`;
        break;
      case 90001:
        currencyType = 'chip'; // 钻石映射到chip
        description = `获得${amount}钻石`;
        break;
      case 90002:
        currencyType = 'worldCoin';
        description = `获得${amount}欧元`;
        break;
      case 90003:
        currencyType = 'cash';
        description = `获得${amount}现金`;
        break;
      case 90004:
        currencyType = 'energy';
        description = `获得${amount}体力`;
        break;
      case 90005:
        currencyType = 'integral';
        description = `获得${amount}积分`;
        break;
      default:
        this.logger.warn(`未知的货币类型: ResId=${resId}`);
        return XResultUtils.ok(undefined);
    }

    // 调用内部货币服务添加货币
    const currencyResult = await this.addCurrencyInternal(
      characterId,
      currencyType,
      amount,
      'redeem_code'
    );

    if (XResultUtils.isFailure(currencyResult)) {
      this.logger.warn(`兑换码货币添加失败: ${currencyResult.message}`, { characterId, currencyType, amount });
    }

    rewards.push({
      type: 'currency',
      currencyType,
      amount,
      description,
      resId,
    });

      this.logger.log(`货币奖励发放成功: ${characterId}, ${currencyType}: ${amount}`);
      return XResultUtils.ok(undefined);
  }

  /**
   * 处理物品奖励
   * 基于old项目: 调用Inventory服务添加物品
   */
  private async processItemReward(characterId: string, itemId: number, quantity: number, rewards: any[]): Promise<XResult<void>> {
    // 如果有微服务客户端，调用Inventory服务添加物品
    if (this.microserviceClient) {
      const addItemResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'inventory.addItem',
        {
          characterId,
          configId: itemId,
          quantity,
          source: 'redeem_code'
        }
      );

      if (XResultUtils.isFailure(addItemResult)) {
        this.logger.error(`调用Inventory服务添加物品失败: ${addItemResult.message}`);
        return XResultUtils.error(addItemResult.message || '添加物品失败', addItemResult.code || 'ADD_ITEM_FAILED');
      }

      this.logger.log(`物品奖励发放成功: ${characterId}, 物品ID: ${itemId}, 数量: ${quantity}`);
    } else {
      this.logger.warn(`微服务客户端未配置，物品奖励仅记录: ${characterId}, 物品ID: ${itemId}, 数量: ${quantity}`);
    }

    // 记录奖励信息
    rewards.push({
      type: 'item',
      itemId,
      quantity,
      description: `获得物品 x${quantity}`,
      resId: itemId,
    });

    return XResultUtils.ok(undefined);
  }

  /**
   * 处理球员奖励
   * 基于old项目: 调用Hero服务创建球员
   */
  private async processHeroReward(characterId: string, heroResId: number, quantity: number, rewards: any[]): Promise<XResult<void>> {
    for (let i = 0; i < quantity; i++) {
      // 如果有微服务客户端，调用Hero服务创建球员
      if (this.microserviceClient) {
        const createHeroResult = await this.callMicroservice(
          MICROSERVICE_NAMES.HERO_SERVICE,
          'hero.createHero',
          {
            characterId,
            resId: heroResId,
            source: 'redeem_code'
          }
        );

        if (XResultUtils.isSuccess(createHeroResult)) {
          rewards.push({
            type: 'hero',
            heroResId,
            heroId: createHeroResult.data?.heroId,
            description: `获得球员`,
            resId: heroResId,
          });

          this.logger.log(`球员奖励发放成功: ${characterId}, 球员ResId: ${heroResId}, HeroId: ${createHeroResult.data?.heroId}`);
        } else {
          this.logger.error(`调用Hero服务创建球员失败: ${createHeroResult.message}`);
          return XResultUtils.error(createHeroResult.message || '创建球员失败', createHeroResult.code || 'CREATE_HERO_FAILED');
        }
      } else {
        // 微服务客户端未配置，仅记录奖励
        rewards.push({
          type: 'hero',
          heroResId,
          description: `获得球员`,
          resId: heroResId,
        });

        this.logger.warn(`微服务客户端未配置，球员奖励仅记录: ${characterId}, 球员ResId: ${heroResId}`);
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 根据资质获取初始球员配置
   * 基于old项目: 不同资质对应不同的初始球员
   */
  private getInitialHeroConfigsByQualified(qualified: number): any[] {
    const configs = {
      1: [ // 低资质
        { resId: 1001, position: 'GK' },
        { resId: 1002, position: 'CB' },
        { resId: 1003, position: 'CB' },
        { resId: 1004, position: 'LB' },
        { resId: 1005, position: 'RB' },
        { resId: 1006, position: 'CM' },
        { resId: 1007, position: 'CM' },
        { resId: 1008, position: 'LM' },
        { resId: 1009, position: 'RM' },
        { resId: 1010, position: 'ST' },
        { resId: 1011, position: 'ST' },
      ],
      2: [ // 中资质
        { resId: 2001, position: 'GK' },
        { resId: 2002, position: 'CB' },
        { resId: 2003, position: 'CB' },
        { resId: 2004, position: 'LB' },
        { resId: 2005, position: 'RB' },
        { resId: 2006, position: 'CM' },
        { resId: 2007, position: 'CM' },
        { resId: 2008, position: 'LM' },
        { resId: 2009, position: 'RM' },
        { resId: 2010, position: 'ST' },
        { resId: 2011, position: 'ST' },
      ],
      3: [ // 高资质
        { resId: 3001, position: 'GK' },
        { resId: 3002, position: 'CB' },
        { resId: 3003, position: 'CB' },
        { resId: 3004, position: 'LB' },
        { resId: 3005, position: 'RB' },
        { resId: 3006, position: 'CM' },
        { resId: 3007, position: 'CM' },
        { resId: 3008, position: 'LM' },
        { resId: 3009, position: 'RM' },
        { resId: 3010, position: 'ST' },
        { resId: 3011, position: 'ST' },
      ],
    };

    return configs[qualified] || configs[1];
  }

  /**
   * 根据名称搜索角色
   * 基于old项目: accountService.searchPlayerName 和 account.getPlayerUid
   * 用于商业赛等功能的对手搜索
   */
  async searchByName(dto: SearchByNamePayloadDto): Promise<XResult<any>> {
    this.logger.log(`根据名称搜索角色: ${dto.name}, 服务器: ${dto.serverId || 'all'}`);
    
    // 调用Repository搜索角色
    const searchResult = await this.characterRepository.findByName(dto.name.trim(), dto.serverId);

    // 使用handleRepositoryResult处理Repository结果
    const characterResult = this.handleRepositoryResult(searchResult);
    if (XResultUtils.isFailure(characterResult)) {
      this.logger.error(`搜索角色失败: ${characterResult.message}`);
      return XResultUtils.ok(null); // 搜索失败返回null，保持原有业务逻辑
    }

    const character = characterResult.data;
    if (!character) {
      this.logger.log(`未找到角色: ${name}`);
      return XResultUtils.ok(null); // 未找到返回null，保持原有业务逻辑
    }

    // 构建搜索结果的基本信息，用于商业赛等功能
    const searchResultData = {
      characterId: character.characterId,
      playerUid: character.characterId, // 兼容old项目的playerUid字段
      name: character.name,
      faceIcon: character.faceIcon || 0,
      level: character.level,
      fame: character.fame || 0,
      ballFanCount: character.fame || 0, // 使用fame作为球迷数的临时替代
      actualStrength: character.level * 100, // 使用等级计算临时实力值
      isGroundOpen: character.fieldLevel > 0, // 根据球场等级判断是否开放
      groundOpenStatus: character.fieldLevel > 0, // 兼容old项目的groundOpenStatus字段
      lastUpdateTime: new Date(),
      // TODO: 这些字段应该通过其他微服务获取更准确的数据
      // - ballFanCount: 通过Hero服务获取真实球迷数
      // - actualStrength: 通过Formation服务获取队伍实力
      // - isGroundOpen: 通过Ground服务获取球场开放状态
    };

    this.logger.log(`角色搜索成功: ${dto.name} -> ${character.characterId}`);
    return XResultUtils.ok(searchResultData);
  }

  // ==================== 球探系统方法 ====================

  /**
   * 获取角色球探数据
   * 基于old项目Scout实体
   */
  async getScoutData(characterId: string): Promise<XResult<any>> {
    this.logger.log(`获取球探数据: ${characterId}`);
    
    // 1. 查找角色
    const characterResult = await this.characterRepository.findById(characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return this.handleRepositoryResult(characterResult, '查找角色失败');
    }

    if (!characterResult.data) {
      return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
    }

    const character = characterResult.data;

    // 2. 检查并初始化球探数据
    if (!character.scoutData) {
      const initialScoutData = {
        scoutRp: 0,
        scoutEnergy: 100,
        scoutGroup: [
          { type: 1, count: 5, getTime: 1 }, // 初级球探组
          { type: 2, count: 1, getTime: 1 }, // 高级球探组
          { type: 3, count: 1, getTime: 1 }, // 顶级球探组
        ],
        scoutPack: [],
        isFrist: 0,
        reTime: Date.now(),
        lastEnergyRegenTime: new Date(),
        lastSearchTime: 0,
        maxScoutPackSize: 10,
      };

      const updateResult = await this.characterRepository.update(characterId, { scoutData: initialScoutData });
      if (XResultUtils.isFailure(updateResult)) {
        return this.handleRepositoryResult(updateResult, '初始化球探数据失败');
      }

      character.scoutData = initialScoutData;
    }

    // 3. 更新体力恢复（基于old项目的体力恢复机制）
    const regenResult = await this.updateScoutEnergyRegen(character);
    if (XResultUtils.isFailure(regenResult)) {
      this.logger.warn(`更新球探体力恢复失败: ${regenResult.message}`);
    }

    this.logger.log(`获取球探数据成功: ${dto.characterId}`);
    return XResultUtils.ok(character.scoutData);
  }

  /**
   * 更新角色球探数据
   * 基于old项目Scout实体
   */
  async updateScoutData(dto: UpdateScoutDataPayloadDto): Promise<XResult<void>> {
    this.logger.log(`更新球探数据: ${dto.characterId}`);
    
    // 1. 查找角色
    const characterResult = await this.characterRepository.findById(dto.characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return this.handleRepositoryResult(characterResult, '查找角色失败');
    }

    if (!characterResult.data) {
      return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND], ErrorCode.CHARACTER_NOT_FOUND);
    }

    const character = characterResult.data;

    // 2. 更新球探数据
    const updatedScoutData = {
      ...character.scoutData,
      ...dto.scoutData,
    };

    const updateResult = await this.characterRepository.update(dto.characterId, { scoutData: updatedScoutData });
    if (XResultUtils.isFailure(updateResult)) {
      return this.handleRepositoryResult(updateResult, '更新球探数据失败');
    }

    this.logger.log(`球探数据更新成功: ${dto.characterId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 更新球探体力恢复
   * 基于old项目的体力恢复机制：每5分钟恢复1点体力，最大100点
   */
  private async updateScoutEnergyRegen(character: any): Promise<XResult<void>> {
    const now = new Date();
    const lastRegenTime = new Date(character.scoutData.lastEnergyRegenTime);
    const timeDiff = now.getTime() - lastRegenTime.getTime();

    // 每5分钟恢复1点体力
    const regenInterval = 5 * 60 * 1000; // 5分钟
    const regenPoints = Math.floor(timeDiff / regenInterval);

    if (regenPoints > 0 && character.scoutData.scoutEnergy < 100) {
      const newEnergy = Math.min(100, character.scoutData.scoutEnergy + regenPoints);
      const updatedScoutData = {
        ...character.scoutData,
        scoutEnergy: newEnergy,
        lastEnergyRegenTime: now,
      };

      const updateResult = await this.characterRepository.update(character.characterId, { scoutData: updatedScoutData });
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(updateResult.message || '更新球探体力失败', updateResult.code || 'UPDATE_SCOUT_ENERGY_FAILED');
      }

      this.logger.log(`球探体力恢复: ${character.characterId}, 恢复${regenPoints}点, 当前${newEnergy}点`);
    }

    return XResultUtils.ok(undefined);
  }
}
