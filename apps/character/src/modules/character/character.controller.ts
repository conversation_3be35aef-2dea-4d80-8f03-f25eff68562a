import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { MessagePatternInternal } from '@libs/common/decorators';
import { BaseController } from '@libs/common/controller';
import { CharacterService } from './character.service';
import {
  CharacterInfoDto,
  LoginResultDto,
  LevelUpResultDto,
  BuyEnergyResultDto
} from '@character/common/dto/character.dto';
import {
  CreateCharacterPayloadDto,
  LoginCharacterPayloadDto,
  LogoutCharacterPayloadDto,
  GetCharacterInfoPayloadDto,
  UpdateCharacterPayloadDto,
  GetCharacterListPayloadDto,
  AddCurrencyPayloadDto,
  SubtractCurrencyPayloadDto,
  BuyEnergyPayloadDto,
  LevelUpPayloadDto,
  CompleteStepPayloadDto,
  FinishGuidePayloadDto,
  SetBeliefPayloadDto,
  UseRedeemCodePayloadDto,
  UpdateBuffPayloadDto,
  SearchByNamePayloadDto,
  GetPersonInfoPayloadDto,
  CreateRolePayloadDto,
  ModifyCharacterNamePayloadDto,
  AddResourcePayloadDto,
  GetEnergyRewardPayloadDto,
  CostCashTaskPayloadDto,
  DeductResourcePayloadDto,
  GetScoutDataPayloadDto,
  UpdateScoutDataPayloadDto,
  InitializeFromAuthPayloadDto
} from '@character/common/dto/character-payload.dto';
import { Cacheable, CacheEvict, CachePut } from '@libs/redis';
import { CharacterDocument } from '@character/common/schemas/character.schema';
import { XResponse, PaginationResult, XResultUtils } from '@libs/common/types/result.type';
import { StandardMicroserviceValidationPipe } from "@libs/common";

/**
 * 角色控制器
 * 继承BaseController，提供统一的微服务接口处理和Result模式集成
 *
 * 🎯 核心功能：
 * - 角色创建和管理
 * - 角色登录和认证
 * - 角色属性和资源管理
 * - 角色升级和能量购买
 * - 角色列表查询和分页
 *
 * 🚀 优化特性：
 * - 统一的请求处理和响应格式
 * - 完整的Result模式错误处理
 * - 自动的性能监控和日志记录
 * - 标准化的缓存装饰器集成
 * - 参数验证和提取框架
 */
@Controller()
export class CharacterController extends BaseController {
  constructor(private readonly characterService: CharacterService) {
    super('CharacterController', {
      enableRequestLogging: true,
      enablePerformanceMonitoring: true,
      autoHandleExceptions: true
    });
  }

  // ========== 角色管理接口 ==========

  /**
   * 创建新角色
   * 使用BaseController的统一请求处理框架和新的Payload DTO
   */
  @MessagePattern('character.create')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'user:characters:#{payload.userId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createCharacter(@Payload() payload: CreateCharacterPayloadDto): Promise<XResponse<CharacterDocument>> {
    this.logger.log(`创建角色请求: ${JSON.stringify(payload, null, 2)}`);

    const result = await this.characterService.createCharacter(payload);
    return this.fromResult(result);
  }

  /**
   * 角色登录
   * 使用BaseController的统一请求处理框架和新的Payload DTO
   */
  @MessagePattern('character.login')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CachePut({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async loginCharacter(@Payload() payload: LoginCharacterPayloadDto): Promise<XResponse<LoginResultDto>> {
    this.logger.log(`登录角色请求: ${JSON.stringify(payload, null, 2)}`);

    const result = await this.characterService.loginCharacter(payload);
    return this.fromResult(result);
  }

  /**
   * 角色登出
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.logout')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server'
  })
  async logoutCharacter(@Payload() payload: LogoutCharacterPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`登出角色请求: ${JSON.stringify(payload, null, 2)}`);

    const result = await this.characterService.logoutCharacter(payload);
    return this.fromResult(result);
  }

  /**
   * 获取角色信息
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.getInfo')
  @UsePipes(StandardMicroserviceValidationPipe)
  @Cacheable({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getCharacterInfo(@Payload() payload: GetCharacterInfoPayloadDto): Promise<XResponse<CharacterInfoDto>> {
    this.logger.log(`获取角色信息请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.getCharacterInfo(payload.characterId);
    return this.fromResult(result);
  }

  /**
   * 更新角色信息
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.update')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CachePut({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async updateCharacter(@Payload() payload: UpdateCharacterPayloadDto): Promise<XResponse<CharacterDocument>> {
    this.logger.log(`更新角色信息请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.updateCharacter(payload);
    return this.fromResult(result);
  }

  /**
   * 获取角色列表
   * 使用BaseController的统一请求处理框架和分页支持
   */
  @MessagePattern('character.getList')
  @UsePipes(StandardMicroserviceValidationPipe)
  @Cacheable({
    key: 'user:characters:#{payload.userId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getCharacterList(@Payload() payload: GetCharacterListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取角色列表请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.getCharacterList(payload);
    return this.fromResult(result);
  }

  // ========== 货币操作接口（内部服务调用）==========

  /**
   * 增加货币 - 仅内部服务调用
   * 使用BaseController的统一请求处理框架
   */
  @MessagePatternInternal('character.currency.add')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addCurrency(@Payload() payload: AddCurrencyPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`增加货币请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.addCurrency(payload);
    return this.fromResult(result);
  }

  /**
   * 扣除货币 - 仅内部服务调用
   * 使用BaseController的统一请求处理框架
   */
  @MessagePatternInternal('character.currency.subtract')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async subtractCurrency(@Payload() payload: SubtractCurrencyPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`扣除货币请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.subtractCurrency(payload);
    return this.fromResult(result);
  }

  // ========== 角色功能接口 ==========

  /**
   * 购买体力
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.energy.buy')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyEnergy(@Payload() payload: BuyEnergyPayloadDto): Promise<XResponse<BuyEnergyResultDto>> {
    this.logger.log(`购买体力请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.buyEnergy(payload);
    return this.fromResult(result);
  }

  /**
   * 角色升级
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.levelup')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async levelUp(@Payload() payload: LevelUpPayloadDto): Promise<XResponse<LevelUpResultDto>> {
    this.logger.log(`角色升级请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.levelUp(payload);
    return this.fromResult(result);
  }

  /**
   * 完成创角步骤
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.progress.completeStep')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async completeCreateStep(@Payload() payload: CompleteStepPayloadDto): Promise<XResponse<{ characterId: string; completedStep: number; nextStep: number; }>> {
    this.logger.log(`完成创角步骤请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.completeCreateStep(payload);
    return this.fromResult(result);
  }

  /**
   * 完成新手引导
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.progress.finishGuide')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async finishGuide(@Payload() payload: FinishGuidePayloadDto): Promise<XResponse<{ characterId: string; isNewer: boolean; rewards: { type: string; amount: number; }[]; }>> {
    this.logger.log(`完成新手引导请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.finishGuide(payload);
    return this.fromResult(result);
  }

  /**
   * 设置角色信仰
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.setBelief')
  @UsePipes(StandardMicroserviceValidationPipe)
  async setCharacterBelief(@Payload() payload: SetBeliefPayloadDto): Promise<XResponse<CharacterDocument>> {
    this.logger.log(`设置角色信仰请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.setCharacterBelief(payload);
    return this.fromResult(result);
  }

  /**
   * 使用兑换码
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.useRedeemCode')
  @UsePipes(StandardMicroserviceValidationPipe)
  async useRedeemCode(@Payload() payload: UseRedeemCodePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`使用兑换码请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.useRedeemCode(payload);
    return this.fromResult(result);
  }

  /**
   * 更新持续buff
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.updateBuff')
  @UsePipes(StandardMicroserviceValidationPipe)
  async updateContinuedBuff(@Payload() payload: UpdateBuffPayloadDto): Promise<XResponse<CharacterDocument>> {
    this.logger.log(`更新持续buff请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.updateContinuedBuff(payload);
    return this.fromResult(result);
  }

  /**
   * 根据名称搜索角色
   * 基于old项目: accountService.searchPlayerName
   * 用于商业赛等功能的对手搜索
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.searchByName')
  @UsePipes(StandardMicroserviceValidationPipe)
  @Cacheable({
    key: 'character:search:name:#{payload.name}:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async searchCharacterByName(@Payload() payload: SearchByNamePayloadDto): Promise<XResponse<CharacterDocument[]>> {
    this.logger.log(`根据名称搜索角色请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.searchByName(payload);
    return this.fromResult(result);
  }

  // ========== old项目核心API（兼容性接口）==========

  /**
   * 获取个人信息
   * 对应old项目: game.player.getPersonInfo
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.getPersonInfo')
  @UsePipes(StandardMicroserviceValidationPipe)
  @Cacheable({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getPersonInfo(@Payload() payload: GetPersonInfoPayloadDto): Promise<XResponse<{ personInfo: any; totalValue: number; }>> {
    this.logger.log(`获取个人信息请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.getPersonInfo(payload);
    return this.fromResult(result);
  }

  /**
   * 创建角色
   * 对应old项目: game.player.createRole
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.createRole')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createRole(@Payload() payload: CreateCharacterPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`创建角色请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.createCharacter(payload);
    return this.fromResult(result);
  }

  /**
   * 修改玩家名称
   * 对应old项目: game.player.modifyPlayerName
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.modifyCharacterName')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async modifyCharacterName(@Payload() payload: ModifyCharacterNamePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`修改玩家名称请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.modifyCharacterName(payload);
    return this.fromResult(result);
  }

  /**
   * 添加资源 - 仅内部服务调用
   * 对应old项目: game.player.addResource
   * 使用BaseController的统一请求处理框架
   */
  @MessagePatternInternal('character.addResource')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addResource(@Payload() payload: AddResourcePayloadDto): Promise<XResponse<{ message: string; newValue: number; }>> {
    this.logger.log(`添加资源请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.addResource(payload);
    return this.fromResult(result);
  }

  /**
   * 获取体力奖励
   * 对应old项目: game.player.getEnergyReward
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.getEnergyReward')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getEnergyReward(@Payload() payload: GetEnergyRewardPayloadDto): Promise<XResponse<{ energy: number; }>> {
    this.logger.log(`获取体力奖励请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.getEnergyReward(payload);
    return this.fromResult(result);
  }

  /**
   * 消耗金币任务
   * 对应old项目: game.player.costCashTask
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.costCashTask')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async costCashTask(@Payload() payload: CostCashTaskPayloadDto): Promise<XResponse<{ message: string; remainingCash: number; }>> {
    this.logger.log(`消耗金币任务请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.costCashTask(payload);
    return this.fromResult(result);
  }

  /**
   * 扣除资源 - 仅内部服务调用
   * 对应old项目: game.player.deductResource
   * 使用BaseController的统一请求处理框架
   */
  @MessagePatternInternal('character.deductResource')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:person:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deductResource(@Payload() payload: DeductResourcePayloadDto): Promise<XResponse<{ message: string; newValue: number; }>> {
    this.logger.log(`扣除资源请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.deductResource(payload);
    return this.fromResult(result);
  }

  // ========== 球探系统API ==========

  /**
   * 获取角色球探数据
   * 基于old项目Scout实体
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.getScoutData')
  @UsePipes(StandardMicroserviceValidationPipe)
  @Cacheable({
    key: 'character:scout:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getScoutData(@Payload() payload: GetScoutDataPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取角色球探数据请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.getScoutData(payload.characterId);
    return this.fromResult(result);
  }

  /**
   * 更新角色球探数据
   * 基于old项目Scout实体
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.updateScoutData')
  @UsePipes(StandardMicroserviceValidationPipe)
  @CacheEvict({
    key: 'character:scout:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateScoutData(@Payload() payload: UpdateScoutDataPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新角色球探数据请求: ${JSON.stringify(payload, null, 2)}`);
    
    const result = await this.characterService.updateScoutData(payload);
    return this.fromResult(result);
  }

  // ========== 服务间通信接口 ==========

  /**
   * 接收Auth服务的角色初始化通知
   * 使用BaseController的统一请求处理框架
   */
  @MessagePattern('character.initializeFromAuth')
  @UsePipes(StandardMicroserviceValidationPipe)
  async initializeFromAuth(@Payload() payload: InitializeFromAuthPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`收到Auth服务角色初始化通知: ${JSON.stringify(payload, null, 2)}`);
    
    return this.handleRequest(async () => {
      this.logger.log(`📨 收到Auth服务角色初始化通知: ${payload.characterId}`);

      const result = await this.characterService.initializeFromAuth(payload);
      if (XResultUtils.isSuccess(result)) {
        return this.fromResult(XResultUtils.ok({
          characterId: result.data.characterId,
          success: true,
        }), '角色游戏数据初始化成功');
      } else {
        return this.fromResult(result, '角色游戏数据初始化失败');
      }
    }, payload);
  }
}
