/**
 * 角色计算器
 * 从CharacterService中剥离的计算函数
 * 基于old项目的计算逻辑，无外部依赖
 */

/**
 * 费用计算器
 * 基于old项目的费用计算逻辑
 */
export class CostCalculator {
  /**
   * 计算体力购买费用
   * 基于old项目的体力购买费用递增逻辑
   */
  static calculateEnergyCost(currentBuyCount: number, buyCount: number): number {
    let totalCost = 0;
    for (let i = 0; i < buyCount; i++) {
      const cost = Math.min(100 + (currentBuyCount + i) * 50, 1000); // 最高1000金币
      totalCost += cost;
    }
    return totalCost;
  }
}

/**
 * 奖励计算器
 * 基于old项目的奖励计算逻辑
 */
export class RewardCalculator {
  /**
   * 计算升级奖励
   * 基于old项目的升级奖励逻辑
   */
  static calculateLevelUpRewards(oldLevel: number, newLevel: number): Array<{ type: string; amount: number }> {
    const rewards = [];
    const levelDiff = newLevel - oldLevel;

    // 每级奖励1000欧元和10体力
    rewards.push({ type: 'cash', amount: levelDiff * 1000 });
    rewards.push({ type: 'energy', amount: levelDiff * 10 });

    // 每5级额外奖励钻石
    const diamondBonus = Math.floor(newLevel / 5) - Math.floor(oldLevel / 5);
    if (diamondBonus > 0) {
      rewards.push({ type: 'gold', amount: diamondBonus * 50 });
    }

    return rewards;
  }

  /**
   * 获取货币类型和描述
   * 基于old项目的货币ResId映射
   */
  static getCurrencyTypeAndDescription(resId: number): { 
    type: 'cash' | 'gold' | 'energy' | 'worldCoin' | 'chip' | 'integral'; 
    description: string 
  } {
    // 基于old项目的货币ResId映射
    switch (resId) {
      case 90000:
        return { type: 'gold', description: '获得金币' };
      case 90001:
        return { type: 'cash', description: '获得钻石' };
      case 90002:
        return { type: 'energy', description: '获得欧元' };
      case 90003:
        return { type: 'worldCoin', description: '获得世界币' };
      case 90004:
        return { type: 'chip', description: '获得芯片' };
      case 90005:
        return { type: 'integral', description: '获得积分' };
      default:
        return { type: 'gold', description: '获得金币' };
    }
  }

  /**
   * 判断奖励类型
   * 基于old项目逻辑：根据ResId范围判断奖励类型
   */
  static getRewardType(resId: number): 'currency' | 'item' | 'hero' | 'unknown' {
    if (resId >= 90000 && resId <= 90999) {
      return 'currency'; // 货币类型 (90000-90999)
    } else if (resId >= 80000 && resId <= 89999) {
      return 'item'; // 物品类型 (80000-89999)
    } else if (resId >= 10000 && resId <= 79999) {
      return 'hero'; // 球员类型 (10000-79999)
    } else {
      return 'unknown';
    }
  }
}

/**
 * 团队价值计算器
 * 基于old项目的团队价值计算逻辑
 */
export class TeamValueCalculator {
  /**
   * 计算团队总价值
   * 基于old项目: 累计所有球员的市场价值
   */
  static calculateTeamTotalValue(heroes: Array<{ marketValue: number }>): number {
    let totalValue = 0;

    // 累计所有球员的市场价值
    for (const hero of heroes) {
      totalValue += hero.marketValue || 0;
    }

    return totalValue;
  }

  /**
   * 计算平均球员价值
   * 基于团队总价值和球员数量
   */
  static calculateAverageHeroValue(heroes: Array<{ marketValue: number }>): number {
    if (heroes.length === 0) return 0;
    
    const totalValue = this.calculateTeamTotalValue(heroes);
    return Math.round(totalValue / heroes.length);
  }

  /**
   * 获取最有价值的球员
   * 返回市场价值最高的球员
   */
  static getMostValuableHero(heroes: Array<{ heroId: string; marketValue: number }>): { heroId: string; marketValue: number } | null {
    if (heroes.length === 0) return null;
    
    return heroes.reduce((max, hero) => 
      hero.marketValue > max.marketValue ? hero : max
    );
  }
}
