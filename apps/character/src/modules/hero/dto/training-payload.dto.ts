/**
 * Training模块的Payload DTO定义
 * 
 * 为training.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsBoolean, IsEnum, IsArray, Min, Max, Length } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// 训练类型枚举（从hero.dto.ts中复制）
export enum TrainingType {
  PRIMARY = 1,    // 初级训练
  INTERMEDIATE = 2, // 中级训练
  ADVANCED = 3,   // 高级训练
  TARGETED = 4,   // 定向训练
}


// 训练方式枚举（从hero.dto.ts中复制）
export enum TrainingMethod {
  GOLD = 1,      // 使用金币
  ITEM = 2,      // 使用道具
}

// ==================== 1. 训练信息查询相关 ====================

/**
 * 获取球员特训信息Payload DTO
 * @MessagePattern('training.getTrainInfo')
 */
export class GetTrainInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

// ==================== 2. 训练操作相关 ====================

/**
 * 训练球员Payload DTO
 * @MessagePattern('training.train')
 * 扩展TrainHeroDto，合并BasePayloadDto内容
 */
export class TrainPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '训练类型', enum: TrainingType, example: TrainingType.NORMAL })
  @Expose()
  @IsEnum(TrainingType, { message: '训练类型必须是有效的枚举值' })
  trainingType: TrainingType;

  @ApiProperty({ description: '训练方式', enum: TrainingMethod, example: TrainingMethod.GOLD })
  @Expose()
  @IsEnum(TrainingMethod, { message: '训练方式必须是有效的枚举值' })
  trainingMethod: TrainingMethod;

  @ApiPropertyOptional({ description: '定向训练的属性类型（仅定向训练时需要）', type: [String], example: ['speed', 'strength'] })
  @Expose()
  @IsOptional()
  @IsArray({ message: '属性类型必须是数组' })
  @IsString({ each: true, message: '属性类型必须是字符串' })
  @Length(1, 20, { each: true, message: '属性类型长度必须在1-20个字符之间' })
  targetAttributes?: string[];

  @ApiPropertyOptional({ description: '使用的道具ID（训练方式为道具时需要）', example: 1001 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '道具ID必须是数字' })
  @Min(1, { message: '道具ID不能小于1' })
  @Max(999999, { message: '道具ID不能大于999999' })
  itemId?: number;

  @ApiPropertyOptional({ description: '训练次数（默认为1）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '训练次数必须是数字' })
  @Min(1, { message: '训练次数不能小于1' })
  @Max(100, { message: '训练次数不能大于100' })
  count?: number;
}

/**
 * 替换球员特训Payload DTO
 * @MessagePattern('training.replaceHeroTrain')
 */
export class ReplaceHeroTrainPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '替换方式', example: 1 })
  @Expose()
  @IsNumber({}, { message: '替换方式必须是数字' })
  @Min(1, { message: '替换方式不能小于1' })
  @Max(10, { message: '替换方式不能大于10' })
  index: number;
}

// ==================== 3. 训练状态管理相关 ====================

/**
 * 设置球员训练状态Payload DTO
 * @MessagePattern('training.setTrainStatus')
 */
export class SetTrainStatusPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '是否训练中', example: true })
  @Expose()
  @IsBoolean({ message: '是否训练中必须是布尔值' })
  isTrain: boolean;

  @ApiPropertyOptional({ description: '是否锁定训练（可选）', example: false })
  @Expose()
  @IsOptional()
  @IsBoolean({ message: '是否锁定训练必须是布尔值' })
  isLockTrain?: boolean;
}
