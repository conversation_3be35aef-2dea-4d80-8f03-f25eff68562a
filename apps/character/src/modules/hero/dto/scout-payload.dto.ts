/**
 * Scout模块的Payload DTO定义
 * 
 * 为scout.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Min, Max, Length, IsArray } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 球探包管理相关 ====================

/**
 * 获取球探包信息Payload DTO
 * @MessagePattern('scout.getPackInfo')
 */
export class GetPackInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 删除球探包球员Payload DTO
 * @MessagePattern('scout.deletePackHero')
 */
export class DeletePackHeroPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '球员索引数组', type: [Number], example: [0, 1, 2] })
  @Expose()
  @IsArray({ message: '球员索引必须是数组' })
  @IsNumber({}, { each: true, message: '球员索引必须是数字' })
  @Min(0, { each: true, message: '球员索引不能小于0' })
  @Max(99, { each: true, message: '球员索引不能大于99' })
  index: number[];
}

/**
 * 签约球探球员Payload DTO
 * @MessagePattern('scout.signHero')
 */
export class SignHeroPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '球员配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '球员配置ID必须是数字' })
  @Min(1, { message: '球员配置ID不能小于1' })
  @Max(999999, { message: '球员配置ID不能大于999999' })
  resId: number;
}

// ==================== 2. 球探探索相关 ====================

/**
 * 球探探索Payload DTO
 * @MessagePattern('scout.explore')
 */
export class ExplorePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '探索类型', example: 1 })
  @Expose()
  @IsNumber({}, { message: '探索类型必须是数字' })
  @Min(1, { message: '探索类型不能小于1' })
  @Max(10, { message: '探索类型不能大于10' })
  type: number;
}

/**
 * 球探搜索Payload DTO
 * @MessagePattern('scout.search')
 */
export class SearchPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '球探类型', example: 'normal', enum: ['normal', 'advanced', 'premium'] })
  @Expose()
  @IsString({ message: '球探类型必须是字符串' })
  @Length(1, 20, { message: '球探类型长度必须在1-20个字符之间' })
  scoutType: string;

  @ApiPropertyOptional({ description: '目标品质（可选）', example: 'gold', enum: ['bronze', 'silver', 'gold', 'diamond'] })
  @Expose()
  @IsOptional()
  @IsString({ message: '目标品质必须是字符串' })
  @Length(1, 20, { message: '目标品质长度必须在1-20个字符之间' })
  targetQuality?: string;
}

// ==================== 3. 球探资源管理相关 ====================

/**
 * 球探RP值兑换Payload DTO
 * @MessagePattern('scout.exchangeScout')
 */
export class ExchangeScoutPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiPropertyOptional({ description: '兑换类型（默认为3）', example: 3 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '兑换类型必须是数字' })
  @Min(1, { message: '兑换类型不能小于1' })
  @Max(10, { message: '兑换类型不能大于10' })
  type?: number;
}

/**
 * 购买球探体力Payload DTO
 * @MessagePattern('scout.buyEnergy')
 */
export class BuyEnergyPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiPropertyOptional({ description: '购买数量（默认为50）', example: 50 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '购买数量必须是数字' })
  @Min(1, { message: '购买数量不能小于1' })
  @Max(1000, { message: '购买数量不能大于1000' })
  amount?: number;
}

/**
 * 恢复球探体力Payload DTO
 * @MessagePattern('scout.recoverEnergy')
 */
export class RecoverEnergyPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}
