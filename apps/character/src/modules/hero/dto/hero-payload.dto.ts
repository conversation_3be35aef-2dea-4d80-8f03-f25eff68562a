/**
 * Hero模块的Payload DTO定义
 * 
 * 为hero.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsBoolean, IsEnum, IsArray, Min, Max, Length } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { HeroPosition } from '@libs/game-constants';
import {HeroQuality} from "@hero/common/types";

// ==================== 1. 球员配置相关 ====================

/**
 * 获取球员配置Payload DTO
 * @MessagePattern('hero.getConfig')
 */
export class GetConfigPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '球员配置ID必须是数字' })
  @Min(1, { message: '球员配置ID不能小于1' })
  @Max(999999, { message: '球员配置ID不能大于999999' })
  heroId: number;
}

/**
 * 根据位置获取球员配置Payload DTO
 * @MessagePattern('hero.getConfigByPosition')
 */
export class GetConfigByPositionPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员位置', enum: HeroPosition, example: HeroPosition.GK })
  @Expose()
  @IsEnum(HeroPosition, { message: '球员位置必须是有效的枚举值' })
  position: HeroPosition;

  @ApiPropertyOptional({ description: '限制数量（可选）', example: 10 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '限制数量必须是数字' })
  @Min(1, { message: '限制数量不能小于1' })
  @Max(100, { message: '限制数量不能大于100' })
  limit?: number;
}

// ==================== 2. 球员状态管理相关 ====================

/**
 * 设置球员治疗状态Payload DTO
 * @MessagePattern('hero.setTreatStatus')
 */
export class SetTreatStatusPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '是否治疗中', example: true })
  @Expose()
  @IsBoolean({ message: '是否治疗中必须是布尔值' })
  isTreat: boolean;
}

/**
 * 更新球员疲劳值Payload DTO
 * @MessagePattern('hero.updateFatigue')
 */
export class UpdateFatiguePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '疲劳值变化', example: -10 })
  @Expose()
  @IsNumber({}, { message: '疲劳值变化必须是数字' })
  @Min(-100, { message: '疲劳值变化不能小于-100' })
  @Max(100, { message: '疲劳值变化不能大于100' })
  fatigueChange: number;
}

// ==================== 3. 球员基础操作相关 ====================

/**
 * 创建球员Payload DTO
 * @MessagePattern('hero.create')
 * 扩展CreateHeroDto，合并BasePayloadDto内容
 */
export class CreatePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;

  @ApiProperty({ description: '球员配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '球员配置ID必须是数字' })
  @Min(1, { message: '球员配置ID不能小于1' })
  @Max(999999, { message: '球员配置ID不能大于999999' })
  resId: number;

  @ApiPropertyOptional({ description: '球员名称（可选）', example: 'Messi' })
  @Expose()
  @IsOptional()
  @IsString({ message: '球员名称必须是字符串' })
  @Length(1, 50, { message: '球员名称长度必须在1-50个字符之间' })
  name?: string;

  @ApiPropertyOptional({ description: '初始位置（可选）', enum: HeroPosition, example: HeroPosition.GK })
  @Expose()
  @IsOptional()
  @IsEnum(HeroPosition, { message: '初始位置必须是有效的枚举值' })
  position?: HeroPosition;

  @ApiPropertyOptional({ description: '初始品质（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '初始品质必须是数字' })
  @Min(1, { message: '初始品质不能小于1' })
  @Max(6, { message: '初始品质不能大于6' })
  quality?: number;

  @ApiPropertyOptional({ description: '初始等级（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '初始等级必须是数字' })
  @Min(1, { message: '初始等级不能小于1' })
  @Max(100, { message: '初始等级不能大于100' })
  level?: number;

  @ApiPropertyOptional({ description: '国籍（可选）', example: '阿根廷' })
  @Expose()
  @IsOptional()
  @IsString({ message: '国籍必须是字符串' })
  @Length(1, 50, { message: '国籍长度必须在1-50个字符之间' })
  nationality?: string;

  @ApiPropertyOptional({ description: '俱乐部' })
  @Expose()
  @IsOptional()
  @IsString({ message: '俱乐部必须是字符串' })
  @Length(1, 50, { message: '俱乐部长度必须在1-50个字符之间' })
  club?: string;

  @ApiPropertyOptional({ description: '获得方式（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '获得方式必须是数字' })
  @Min(1, { message: '获得方式不能小于1' })
  @Max(10, { message: '获得方式不能大于10' })
  obtainType?: number;
}

/**
 * 批量获取球员信息Payload DTO
 * @MessagePattern('hero.getBatch')
 */
export class GetBatchPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID列表', type: [String], example: ['hero_12345', 'hero_67890'] })
  @Expose()
  @IsArray({ message: '球员ID列表必须是数组' })
  @IsString({ each: true, message: '球员ID必须是字符串' })
  @Length(1, 50, { each: true, message: '球员ID长度必须在1-50个字符之间' })
  heroIds: string[];
}

/**
 * 获取球员信息Payload DTO
 * @MessagePattern('hero.getInfo')
 */
export class GetInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 更新球员信息Payload DTO
 * @MessagePattern('hero.update')
 * 扩展UpdateHeroDto，合并BasePayloadDto内容
 */
export class UpdatePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiPropertyOptional({ description: '球员名称（可选）', example: 'New Name' })
  @Expose()
  @IsOptional()
  @IsString({ message: '球员名称必须是字符串' })
  @Length(1, 50, { message: '球员名称长度必须在1-50个字符之间' })
  name?: string;

  @ApiPropertyOptional({ description: '是否锁定' })
  @Expose()
  @IsOptional()
  @IsBoolean({ message: '是否锁定必须是布尔值' })
  isLocked?: boolean;

  @ApiPropertyOptional({ description: '装备列表' })
  @Expose()
  @IsOptional()
  @IsArray({ message: '装备列表必须是数组' })
  @IsString({ each: true, message: '装备ID必须是字符串' })
  equipments?: string[];

  @ApiPropertyOptional({ description: '薪水' })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '薪水必须是数字' })
  @Min(0, { message: '薪水不能小于0' })
  salary?: number;

  @ApiPropertyOptional({ description: '等级（可选）', example: 10 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '等级必须是数字' })
  @Min(1, { message: '等级不能小于1' })
  @Max(100, { message: '等级不能大于100' })
  level?: number;

  @ApiPropertyOptional({ description: '经验值（可选）', example: 1500 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '经验值必须是数字' })
  @Min(0, { message: '经验值不能小于0' })
  exp?: number;
}

/**
 * 获取球员列表Payload DTO
 * @MessagePattern('hero.getList')
 * 扩展GetHeroListDto，合并BasePayloadDto内容
 */
export class GetListPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '角色ID（可选）', example: 'char_12345' })
  @Expose()
  @IsOptional()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId?: string;

  @ApiPropertyOptional({ description: '页码（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number;

  @ApiPropertyOptional({ description: '每页数量（可选）', example: 20 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能大于100' })
  limit?: number;

  @ApiPropertyOptional({ description: '球员位置（可选）', enum: HeroPosition })
  @Expose()
  @IsOptional()
  @IsEnum(HeroPosition, { message: '球员位置必须是有效的枚举值' })
  position?: HeroPosition;

  @ApiPropertyOptional({ description: '品质过滤' })
  @IsOptional()
  @IsEnum(HeroQuality)
  quality?: HeroQuality;

  @ApiPropertyOptional({ description: '是否在阵容中' })
  @IsOptional()
  @IsBoolean()
  isInFormation?: boolean;

  @ApiPropertyOptional({ description: '是否在市场上' })
  @IsOptional()
  @IsBoolean()
  isOnMarket?: boolean;

  @ApiPropertyOptional({ description: '最小等级（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '最小等级必须是数字' })
  @Min(1, { message: '最小等级不能小于1' })
  minLevel?: number;

  @ApiPropertyOptional({ description: '最大等级（可选）', example: 100 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '最大等级必须是数字' })
  @Min(1, { message: '最大等级不能小于1' })
  @Max(100, { message: '最大等级不能大于100' })
  maxLevel?: number;

  @ApiPropertyOptional({ description: '排序字段' })
  @IsOptional()
  @IsString()
  sortBy?: 'level' | 'overallRating' | 'obtainTime' | 'marketValue';

  @ApiPropertyOptional({ description: '排序方向' })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';
}

// ==================== 4. 球员升级和技能相关 ====================

/**
 * 球员升级Payload DTO
 * @MessagePattern('hero.levelUp')
 * 扩展LevelUpHeroDto，合并BasePayloadDto内容
 */
export class LevelUpPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiPropertyOptional({ description: '目标等级（可选）', example: 10 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '目标等级必须是数字' })
  @Min(1, { message: '目标等级不能小于1' })
  @Max(100, { message: '目标等级不能大于100' })
  targetLevel?: number;

  @ApiPropertyOptional({ description: '是否使用道具（可选）', example: false })
  @Expose()
  @IsOptional()
  @IsBoolean({ message: '是否使用道具必须是布尔值' })
  useItems?: boolean;
}

/**
 * 技能升级Payload DTO
 * @MessagePattern('hero.skill.upgrade')
 * 扩展UpgradeSkillDto，合并BasePayloadDto内容
 */
export class SkillUpgradePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '技能ID', example: 'skill_001' })
  @Expose()
  @IsString({ message: '技能ID必须是字符串' })
  @Length(1, 50, { message: '技能ID长度必须在1-50个字符之间' })
  skillId: string;

  @ApiPropertyOptional({ description: '升级等级（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '升级等级必须是数字' })
  @Min(1, { message: '升级等级不能小于1' })
  @Max(10, { message: '升级等级不能大于10' })
  upgradeLevel?: number;
}

// ==================== 5. 市场和阵容相关 ====================

/**
 * 市场操作Payload DTO
 * @MessagePattern('hero.market.operation')
 * 扩展MarketOperationDto，合并BasePayloadDto内容
 */
export class MarketOperationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '操作类型', example: 'sell', enum: ['sell', 'buy', 'cancel'] })
  @Expose()
  @IsString({ message: '操作类型必须是字符串' })
  @Length(1, 20, { message: '操作类型长度必须在1-20个字符之间' })
  operation: string;

  @ApiPropertyOptional({ description: '价格（可选）', example: 1000000 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '价格必须是数字' })
  @Min(1, { message: '价格不能小于1' })
  @Max(999999999, { message: '价格不能大于999999999' })
  price?: number;
}

/**
 * 获取阵容球员Payload DTO
 * @MessagePattern('hero.getFormation')
 */
export class GetFormationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 获取市场球员Payload DTO
 * @MessagePattern('hero.getMarket')
 */
export class GetMarketPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '页码（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number;

  @ApiPropertyOptional({ description: '每页数量（可选）', example: 20 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能大于100' })
  limit?: number;
}

/**
 * 获取球员统计Payload DTO
 * @MessagePattern('hero.getStats')
 */
export class GetStatsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

// ==================== 6. 球员进化和突破相关 ====================

/**
 * 获取升星需求Payload DTO
 * @MessagePattern('hero.evolution.requirements')
 */
export class GetEvolutionRequirementsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 球员升星Payload DTO
 * @MessagePattern('hero.evolve')
 * 扩展EvolveHeroDto，合并BasePayloadDto内容
 */
export class EvolvePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiPropertyOptional({ description: '使用的材料球员ID列表（可选）', type: [String] })
  @Expose()
  @IsOptional()
  @IsArray({ message: '材料球员ID列表必须是数组' })
  @IsString({ each: true, message: '材料球员ID必须是字符串' })
  @Length(1, 50, { each: true, message: '材料球员ID长度必须在1-50个字符之间' })
  materialHeroIds?: string[];

  @ApiPropertyOptional({ description: '是否使用保护道具（锁定成功）' })
  @Expose()
  @IsOptional()
  @IsBoolean({ message: '是否使用保护道具必须是布尔值' })
  useProtection?: boolean;

  @ApiPropertyOptional({ description: '是否使用万能卡' })
  @Expose()
  @IsOptional()
  @IsBoolean({ message: '是否使用万能卡必须是布尔值' })
  useUniversalCard?: boolean;

  @ApiPropertyOptional({ description: '使用的道具列表' })
  @Expose()
  @IsOptional()
  @IsArray({ message: '道具列表必须是数组' })
  items?: Array<{
    itemId: string;
    count: number;
  }>;
}

/**
 * 获取球员状态Payload DTO
 * @MessagePattern('hero.getStatus')
 */
export class GetStatusPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 治疗球员Payload DTO
 * @MessagePattern('hero.treat')
 */
export class TreatPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiPropertyOptional({ description: '治疗类型（可选）', example: 'normal', enum: ['normal', 'advanced', 'premium'] })
  @Expose()
  @IsOptional()
  @IsString({ message: '治疗类型必须是字符串' })
  @Length(1, 20, { message: '治疗类型长度必须在1-20个字符之间' })
  treatmentType?: string;
}

/**
 * 球员续约Payload DTO
 * @MessagePattern('hero.renewContract')
 */
export class RenewContractPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '合约天数', example: 365 })
  @Expose()
  @IsNumber({}, { message: '合约天数必须是数字' })
  @Min(1, { message: '合约天数不能小于1' })
  @Max(3650, { message: '合约天数不能大于3650（10年）' })
  contractDays: number;
}

/**
 * 恢复球员疲劳Payload DTO
 * @MessagePattern('hero.recoverFatigue')
 */
export class RecoverFatiguePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiPropertyOptional({ description: '恢复值（可选）', example: 20 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '恢复值必须是数字' })
  @Min(1, { message: '恢复值不能小于1' })
  @Max(100, { message: '恢复值不能大于100' })
  recoveryValue?: number;
}

// ==================== 7. 突破相关 ====================

/**
 * 获取突破信息Payload DTO
 * @MessagePattern('hero.breakthrough.info')
 */
export class GetBreakthroughInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 球员突破Payload DTO
 * @MessagePattern('hero.breakthrough')
 */
export class BreakthroughPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 撤销突破Payload DTO
 * @MessagePattern('hero.breakthrough.revert')
 */
export class RevertBreakthroughPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

// ==================== 8. 属性计算和生涯管理相关 ====================

/**
 * 重新计算球员属性Payload DTO
 * @MessagePattern('hero.recalculateAttributes')
 */
export class RecalculateAttributesPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 批量重新计算球员属性Payload DTO
 * @MessagePattern('hero.recalculateAllAttributes')
 */
export class RecalculateAllAttributesPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 球员续约生涯Payload DTO
 * @MessagePattern('hero.renewCareer')
 */
export class RenewCareerPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiPropertyOptional({ description: '续约天数（可选，默认365天）', example: 365 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '续约天数必须是数字' })
  @Min(1, { message: '续约天数不能小于1' })
  @Max(3650, { message: '续约天数不能大于3650（10年）' })
  days?: number;
}

/**
 * 获取球员生涯信息Payload DTO
 * @MessagePattern('hero.getCareerInfo')
 */
export class GetCareerInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 检查合约到期Payload DTO
 * @MessagePattern('hero.checkContractExpiration')
 */
export class CheckContractExpirationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}

/**
 * 处理球员退役Payload DTO
 * @MessagePattern('hero.processRetirement')
 */
export class ProcessRetirementPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  characterId: string;
}
