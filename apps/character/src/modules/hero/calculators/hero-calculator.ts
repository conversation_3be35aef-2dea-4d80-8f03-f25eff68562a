/**
 * 球员计算器
 * 从HeroCultivationService中剥离的计算函数
 * 基于old项目的计算逻辑，无外部依赖
 */

/**
 * 属性计算器
 * 基于old项目的属性计算逻辑
 */
export class AttributeCalculator {
  /**
   * 计算升星属性加成
   * 基于old项目: _InnerCalcUpgradeStarValue
   */
  static calculateStarAttributeBonus(hero: any): any {
    const star = hero.star || 0;
    const bonus = star * 2; // 每星+2属性（简化计算）

    return {
      speed: bonus,
      shooting: bonus,
      passing: bonus,
      defending: bonus,
      dribbling: bonus,
      physicality: bonus,
      goalkeeping: bonus,
    };
  }

  /**
   * 计算特训属性加成
   * 基于old项目的特训加成计算逻辑
   */
  static calculateTrainingAttributeBonus(hero: any): any {
    try {
      const training = hero.training || {};

      // 基于新架构：使用训练阶段信息计算特训加成
      // 每个属性的训练阶段越高，加成越大
      const trainingBonus = {
        speed: (training.speed || 0) * 5,
        shooting: (training.shooting || 0) * 5,
        passing: (training.passing || 0) * 5,
        defending: (training.defending || 0) * 5,
        dribbling: (training.dribbling || 0) * 5,
        physicality: (training.physicality || 0) * 5,
        goalkeeping: (training.goalkeeping || 0) * 5,
      };

      return trainingBonus;
    } catch (error) {
      // 计算失败时返回零加成
      return {
        speed: 0, shooting: 0, passing: 0, defending: 0,
        dribbling: 0, physicality: 0, goalkeeping: 0,
      };
    }
  }

  /**
   * 计算突破属性加成
   * 基于old项目: breakthrough相关计算
   */
  static calculateBreakthroughAttributeBonus(hero: any): any {
    const breakthrough = hero.breakthrough || [];
    const totalBreakthrough = breakthrough.reduce((sum, value) => sum + value, 0);

    // 突破值平均分配到各属性
    const bonus = Math.floor(totalBreakthrough / 7);

    return {
      speed: bonus, shooting: bonus, passing: bonus, defending: bonus,
      dribbling: bonus, physicality: bonus, goalkeeping: bonus,
    };
  }

  /**
   * 计算一级属性（基础属性）
   * 基于old项目: calcOneLevelAttr
   */
  static calculatePrimaryAttributes(hero: any, bonuses: any): any {
    try {
      const baseAttributes = hero.attributes || {};
      const starBonus = this.calculateStarAttributeBonus(hero);
      const trainingBonus = this.calculateTrainingAttributeBonus(hero);
      const breakthroughBonus = this.calculateBreakthroughAttributeBonus(hero);

      // 累加所有加成
      const primaryAttributes = {};
      const attributeNames = ['speed', 'shooting', 'passing', 'defending', 'dribbling', 'physicality', 'goalkeeping'];

      for (const attr of attributeNames) {
        primaryAttributes[attr] = 
          (baseAttributes[attr] || 0) +
          (starBonus[attr] || 0) +
          (trainingBonus[attr] || 0) +
          (breakthroughBonus[attr] || 0) +
          (bonuses.formation?.[attr] || 0) +
          (bonuses.coach?.[attr] || 0) +
          (bonuses.beliefSkill?.[attr] || 0);
      }

      return primaryAttributes;
    } catch (error) {
      // 计算失败时返回基础属性
      return hero.attributes || {};
    }
  }

  /**
   * 计算二级属性（进攻值、防守值）
   * 基于old项目的二级属性计算逻辑
   */
  static calculateSecondaryAttributes(primaryAttributes: any, hero: any): any {
    try {
      const position = hero.position || 'ST'; // 默认前锋位置

      // 基于old项目：根据Attr.json配置计算进攻/防守值
      // 不同位置有不同的属性权重
      const positionWeights = this.getPositionWeights(position);

      const attackValue = Math.floor(
        (primaryAttributes.speed || 0) * positionWeights.attack.speed +
        (primaryAttributes.shooting || 0) * positionWeights.attack.shooting +
        (primaryAttributes.passing || 0) * positionWeights.attack.passing +
        (primaryAttributes.dribbling || 0) * positionWeights.attack.dribbling
      );

      const defendValue = Math.floor(
        (primaryAttributes.defending || 0) * positionWeights.defend.defending +
        (primaryAttributes.physicality || 0) * positionWeights.defend.physicality +
        (primaryAttributes.speed || 0) * positionWeights.defend.speed
      );

      return {
        attackValue,
        defendValue,
        // 保留一级属性
        ...primaryAttributes
      };
    } catch (error) {
      // 计算失败时返回默认值
      return {
        attackValue: 50,
        defendValue: 50,
        ...primaryAttributes
      };
    }
  }

  /**
   * 获取位置权重配置
   * 基于old项目的Attr.json配置
   */
  static getPositionWeights(position: string): any {
    const weights = {
      'GK': { // 门将
        attack: { speed: 0.1, shooting: 0.1, passing: 0.3, dribbling: 0.1 },
        defend: { defending: 0.4, physicality: 0.3, speed: 0.2 }
      },
      'DC': { // 中后卫
        attack: { speed: 0.2, shooting: 0.1, passing: 0.4, dribbling: 0.1 },
        defend: { defending: 0.5, physicality: 0.3, speed: 0.2 }
      },
      'ST': { // 前锋
        attack: { speed: 0.3, shooting: 0.4, passing: 0.2, dribbling: 0.3 },
        defend: { defending: 0.2, physicality: 0.3, speed: 0.2 }
      },
      'MC': { // 中场
        attack: { speed: 0.2, shooting: 0.2, passing: 0.4, dribbling: 0.3 },
        defend: { defending: 0.3, physicality: 0.2, speed: 0.3 }
      }
    };

    return weights[position] || weights['ST']; // 默认使用前锋权重
  }
}

/**
 * 实力评分计算器
 * 基于old项目的实力和评分计算逻辑
 */
export class PowerRatingCalculator {
  /**
   * 计算球员总实力
   * 基于old项目的实力计算公式
   */
  static calculateTotalPower(attributes: any): number {
    const {
      speed = 0,
      shooting = 0,
      passing = 0,
      defending = 0,
      dribbling = 0,
      physicality = 0,
      goalkeeping = 0
    } = attributes;

    // 基于old项目的实力计算公式
    const totalPower = Math.floor(
      (speed + shooting + passing + defending + dribbling + physicality + goalkeeping) / 7
    );

    return Math.max(totalPower, 1); // 最低实力为1
  }

  /**
   * 计算球员综合评分
   * 基于old项目的评分算法
   */
  static calculateOverallRating(attributes: any, level: number): number {
    const totalPower = this.calculateTotalPower(attributes);
    const levelBonus = level * 2; // 等级加成
    const rating = Math.floor((totalPower + levelBonus) / 10);

    return Math.min(Math.max(rating, 1), 100); // 评分范围1-100
  }
}
