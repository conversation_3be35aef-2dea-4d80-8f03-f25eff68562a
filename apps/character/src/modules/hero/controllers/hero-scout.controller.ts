import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CacheEvict, Cacheable } from '@libs/redis';
import { HeroScoutService } from '../services/hero-scout.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  BuyEnergyPayloadDto,
  DeletePackHeroPayloadDto,
  ExchangeScoutPayloadDto,
  ExplorePayloadDto,
  GetPackInfoPayloadDto,
  RecoverEnergyPayloadDto,
  SearchPayloadDto,
  SignHeroPayloadDto
} from "@character/common/dto/scout-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

/**
 * 球探系统控制器
 * 基于old项目的球探相关功能
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class HeroScoutController extends BaseController {
  constructor(private readonly heroScoutService: HeroScoutService) {
    super('HeroScoutController');
  }

  /**
   * 获取球探包信息
   * 对应old项目: getScoutPackInfo
   */
  @MessagePattern('scout.getPackInfo')
  @Cacheable({
    key: 'character:scout:pack:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getScoutPackInfo(@Payload() payload: GetPackInfoPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球探包信息: ${payload.characterId}`);
    const result = await this.heroScoutService.getScoutPackInfo(payload.characterId);
    return this.fromResult(result);
  }

  /**
   * 删除球探包中的球员
   * 对应old项目: delScoutPackHero（支持批量删除）
   */
  @MessagePattern('scout.deletePackHero')
  @CacheEvict({
    key: 'character:scout:pack:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deleteScoutPackHero(@Payload() payload: DeletePackHeroPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`删除球探包球员: ${payload.characterId}, 索引: ${JSON.stringify(payload.index)}`);
    const result = await this.heroScoutService.deleteScoutPackHero(payload.characterId, payload.index);
    return this.fromResult(result);
  }

  /**
   * 签约球探球员
   * 对应old项目: signScoutHero（使用resId而不是index）
   */
  @MessagePattern('scout.signHero')
  @CacheEvict({
    key: 'character:scout:pack:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async signScoutHero(@Payload() payload: SignHeroPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`签约球探球员: ${payload.characterId}, 配置ID: ${payload.resId}`);
    const result = await this.heroScoutService.signScoutHero(payload.characterId, payload.resId);
    return this.fromResult(result);
  }

  /**
   * 球探探索（核心功能）
   * 对应old项目: getScoutReward
   */
  @MessagePattern('scout.explore')
  @CacheEvict({
    key: 'character:scout:pack:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getScoutReward(@Payload() payload: ExplorePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球探探索: ${payload.characterId}, 类型: ${payload.type}`);
    const result = await this.heroScoutService.getScoutReward(payload.characterId, payload.type);
    return this.fromResult(result);
  }

  /**
   * 球探搜索
   * 基于old项目扩展的球探搜索功能
   */
  @MessagePattern('scout.search')
  async scoutSearch(@Payload() payload: SearchPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球探搜索: ${payload.characterId}, 类型: ${payload.scoutType}`);
    const result = await this.heroScoutService.scoutSearch(payload.characterId, payload.scoutType, payload.targetQuality);
    return this.fromResult(result);
  }

  /**
   * 球探RP值兑换
   * 基于old项目Scout.exchangeScout方法
   */
  @MessagePattern('scout.exchangeScout')
  async exchangeScout(@Payload() payload: ExchangeScoutPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球探RP值兑换: ${payload.characterId}, 类型: ${payload.type || 3}`);
    const result = await this.heroScoutService.exchangeScout(payload.characterId, payload.type || 3);
    return this.fromResult(result);
  }

  /**
   * 购买球探体力
   * 基于old项目的体力购买机制
   */
  @MessagePattern('scout.buyEnergy')
  async buyScoutEnergy(@Payload() payload: BuyEnergyPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`购买球探体力: ${payload.characterId}, 数量: ${payload.amount || 50}`);
    const result = await this.heroScoutService.buyScoutEnergy(payload.characterId, payload.amount || 50);
    return this.fromResult(result);
  }

  /**
   * 手动恢复球探体力
   * 基于old项目的体力恢复机制
   */
  @MessagePattern('scout.recoverEnergy')
  async recoverScoutEnergy(@Payload() payload: RecoverEnergyPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`手动恢复球探体力: ${payload.characterId}`);
    const result = await this.heroScoutService.recoverScoutEnergy(payload.characterId);
    return this.fromResult(result);
  }
}
