import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { HeroService } from '../services/hero.service';

import { HeroPosition } from '@libs/game-constants';
import { <PERSON>ache<PERSON>, CacheEvict, CachePut } from '@libs/redis';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import { BaseController } from '@libs/common/controller';

import {
  BreakthroughPayloadDto, 
  CheckContractExpirationPayloadDto,
  CreatePayloadDto,
  EvolvePayloadDto,
  GetBatchPayloadDto,
  GetBreakthroughInfoPayloadDto, 
  GetCareerInfoPayloadDto,
  GetConfigByPositionPayloadDto,
  GetConfigPayloadDto,
  GetEvolutionRequirementsPayloadDto,
  GetInfoPayloadDto,
  GetListPayloadDto,
  GetMarketPayloadDto,
  GetStatsPayloadDto,
  GetStatusPayloadDto, 
  MarketOperationPayloadDto, 
  ProcessRetirementPayloadDto,
  RecalculateAllAttributesPayloadDto,
  RecalculateAttributesPayloadDto,
  RecoverFatiguePayloadDto, RenewCareerPayloadDto,
  RenewContractPayloadDto,
  RevertBreakthroughPayloadDto,
  SetTreatStatusPayloadDto,
  SkillUpgradePayloadDto,
  TreatPayloadDto,
  UpdatePayloadDto,
  GetFormationPayloadDto,
  UpdateFatiguePayloadDto,
  LevelUpPayloadDto
} from "@character/common/dto/hero-payload.dto";

import { XResponse } from '@libs/common/types/result.type';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class HeroController extends BaseController {
  protected readonly logger = new Logger(HeroController.name);

  constructor(private readonly heroService: HeroService) {
    super('HeroController');
  }

  // ==================== 球员配置管理 ====================

  /**
   * 获取球员配置信息
   */
  @MessagePattern('hero.getConfig')
  @Cacheable({
    key: 'hero:config:#{payload.heroId}',
    dataType: 'global',
    ttl: 3600
  })
  async getHeroConfig(@Payload() payload: GetConfigPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员配置信息: ${payload.heroId}`);
    const result = await this.heroService.getHeroConfig(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 根据位置获取球员配置
   */
  @MessagePattern('hero.getConfigByPosition')
  @Cacheable({
    key: 'hero:config:position:#{payload.position}:#{payload.limit}',
    dataType: 'global',
    ttl: 1800
  })
  async getHeroConfigsByPosition(@Payload() payload: GetConfigByPositionPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`根据位置获取球员配置: ${payload.position}`);
    const result = await this.heroService.getHeroConfigsByPosition(payload.position, payload.limit);
    return this.fromResult(result);
  }

  // ==================== 球员状态管理 ====================

  /**
   * 设置球员治疗状态
   */
  @MessagePattern('hero.setTreatStatus')
  async setHeroTreatStatus(@Payload() payload: SetTreatStatusPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`设置球员治疗状态: ${payload.heroId}, 治疗中: ${payload.isTreat}`);
    const result = await this.heroService.setHeroTreatStatus(payload.heroId, payload.isTreat);
    return this.fromResult(result);
  }

  /**
   * 更新球员疲劳值
   */
  @MessagePattern('hero.updateFatigue')
  async updateHeroFatigue(@Payload() payload: UpdateFatiguePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新球员疲劳值: ${payload.heroId}, 变化: ${payload.fatigueChange}`);
    const result = await this.heroService.updateHeroFatigue(payload.heroId, payload.fatigueChange);
    return this.fromResult(result);
  }

  // ==================== 球员实例管理 ====================

  /**
   * 创建新球员
   */
  @MessagePattern('hero.create')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createHero(@Payload() payload: CreatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`创建球员请求: ${JSON.stringify(payload)}`);
    const result = await this.heroService.createHero(payload);
    return this.fromResult(result);
  }

  /**
   * 批量获取球员信息
   */
  @MessagePattern('hero.getBatch')
  async getBatchHeroes(@Payload() payload: GetBatchPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量获取球员信息: ${payload.heroIds.length}个球员`);
    const result = await this.heroService.getBatchHeroes(payload.heroIds);
    return this.fromResult(result);
  }

  /**
   * 获取球员信息
   */
  @MessagePattern('hero.getInfo')
  @Cacheable({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getHeroInfo(@Payload() payload: GetInfoPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员信息: ${payload.heroId}`);
    const result = await this.heroService.getHeroInfo(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 更新球员信息
   */
  @MessagePattern('hero.update')
  @CachePut({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async updateHero(@Payload() payload: UpdatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新球员信息: ${payload.heroId}`);
    const result = await this.heroService.updateHero(payload.heroId, payload);
    return this.fromResult(result);
  }

  /**
   * 获取球员列表
   */
  @MessagePattern('hero.getList')
  // TODO 条件查询分析缓存必要性
  // @Cacheable({
  //   key: 'character:heroes:#{payload.characterId}:#{payload.page}:#{payload.limit}',
  //   dataType: 'server',
  //   serverId: '#{payload.serverId}',
  //   ttl: 1800
  // })
  async getHeroList(@Payload() payload: GetListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员列表: ${JSON.stringify(payload)}`);
    const result = await this.heroService.getHeroList(payload);
    return this.fromResult(result);
  }

  /**
   * 球员升级
   */
  @MessagePattern('hero.levelUp')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async levelUpHero(@Payload() payload: LevelUpPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`球员升级: ${payload.heroId}`);
      // TODO: 实现球员升级逻辑，使用Result模式
      // const result = await this.heroService.levelUpHero(payload);
      // return this.fromResult(result);
      // 临时实现
      return this.toSuccessResponse({
        leveledUp: true,
        heroId: payload.heroId,
        oldLevel: 1,
        newLevel: 2,
        attributeIncrease: {
          speed: 1,
          shooting: 1,
          passing: 1,
          defending: 1,
          dribbling: 1,
          physicality: 1,
          goalkeeping: 1,
        },
        expCost: 100,
        remainingExp: 0,
      }, '升级成功');
    }, payload);
  }

  /**
   * 技能升级
   */
  @MessagePattern('hero.skill.upgrade')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async upgradeSkill(@Payload() payload: SkillUpgradePayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`技能升级: ${payload.heroId}, 技能: ${payload.skillId}`);
      // TODO: 实现技能升级逻辑，使用Result模式
      // const result = await this.heroService.upgradeSkill(payload);
      // return this.fromResult(result);
      // 临时实现
      return this.toSuccessResponse({
        heroId: payload.heroId,
        skillId: payload.skillId,
        oldLevel: 1,
        newLevel: 2,
        cost: 1000,
      }, '技能升级成功');
    }, payload);
  }

  /**
   * 市场操作
   */
  @MessagePattern('hero.market.operation')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async marketOperation(@Payload() payload: MarketOperationPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`市场操作: ${payload.heroId}, 操作: ${payload.operation}`);
      // TODO: 实现市场操作逻辑，使用Result模式
      // const result = await this.heroService.marketOperation(payload);
      // return this.fromResult(result);
      // 临时实现
      return this.toSuccessResponse({
        heroId: payload.heroId,
        operation: payload.operation,
        price: payload.price,
        success: true,
      }, '操作成功');
    }, payload);
  }

  /**
   * 获取阵容中的球员
   */
  @MessagePattern('hero.getFormation')
  @Cacheable({
    key: 'character:formation:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getFormationHeroes(@Payload() payload: GetFormationPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取阵容球员: ${payload.characterId}`);
      // TODO: 实现获取阵容球员逻辑，使用Result模式
      // const result = await this.heroService.getFormationHeroes(payload);
      // return this.fromResult(result);
      // 临时实现
      return this.toSuccessResponse({
        characterId: payload.characterId,
        heroes: [],
        formation: '4-4-2',
      }, '获取成功');
    }, payload);
  }

  /**
   * 获取市场球员
   */
  @MessagePattern('hero.getMarket')
  @Cacheable({
    key: 'server:market:heroes:#{payload.page}:#{payload.limit}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getMarketHeroes(@Payload() payload: GetMarketPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取市场球员: 页码${payload.page || 1}`);
      // TODO: 实现获取市场球员逻辑，使用Result模式
      // const result = await this.heroService.getMarketHeroes(payload);
      // return this.fromResult(result);
      // 临时实现
      return this.toSuccessResponse({
        list: [],
        total: 0,
        page: payload.page || 1,
        limit: payload.limit || 20,
      }, '获取成功');
    }, payload);
  }

  /**
   * 获取球员统计
   * 基于old项目: 统计角色的球员数据
   */
  @MessagePattern('hero.getStats')
  @Cacheable({
    key: 'character:hero:stats:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getHeroStats(@Payload() payload: GetStatsPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取球员统计: ${payload.characterId}`);
      const result = await this.heroService.getCharacterHeroStats(payload.characterId);
      return this.fromResult(result);
    }, payload);
  }
}
