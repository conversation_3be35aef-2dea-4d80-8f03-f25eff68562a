import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { HeroSkillService } from '../services/hero-skill.service';

import { SkillPosition } from '@character/common/types';
import { Cacheable, CacheEvict } from '@libs/redis';

import { XResponse } from '@libs/common/types/result.type';

import {
  ActivatePayloadDto, BatchOperationPayloadDto,
  DeactivatePayloadDto,
  GetActivePayloadDto,
  GetConfigByPositionPayloadDto,
  GetConfigListPayloadDto,
  GetConfigPayloadDto,
  GetListPayloadDto, GetStatsPayloadDto,
  LearnPayloadDto, ResetPayloadDto,
  UpgradePayloadDto, UsePayloadDto
} from "@character/modules/hero/dto/skill-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class HeroSkillController extends BaseController {
  constructor(private readonly heroSkillService: HeroSkillService) {
    super('HeroSkillController');
  }

  // ==================== 球员技能管理 ====================

  /**
   * 球员学习技能
   */
  @MessagePattern('skill.learn')
  @CacheEvict({ 
    key: 'hero:skills:#{payload.learnDto.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async learnSkill(@Payload() payload: LearnPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球员学习技能: ${JSON.stringify(payload)}`);
    const result = await this.heroSkillService.learnSkill(payload);
    return this.fromResult(result);
  }

  /**
   * 升级球员技能
   */
  @MessagePattern('skill.upgrade')
  @CacheEvict({ 
    key: 'hero:skill:#{payload.upgradeDto.skillId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async upgradeSkill(@Payload() payload: UpgradePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`升级球员技能: ${JSON.stringify(payload)}`);
    const result = await this.heroSkillService.upgradeSkill(payload);
    return this.fromResult(result);
  }

  /**
   * 激活球员技能
   */
  @MessagePattern('skill.activate')
  @CacheEvict({ 
    key: 'hero:skills:active:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async activateSkill(@Payload() payload: ActivatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`激活球员技能: ${JSON.stringify(payload)}`);
    const result = await this.heroSkillService.activateSkill(payload);
    return this.fromResult(result);
  }

  /**
   * 取消激活球员技能
   */
  @MessagePattern('skill.deactivate')
  @CacheEvict({ 
    key: 'hero:skills:active:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deactivateSkill(@Payload() payload: DeactivatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`取消激活球员技能: ${payload.skillId}`);
    const result = await this.heroSkillService.deactivateSkill(payload.skillId);
    return this.fromResult(result);
  }

  /**
   * 获取球员技能列表
   */
  @MessagePattern('skill.getList')
  @Cacheable({ 
    key: 'hero:skills:#{payload.heroId}:#{payload.activeStatus}:#{payload.equippedOnly}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getSkillList(@Payload() payload: GetListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员技能列表: ${JSON.stringify(payload)}`);
    const result = await this.heroSkillService.getSkillList(payload);
    return this.fromResult(result);
  }

  /**
   * 获取球员已激活的技能
   */
  @MessagePattern('skill.getActive')
  @Cacheable({ 
    key: 'hero:skills:active:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getActiveSkills(@Payload() payload: GetActivePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员已激活技能: ${payload.heroId}`);
    const result = await this.heroSkillService.getActiveSkills(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 重置球员技能
   */
  @MessagePattern('skill.reset')
  @CacheEvict({ 
    key: 'hero:skills:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async resetSkills(@Payload() payload: ResetPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`重置球员技能: ${payload.heroId}, 类型: ${payload.resetType}`);
    // TODO: 实现重置球员技能逻辑
    return this.toSuccessResponse(undefined);
  }

  /**
   * 批量操作球员技能
   */
  @MessagePattern('skill.batchOperation')
  @CacheEvict({ 
    key: 'hero:skills:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchOperateSkills(@Payload() payload: BatchOperationPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量操作球员技能: ${payload.operation}, 数量: ${payload.skillIds.length}`);
    // TODO: 实现批量操作球员技能逻辑
    return this.toSuccessResponse(undefined);
  }

  /**
   * 获取技能统计
   */
  @MessagePattern('skill.getStats')
  @Cacheable({ 
    key: 'hero:skill:stats:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getSkillStats(@Payload() payload: GetStatsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取技能统计: ${payload.heroId}`);
    const stats = await this.heroSkillService.getSkillStats(payload.heroId);
    return this.toSuccessResponse(stats);
  }

  /**
   * 使用技能
   */
  @MessagePattern('skill.use')
  async useSkill(@Payload() payload: UsePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`使用技能: ${payload.skillId}, 球员: ${payload.heroId}`);
    // TODO: 实现使用技能逻辑
    return this.toSuccessResponse({
      skillId: payload.skillId,
      heroId: payload.heroId,
      damage: 0,
      healing: 0,
      effects: [],
    });
  }
}
