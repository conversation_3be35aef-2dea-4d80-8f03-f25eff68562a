import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CacheEvict, Cacheable } from '@libs/redis';
import { HeroCareerService } from '../services/hero-career.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  AddContractDaysPayloadDto,
  AddToRetirementPayloadDto, GetExpiringContractsPayloadDto, GetStatsPayloadDto,
  ProcessRetirementPayloadDto,
  PromoteStatusPayloadDto,
  RenewContractPayloadDto
} from "@character/common/dto/career-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

/**
 * 球员生涯管理控制器
 * 基于old项目的生涯相关功能
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class HeroCareerController extends BaseController {
  constructor(
    private readonly heroCareerService: HeroCareerService
  ) {
    super('HeroCareerController');
  }

  /**
   * 增加球员合约天数
   * 对应old项目: addHeroLeftDay
   */
  @MessagePattern('career.addContractDays')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addHeroLeftDay(@Payload() payload: AddContractDaysPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`增加球员合约天数: ${payload.heroId}, 天数: ${payload.days}`);
    const result = await this.heroCareerService.addHeroLeftDay(payload.heroId, payload.days);
    return this.fromResult(result);
  }

  /**
   * 续约球员
   * 对应old项目: renewTheContract（支持批量续约）
   */
  @MessagePattern('career.renewContract')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async renewTheContract(@Payload() payload: RenewContractPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`续约球员: ${JSON.stringify(payload.heroIds)}`);
    const result = await this.heroCareerService.renewTheContract(payload.heroIds);
    return this.fromResult(result);
  }

  /**
   * 提升球员状态
   * 对应old项目: promoteHeroStatus（使用道具）
   */
  @MessagePattern('career.promoteStatus')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async promoteHeroStatus(@Payload() payload: PromoteStatusPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`提升球员状态: ${payload.heroId}, 道具ID: ${payload.itemId}`);
    const result = await this.heroCareerService.promoteHeroStatus(payload.heroId, payload.itemId);
    return this.fromResult(result);
  }

  /**
   * 加入退役名单
   * 对应old项目: addHeroToRetirementList
   */
  @MessagePattern('career.addToRetirement')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addHeroToRetirementList(@Payload() payload: AddToRetirementPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`加入退役名单: ${payload.heroId}`);
    const result = await this.heroCareerService.addHeroToRetirementList(payload.heroId, payload.characterId);
    return this.fromResult(result);
  }

  /**
   * 处理球员退役
   * 检查并处理到期的球员
   */
  @MessagePattern('career.processRetirement')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async processRetirement(@Payload() payload: ProcessRetirementPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`处理球员退役: ${payload.characterId}`);
    const result = await this.heroCareerService.checkAndProcessRetirement(payload.characterId);
    return this.fromResult(result);
  }

  /**
   * 获取球员生涯统计
   */
  @MessagePattern('career.getStats')
  @Cacheable({
    key: 'hero:career:stats:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600
  })
  async getCareerStats(@Payload() payload: GetStatsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员生涯统计: ${payload.heroId}`);
    const stats = await this.heroCareerService.getCareerStats(payload.heroId);
    return this.fromResult(stats);
  }

  /**
   * 获取即将到期的球员列表
   */
  @MessagePattern('career.getExpiringContracts')
  @Cacheable({
    key: 'character:expiring:contracts:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getExpiringContracts(@Payload() payload: GetExpiringContractsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取即将到期的球员: ${payload.characterId}, ${payload.days}天内`);
    const expiringHeroes = await this.heroCareerService.getExpiringContracts(payload.characterId, payload.days);
    return this.fromResult(expiringHeroes);
  }
}
