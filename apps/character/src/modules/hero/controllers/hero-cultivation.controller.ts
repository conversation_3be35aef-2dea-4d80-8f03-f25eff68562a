import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CacheEvict, Cacheable } from '@libs/redis';
import { HeroCultivationService } from '../services/hero-cultivation.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  BreakOutPayloadDto,
  CultivatePayloadDto, OneKeyCultivatePayloadDto,
  ReBreakOutPayloadDto,
  UpStarPayloadDto
} from "@character/common/dto/cultivation-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
/**
 * 球员养成控制器
 * 基于old项目的养成相关功能
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class HeroCultivationController extends BaseController {
  constructor(private readonly heroCultivationService: HeroCultivationService) {
    super('HeroCultivationController');
  }

  /**
   * 球员养成
   * 对应old项目: cultivateHero
   */
  @MessagePattern('cultivation.cultivate')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async cultivateHero(@Payload() payload: CultivatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球员养成: ${payload.heroId}`);
    const result = await this.heroCultivationService.cultivateHero(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 球员突破
   * 对应old项目: breakOutHero
   */
  @MessagePattern('cultivation.breakOut')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async breakOutHero(@Payload() payload: BreakOutPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球员突破: ${payload.heroId}, 次数: ${payload.index}`);
    const result = await this.heroCultivationService.breakOutHero(payload.heroId, payload.index, payload.arr);
    return this.fromResult(result);
  }

  /**
   * 重新突破
   * 对应old项目: reBreakOutHero
   */
  @MessagePattern('cultivation.reBreakOut')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async reBreakOutHero(@Payload() payload: ReBreakOutPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`重新突破: ${payload.heroId}`);
    const result = await this.heroCultivationService.reBreakOutHero(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 球员升星
   * 对应old项目: heroUpStar
   */
  @MessagePattern('cultivation.upStar')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async heroUpStar(@Payload() payload: UpStarPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球员升星: ${payload.heroId}`);
    const result = await this.heroCultivationService.heroUpStar(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 一键养成
   * 对应old项目: oneKeyCultivateHero
   */
  @MessagePattern('cultivation.oneKeyCultivate')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async oneKeyCultivateHero(@Payload() payload: OneKeyCultivatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`一键养成: ${payload.heroId}, 类型: ${payload.type}`);
    const result = await this.heroCultivationService.oneKeyCultivateHero(payload.heroId, payload.type);
    return this.fromResult(result);
  }
}
