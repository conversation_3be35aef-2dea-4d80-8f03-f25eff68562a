import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { GameConfigFacade } from '@libs/game-config';
import { HeroRepository } from '@character/common/repositories/hero.repository';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { CharacterService } from '../../character/character.service';

import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 统一的球员养成业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 严格基于old项目的养成系统业务逻辑，提供完整的球员养成功能
 *
 * 🎯 核心功能：
 * - 球员培养（欧元/道具培养，支持多属性类型）
 * - 一键培养（智能连续培养直到阶段上限）
 * - 球员突破（1-10次突破，每次随机1-7点）
 * - 球员升星（需要球星卡，基于成功率系统）
 * - 重新突破（撤销非完美突破，支持钻石重置）
 * - 属性重新计算（图鉴、阵容、信仰技能加成）
 *
 * 🚀 性能优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的Result模式错误处理
 * - 微服务调用的错误处理和重试机制
 * - 智能缓存机制优化配置查询
 * - 批量操作减少数据库访问
 *
 * 🔗 服务依赖：
 * - CharacterService：货币扣除、物品消耗、图鉴数据、信仰技能（直接注入）
 * - Activity服务：任务触发、成就系统
 * - Formation服务：阵容加成计算、教练技能
 */
@Injectable()
export class HeroCultivationService extends BaseService {
  constructor(
    private readonly heroRepository: HeroRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly characterService: CharacterService,
  ) {
    super('HeroCultivationService');
  }

  /**
   * 球员养成
   * 严格基于old项目: cultivateHero(uid, type, index)
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   *
   * @param heroId 球员ID
   * @param type 属性类型 (1=速度, 2=射门, 3=传球)
   * @param index 培养类型 (1=欧元培养, 2=道具培养)
   */
  async cultivateHero(heroId: string, type: number = 1, index: number = 1): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`执行球员养成: ${heroId}, 类型: ${type}, 方式: ${index}`);

      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 获取球员配置
      const heroConfig = await this.gameConfig.hero.get(hero.resId);
      if (!heroConfig) {
        return XResultUtils.error('球员配置不存在', 'HERO_CONFIG_NOT_FOUND');
      }

      // 检查当前培养的所有属性是否已满
      const initPropertyResult = await this.getInitPropertyByType(type, hero.resId, hero.oldBreakOut || 30);
      if (XResultUtils.isFailure(initPropertyResult)) {
        return XResultUtils.error(`获取初始属性失败: ${initPropertyResult.message}`, initPropertyResult.code);
      }

      const initProperty = initPropertyResult.data;
      if (this.checkCultivateValueIsFull(hero.oneLevelAttr, initProperty)) {
        return XResultUtils.error('属性已达到最大值', 'ATTRIBUTE_MAX_REACHED');
      }

      // 获取属性阶段
      let stage = this.getCultivateStage(hero.oneLevelAttr, initProperty);
      if (stage > 9) {
        stage = 9;
      }

      // 获取培养配置
      const configResult = await this.getCultivateConfigByStage(hero.isStage[type - 1]);
      if (XResultUtils.isFailure(configResult)) {
        return XResultUtils.error(`获取培养配置失败: ${configResult.message}`, configResult.code);
      }

      const config = configResult.data;
      if (!config) {
        return XResultUtils.error('培养配置不存在', 'CULTIVATE_CONFIG_NOT_FOUND');
      }

      // 检查消耗
      let costResult;
      if (index === 1) { // 欧元培养
        costResult = await this.checkAndDeductMoney(hero.characterId, config.costMoney);
      } else { // 道具培养
        costResult = await this.checkAndDeductItems(hero.characterId, config.costItems);
      }

      if (XResultUtils.isFailure(costResult)) {
        return XResultUtils.error(`扣除费用失败: ${costResult.message}`, costResult.code);
      }

      // 执行培养逻辑
      const random = this.randomFrom(config.lowerLimit, config.upperLimit);
      const cultivationResult = this.innerCultivateHero(
        random,
        hero.oneLevelAttr,
        initProperty,
        stage,
        type,
        hero.isStage
      );

      // 更新球员数据
      const updateResult = await this.heroRepository.updateById(heroId, {
        oneLevelAttr: hero.oneLevelAttr,
        isStage: hero.isStage,
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员数据失败: ${updateResult.message}`, updateResult.code);
      }

      // 重新计算球员实力值
      const recalcResult = await this.reCalcAttrRevision(heroId);
      if (XResultUtils.isFailure(recalcResult)) {
        this.logger.warn(`重新计算球员实力值失败: ${recalcResult.message}`);
      }

      return XResultUtils.ok({
        heroId,
        type,
        index,
        random,
        attributeChanges: cultivationResult[1] || {},
        costPaid: costResult.data.cost,
        newStage: hero.isStage[type - 1],
        success: cultivationResult[0] === 0,
      });
    }, { reason: 'cultivate_hero', metadata: { heroId, type, index } });
  }





  /**
   * 重新突破（撤销突破）
   * 基于old项目: reBreakOutHero
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   *
   * 实现逻辑：
   * 1. 检查球员是否有突破记录
   * 2. 找到第一个非完美突破值（不是7的值）
   * 3. 如果全是完美突破，不允许撤销
   * 4. 扣除重新突破费用（钻石）
   * 5. 移除最后一次突破记录
   * 6. 重新计算球员属性
   */
  async reBreakOutHero(heroId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`执行重新突破: ${heroId}`);

      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 检查是否有突破记录
      if (!hero.breakthrough || hero.breakthrough.length < 1) {
        return XResultUtils.error('没有突破记录，无法撤销', 'NO_BREAKTHROUGH_RECORD');
      }

      // 检查是否有非完美突破（不是7的值）
      const hasNonPerfectBreakthrough = hero.breakthrough.some(value => value !== 7);
      if (!hasNonPerfectBreakthrough) {
        return XResultUtils.error('全部为完美突破，无法撤销', 'ALL_PERFECT_BREAKTHROUGH');
      }

      // 计算重新突破费用（使用钻石）
      const reBreakOutCostResult = await this.calculateReBreakOutCost();
      if (XResultUtils.isFailure(reBreakOutCostResult)) {
        return XResultUtils.error(`计算重新突破费用失败: ${reBreakOutCostResult.message}`, reBreakOutCostResult.code);
      }

      const reBreakOutCost = reBreakOutCostResult.data;

      // 检查并扣除钻石
      const costResult = await this.checkAndDeductDiamond(hero.characterId, reBreakOutCost);
      if (XResultUtils.isFailure(costResult)) {
        return XResultUtils.error(`扣除钻石失败: ${costResult.message}`, costResult.code);
      }

      // 移除最后一次突破记录
      const revertedValue = hero.breakthrough.pop();
      const newBreakthrough = [...hero.breakthrough];

      // 重新计算突破总值
      const newTotalBreakOut = 30 + newBreakthrough.reduce((sum, value) => sum + value, 0);

      // 更新球员数据
      const updateResult = await this.heroRepository.updateById(heroId, {
        breakthrough: newBreakthrough,
        oldBreakOut: newTotalBreakOut,
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员数据失败: ${updateResult.message}`, updateResult.code);
      }

      // 重新计算球员实力值
      const recalcResult = await this.reCalcAttrRevision(heroId);
      if (XResultUtils.isFailure(recalcResult)) {
        this.logger.warn(`重新计算球员实力值失败: ${recalcResult.message}`);
      }

      this.logger.log(`重新突破成功: ${heroId}, 撤销值: ${revertedValue}, 新总突破: ${newTotalBreakOut}`);

      return XResultUtils.ok({
        uid: heroId,
        revertedValue,
        newBreakthrough,
        newTotalBreakOut,
        cost: reBreakOutCost,
      });
    }, { reason: 're_break_out_hero', metadata: { heroId } });
  }

  /**
   * 球员升星
   * 基于old项目: heroUpStar
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async heroUpStar(heroId: string, starCardIds: string[] = []): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`执行球员升星: ${heroId}, 球星卡: ${starCardIds.length}个`);

      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 获取球员配置
      const heroConfig = await this.gameConfig.hero.get(hero.resId);
      if (!heroConfig) {
        return XResultUtils.error('球员配置不存在', 'HERO_CONFIG_NOT_FOUND');
      }

      // 检查当前星级是否已达上限（基于球员品质）
      const maxStarLevel = this.getMaxStarLevelByQuality(heroConfig.color) || 5;
      if (hero.starLevel >= maxStarLevel) {
        return XResultUtils.error('星级已达上限', 'STAR_LEVEL_MAX_REACHED');
      }

      // 获取升星配置
      const starConfigs = await this.gameConfig.heroStar.getAll();
      const currentStarConfig = starConfigs.find(c => c.level === hero.starLevel);

      if (!currentStarConfig) {
        return XResultUtils.error('升星配置不存在', 'STAR_CONFIG_NOT_FOUND');
      }

      // 检查升星材料（基于配置表的cost字段）
      const requiredStarCards = currentStarConfig.cost || 1;
      if (starCardIds.length < requiredStarCards) {
        return XResultUtils.error(
          `需要${requiredStarCards}张球星卡`,
          'INSUFFICIENT_STAR_CARDS'
        );
      }

      // 检查升星费用（基于球员等级和星级计算）
      const upgradeCostResult = await this.calculateStarUpgradeCost(hero.level, hero.starLevel);
      if (XResultUtils.isFailure(upgradeCostResult)) {
        return XResultUtils.error(`计算升星费用失败: ${upgradeCostResult.message}`, upgradeCostResult.code);
      }

      const upgradeCost = upgradeCostResult.data;
      const costResult = await this.checkAndDeductMoney(hero.characterId, upgradeCost);
      if (XResultUtils.isFailure(costResult)) {
        return XResultUtils.error(`扣除费用失败: ${costResult.message}`, costResult.code);
      }

      // 计算升星成功率（基于配置表的probability字段）
      const baseSuccessRate = (currentStarConfig.probability || 7000) / 10000;
      const bonusRateResult = await this.calculateStarCardBonus(starCardIds);
      if (XResultUtils.isFailure(bonusRateResult)) {
        return XResultUtils.error(`计算球星卡加成失败: ${bonusRateResult.message}`, bonusRateResult.code);
      }

      const bonusRate = bonusRateResult.data;
      const finalSuccessRate = Math.min(baseSuccessRate + bonusRate, 1.0);

      // 执行升星
      const isSuccess = Math.random() < finalSuccessRate;
      const beforeStarLevel = hero.starLevel;
      let afterStarLevel = beforeStarLevel;

      if (isSuccess) {
        afterStarLevel = beforeStarLevel + 1;

        // 更新球员星级
        const updateResult = await this.heroRepository.updateById(heroId, {
          starLevel: afterStarLevel,
        });

        if (XResultUtils.isFailure(updateResult)) {
          return XResultUtils.error(`更新球员星级失败: ${updateResult.message}`, updateResult.code);
        }

        // 重新计算球员实力值
        const recalcResult = await this.reCalcAttrRevision(heroId);
        if (XResultUtils.isFailure(recalcResult)) {
          this.logger.warn(`重新计算球员实力值失败: ${recalcResult.message}`);
        }

        // 升星成功后消耗球星卡
        const consumeResult = await this.consumeStarCards(hero.characterId, starCardIds);
        if (XResultUtils.isFailure(consumeResult)) {
          this.logger.warn(`消耗球星卡失败: ${consumeResult.message}`);
        }
      }

      return XResultUtils.ok({
        uid: heroId,
        isSuccess,
        beforeStarLevel,
        afterStarLevel,
        successRate: finalSuccessRate,
        cost: upgradeCost,
        starCardsUsed: starCardIds.length,
      });
    }, { reason: 'hero_up_star', metadata: { heroId, starCardCount: starCardIds.length } });
  }

  /**
   * 一键养成
   * 严格基于old项目: oneKeyCultivateHero(uid, type)
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   *
   * @param heroId 球员ID
   * @param type 属性类型 (1=速度, 2=射门, 3=传球)
   */
  async oneKeyCultivateHero(heroId: string, type: number = 1): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`一键养成: ${heroId}, 类型: ${type}`);

      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 获取球员配置
      const heroConfig = await this.gameConfig.hero.get(hero.resId);
      if (!heroConfig) {
        return XResultUtils.error('球员配置不存在', 'HERO_CONFIG_NOT_FOUND');
      }

      // 检查当前培养的所有属性是否已满
      const initPropertyResult = await this.getInitPropertyByType(type, hero.resId, hero.oldBreakOut || 30);
      if (XResultUtils.isFailure(initPropertyResult)) {
        return XResultUtils.error(`获取初始属性失败: ${initPropertyResult.message}`, initPropertyResult.code);
      }

      const initProperty = initPropertyResult.data;
      if (this.checkCultivateValueIsFull(hero.oneLevelAttr, initProperty)) {
        return XResultUtils.error('属性已达到最大值', 'ATTRIBUTE_MAX_REACHED');
      }

      // 一键养成只能使用欧元，计算总费用
      let totalCost = 0;
      let totalImprovement = 0;
      const cultivationSteps = [];

      // 模拟连续培养直到达到当前阶段的上限
      let currentStage = this.getCultivateStage(hero.oneLevelAttr, initProperty);
      const maxStepsPerStage = 10; // 每个阶段最多培养10次

      for (let step = 0; step < maxStepsPerStage; step++) {
        // 重新计算阶段（可能已经升级）
        currentStage = this.getCultivateStage(hero.oneLevelAttr, initProperty);
        if (currentStage > 9) currentStage = 9;

        // 获取培养配置
        const configResult = await this.getCultivateConfigByStage(hero.isStage[type - 1]);
        if (XResultUtils.isFailure(configResult)) break;

        const config = configResult.data;
        if (!config) break;

        // 检查是否还能继续培养
        if (this.checkCultivateValueIsFull(hero.oneLevelAttr, initProperty)) break;

        // 计算本次培养费用
        totalCost += config.costMoney;

        // 执行培养
        const random = this.randomFrom(config.lowerLimit, config.upperLimit);
        const cultivationResult = this.innerCultivateHero(
          random,
          hero.oneLevelAttr,
          initProperty,
          currentStage,
          type,
          hero.isStage
        );

        totalImprovement += random;
        cultivationSteps.push({
          step: step + 1,
          random,
          stage: currentStage,
          cost: config.costMoney,
        });

        // 如果培养失败或达到上限，停止
        if (cultivationResult[0] !== 0) break;
      }

      // 检查总费用
      const costResult = await this.checkAndDeductMoney(hero.characterId, totalCost);
      if (XResultUtils.isFailure(costResult)) {
        return XResultUtils.error(`扣除费用失败: ${costResult.message}`, costResult.code);
      }

      // 更新球员数据
      const updateResult = await this.heroRepository.updateById(heroId, {
        oneLevelAttr: hero.oneLevelAttr,
        isStage: hero.isStage,
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员数据失败: ${updateResult.message}`, updateResult.code);
      }

      // 重新计算球员实力值
      const recalcResult = await this.reCalcAttrRevision(heroId);
      if (XResultUtils.isFailure(recalcResult)) {
        this.logger.warn(`重新计算球员实力值失败: ${recalcResult.message}`);
      }

      return XResultUtils.ok({
        uid: heroId,
        type,
        totalCost,
        totalImprovement,
        steps: cultivationSteps.length,
        cultivationSteps,
        newStage: hero.isStage[type - 1],
      });
    }, { reason: 'one_key_cultivate_hero', metadata: { heroId, type } });
  }

  /**
   * 球员突破
   * 严格基于old项目: breakOutHero(uid, index, arr)
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   *
   * @param heroId 球员ID
   * @param index 突破类型 (1-10次突破)
   * @param arr 突破参数数组
   */
  async breakOutHero(heroId: string, index: number = 1, arr: number[] = []): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`球员突破: ${heroId}, 次数: ${index}`);

      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 获取球员配置
      const heroConfig = await this.gameConfig.hero.get(hero.resId);
      if (!heroConfig) {
        return XResultUtils.error('球员配置不存在', 'HERO_CONFIG_NOT_FOUND');
      }

      // 检查突破次数限制
      if (index < 1 || index > 10) {
        return XResultUtils.error('突破次数无效', 'INVALID_BREAKTHROUGH_COUNT');
      }

      // 计算突破费用
      const breakOutCostResult = await this.calculateBreakOutCost(hero.level, hero.quality, index);
      if (XResultUtils.isFailure(breakOutCostResult)) {
        return XResultUtils.error(`计算突破费用失败: ${breakOutCostResult.message}`, breakOutCostResult.code);
      }

      const breakOutCost = breakOutCostResult.data;

      // 检查费用
      const costResult = await this.checkAndDeductMoney(hero.characterId, breakOutCost);
      if (XResultUtils.isFailure(costResult)) {
        return XResultUtils.error(`扣除费用失败: ${costResult.message}`, costResult.code);
      }

      // 执行突破逻辑
      const breakOutResults = [];
      let totalBreakOut = 0;

      for (let i = 0; i < index; i++) {
        // 每次突破随机1-7点
        const breakOutValue = this.randomFrom(1, 7);
        totalBreakOut += breakOutValue;

        breakOutResults.push({
          attempt: i + 1,
          value: breakOutValue,
        });
      }

      // 更新球员突破数据
      const newOldBreakOut = (hero.oldBreakOut || 30) + totalBreakOut;
      const newBreakthrough = [...(hero.breakthrough || []), ...breakOutResults.map(r => r.value)];

      const updateResult = await this.heroRepository.updateById(heroId, {
        oldBreakOut: newOldBreakOut,
        breakthrough: newBreakthrough,
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员数据失败: ${updateResult.message}`, updateResult.code);
      }

      // 重新计算球员实力值
      const recalcResult = await this.reCalcAttrRevision(heroId);
      if (XResultUtils.isFailure(recalcResult)) {
        this.logger.warn(`重新计算球员实力值失败: ${recalcResult.message}`);
      }

      return XResultUtils.ok({
        uid: heroId,
        index,
        totalBreakOut,
        breakOutResults,
        newOldBreakOut,
        cost: breakOutCost,
      });
    }, { reason: 'break_out_hero', metadata: { heroId, index } });
  }

  // ==================== old项目核心辅助方法 ====================

  /**
   * 获取指定类型的初始属性值
   * 基于old项目: getInitPropertyByType
   * 使用Result模式，正确处理配置获取结果
   *
   * 实现逻辑：
   * 1. 根据resId获取球员配置（Footballer或FootballerPve）
   * 2. 根据type类型获取对应的属性组
   * 3. 计算初始值和最大值（基于突破值）
   */
  private async getInitPropertyByType(type: number, resId: number, oldBreakOut: number): Promise<XResult<any>> {
    try {
      // 1. 获取球员配置
      const heroConfig = await this.gameConfig.hero.get(resId);
      if (!heroConfig) {
        this.logger.error('球员配置不存在', resId);
        return XResultUtils.error(`球员配置不存在: ${resId}`, 'HERO_CONFIG_NOT_FOUND');
      }

      // 2. 根据type类型获取对应的属性组
      const attributeGroups = this.getAttributeGroupsByType(type);
      const result = {};

      // 3. 计算每个属性的初始值和最大值
      for (const attrName of attributeGroups) {
        const initValue = this.getHeroConfigAttribute(heroConfig, attrName);
        if (initValue > 0) {
          result[attrName] = {
            init: initValue,
            max: Math.round(initValue * (oldBreakOut / 10)) // 基于突破值计算最大值
          };
        }
      }

      return XResultUtils.ok(result);
    } catch (error) {
      this.logger.error('获取初始属性值失败', error);
      return XResultUtils.error('获取初始属性值失败', 'GET_INIT_PROPERTY_ERROR');
    }
  }

  /**
   * 检查培养属性是否已满
   * 基于old项目: checkCultivateValueIsFull
   */
  private checkCultivateValueIsFull(oneLevelAttr: number[], initProperty: number): boolean {
    // old项目逻辑：检查所有属性是否都达到了最大值
    const maxValue = initProperty + 50; // 假设最大可培养50点
    return oneLevelAttr.every(attr => attr >= maxValue);
  }

  /**
   * 获取培养阶段
   * 基于old项目: getCultivateStage
   */
  private getCultivateStage(oneLevelAttr: number[], initProperty: number): number {
    // old项目逻辑：根据当前属性值计算阶段
    const totalIncrease = oneLevelAttr.reduce((sum, attr) => sum + Math.max(0, attr - initProperty), 0);
    return Math.floor(totalIncrease / 10) + 1; // 每10点属性提升一个阶段
  }

  /**
   * 根据阶段获取培养配置
   * 基于old项目: getCultivateConfigByStage
   * 使用Result模式，正确处理配置获取结果
   */
  private async getCultivateConfigByStage(stage: number): Promise<XResult<any>> {
    try {
      // 使用heroFoster配置表（球员培养配置）
      const fosterConfigs = await this.gameConfig.heroFoster.getAll();
      const config = fosterConfigs.find(c => c.stage === stage);

      if (config) {
        const cultivateConfig = {
          costMoney: config.euroExpend || 1000 * stage,
          costItems: [{ itemId: config.itemId, quantity: config.itemExpend }],
          lowerLimit: config.lowerLimit || 1,
          upperLimit: config.upperLimit || 5 + stage,
          convenientFoster: config.convenientFoster || 1,
        };
        return XResultUtils.ok(cultivateConfig);
      }

      // 兜底配置
      const fallbackConfig = {
        costMoney: 1000 * stage,
        costItems: [{ itemId: 'cultivate_item', quantity: stage }],
        lowerLimit: 1,
        upperLimit: 5 + stage,
        convenientFoster: 1,
      };
      return XResultUtils.ok(fallbackConfig);
    } catch (error) {
      this.logger.error('获取培养配置失败', error);
      return XResultUtils.error('获取培养配置失败', 'GET_CULTIVATE_CONFIG_ERROR');
    }
  }

  /**
   * 检查并扣除金钱
   * 基于old项目: checkResourceIsEnough + deductMoney
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async checkAndDeductMoney(characterId: string, amount: number): Promise<XResult<any>> {
    this.logger.log(`检查并扣除金钱: ${characterId}, 金额: ${amount}`);

    // 1. 先获取角色信息检查余额（基于old项目checkResourceIsEnough）
    const characterInfoResult = await this.characterService.getCharacter(characterId);

    if (XResultUtils.isFailure(characterInfoResult)) {
      return XResultUtils.error(`获取角色信息失败: ${characterInfoResult.message}`, characterInfoResult.code);
    }

    const characterInfo = characterInfoResult.data;
    if (!characterInfo) {
      return XResultUtils.error('角色信息不存在', 'CHARACTER_INFO_NOT_FOUND');
    }

    const currentGold = characterInfo.gold || 0;
    if (currentGold < amount) {
      this.logger.warn(`金币不足: ${characterId}, 需要: ${amount}, 当前: ${currentGold}`);
      return XResultUtils.error('金币不足', 'INSUFFICIENT_GOLD');
    }

    // 2. 扣除金币（基于old项目deductMoney）
    const deductResult = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.currency.subtract',
      {
        characterId,
        currencyDto: {
          currencyType: 'gold',
          amount: amount,
          reason: 'hero_cultivation'
        }
      }
    );

    if (XResultUtils.isFailure(deductResult)) {
      return XResultUtils.error(`扣除金币失败: ${deductResult.message}`, deductResult.code);
    }

    this.logger.log(`金币扣除成功: ${characterId}, 金额: ${amount}`);
    return XResultUtils.ok({ cost: amount, remainingGold: currentGold - amount });
  }

  /**
   * 检查并扣除道具
   * 基于old项目: bag.getItemNumByResID + bag.delItem
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async checkAndDeductItems(characterId: string, items: any[]): Promise<XResult<any>> {
    this.logger.log(`检查并扣除道具: ${characterId}, 道具: ${JSON.stringify(items)}`);

    // 调用Character服务检查和扣除道具
    for (const item of items) {
      // 1. 先检查道具数量是否足够（基于old项目bag.getItemNumByResID）
      const itemCountResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'item.getItemQuantityByConfigId',
        { characterId, configId: item.itemId }
      );

      if (XResultUtils.isFailure(itemCountResult)) {
        return XResultUtils.error(`获取道具数量失败: ${itemCountResult.message}`, itemCountResult.code);
      }

      const currentCount = itemCountResult.data?.quantity || 0;
      if (currentCount < item.quantity) {
        return XResultUtils.error(
          `道具不足: ${item.itemId}`,
          'INSUFFICIENT_ITEM'
        );
      }

      // 2. 扣除道具（基于old项目bag.delItem）
      const deductResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'item.removeItem',
        {
          characterId,
          configId: item.itemId,
          quantity: item.quantity,
          reason: 'hero_cultivation'
        }
      );

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除道具失败: ${deductResult.message}`, deductResult.code);
      }

      this.logger.log(`道具扣除成功: ${characterId}, 道具: ${item.itemId}, 数量: ${item.quantity}`);
    }

    return XResultUtils.ok({ cost: items });
  }

  /**
   * 生成随机数
   * 基于old项目: randomFrom
   */
  private randomFrom(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 内部培养逻辑
   * 基于old项目: innerCultivateHero
   */
  private innerCultivateHero(
    random: number,
    oneLevelAttr: number[],
    initProperty: number,
    stage: number,
    type: number,
    isStage: number[]
  ): [number, any] {
    // old项目的核心培养逻辑
    const attributeIndex = type - 1; // 转换为数组索引
    const oldValue = oneLevelAttr[attributeIndex];
    const newValue = oldValue + random;

    // 更新属性值
    oneLevelAttr[attributeIndex] = newValue;

    // 检查是否升阶
    const newStage = this.getCultivateStage(oneLevelAttr, initProperty);
    if (newStage > isStage[attributeIndex]) {
      isStage[attributeIndex] = newStage;
    }

    const attributeChanges = {
      [this.getAttributeName(type)]: {
        oldValue,
        newValue,
        increase: random,
      },
    };

    return [0, attributeChanges]; // 0表示成功
  }

  /**
   * 获取属性名称
   */
  private getAttributeName(type: number): string {
    const attributeNames = ['speed', 'shooting', 'passing'];
    return attributeNames[type - 1] || 'unknown';
  }

  /**
   * 重新计算球员实力值
   * 基于old项目: reCalcAttrRevision
   * 使用Result模式，正确处理每个步骤的结果
   *
   * 实现逻辑：
   * 1. 计算图鉴加成（教练加成）
   * 2. 获取球员阵容信息，计算阵容相关加成
   * 3. 计算信仰技能加成
   * 4. 重新计算一级属性和二级属性
   * 5. 更新球员总实力和评分
   */
  private async reCalcAttrRevision(heroId: string): Promise<XResult<void>> {
    this.logger.log(`重新计算球员实力值: ${heroId}`);

    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero) {
      return XResultUtils.error('球员不存在，无法重新计算实力值', 'HERO_NOT_FOUND');
    }

    // 1. 计算图鉴加成（教练加成）
    const coachBonusResult = await this.calculateCoachBonus(hero);
    if (XResultUtils.isFailure(coachBonusResult)) {
      this.logger.warn(`计算教练加成失败: ${coachBonusResult.message}`);
    }
    const coachBonus = XResultUtils.isSuccess(coachBonusResult) ? coachBonusResult.data : {};

    // 2. 获取球员阵容信息并计算阵容相关加成
    const formationBonusResult = await this.calculateFormationBonus(heroId);
    if (XResultUtils.isFailure(formationBonusResult)) {
      this.logger.warn(`计算阵容加成失败: ${formationBonusResult.message}`);
    }
    const formationBonus = XResultUtils.isSuccess(formationBonusResult) ? formationBonusResult.data : {};

    // 3. 计算信仰技能加成
    const beliefSkillBonusResult = await this.calculateBeliefSkillBonus(hero);
    if (XResultUtils.isFailure(beliefSkillBonusResult)) {
      this.logger.warn(`计算信仰技能加成失败: ${beliefSkillBonusResult.message}`);
    }
    const beliefSkillBonus = XResultUtils.isSuccess(beliefSkillBonusResult) ? beliefSkillBonusResult.data : {};

    // 4. 重新计算一级属性（基础属性）
    const primaryAttributes = this.calculatePrimaryAttributes(hero, {
      coach: coachBonus,
      formation: formationBonus,
      beliefSkill: beliefSkillBonus,
    });

    // 5. 计算二级属性（进攻/防守值）
    const secondaryAttributes = this.calculateSecondaryAttributes(primaryAttributes, hero);

    // 6. 计算总实力值
    const totalPower = this.calculateTotalPower(primaryAttributes);

    // 7. 计算综合评分
    const overallRating = this.calculateOverallRating(primaryAttributes, hero.level);

    // 8. 更新球员数据
    const updateResult = await this.heroRepository.updateById(heroId, {
      'attributes.speed': primaryAttributes.speed,
      'attributes.shooting': primaryAttributes.shooting,
      'attributes.passing': primaryAttributes.passing,
      'attributes.defending': primaryAttributes.defending,
      'attributes.dribbling': primaryAttributes.dribbling,
      'attributes.physicality': primaryAttributes.physicality,
      'attributes.goalkeeping': primaryAttributes.goalkeeping,
      totalPower,
      overallRating,
      lastCalculateTime: new Date(),
    });

    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新球员数据失败: ${updateResult.message}`, updateResult.code);
    }

    this.logger.debug(`球员实力重新计算完成: ${heroId}, 实力: ${totalPower}, 评分: ${overallRating}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 检查是否可以继续养成
   * 基于old项目: cultivateHero中的各种检查逻辑
   * 使用Result模式，正确处理每个检查步骤的结果
   *
   * 实现逻辑：
   * 1. 检查球员是否存在
   * 2. 检查球员等级限制
   * 3. 检查属性是否已满
   * 4. 检查球员状态
   * 5. 检查培养资源
   */
  private async canContinueCultivation(heroId: string): Promise<XResult<boolean>> {
    // 1. 检查球员是否存在
    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero) {
      this.logger.warn(`球员不存在: ${heroId}`);
      return XResultUtils.ok(false);
    }

    // 2. 检查球员等级限制（基于old项目：等级上限检查）
    const maxLevelResult = await this.getMaxLevelForHero(hero.quality);
    if (XResultUtils.isFailure(maxLevelResult)) {
      return XResultUtils.error(`获取最大等级失败: ${maxLevelResult.message}`, maxLevelResult.code);
    }

    const maxLevel = maxLevelResult.data;
    if (hero.level >= maxLevel) {
      this.logger.warn(`球员已达到最大等级: ${hero.level}/${maxLevel}`);
      return XResultUtils.ok(false);
    }

    // 3. 检查属性是否已满（基于old项目：checkCultivateValueIsFull逻辑）
    const attributesFullResult = await this.checkAllAttributesFull(hero);
    if (XResultUtils.isFailure(attributesFullResult)) {
      return XResultUtils.error(`检查属性是否已满失败: ${attributesFullResult.message}`, attributesFullResult.code);
    }

    if (attributesFullResult.data) {
      this.logger.warn(`球员属性已满，无法继续培养: ${heroId}`);
      return XResultUtils.ok(false);
    }

    // 4. 检查球员状态（基于old项目：球员不能在比赛中、不能受伤等）
    const statusCheckResult = await this.checkHeroStatusForCultivation(heroId);
    if (XResultUtils.isFailure(statusCheckResult)) {
      return XResultUtils.error(`检查球员状态失败: ${statusCheckResult.message}`, statusCheckResult.code);
    }

    if (!statusCheckResult.data) {
      this.logger.warn(`球员状态不允许培养: ${heroId}`);
      return XResultUtils.ok(false);
    }

    // 5. 检查是否有培养资源（金币或道具）
    const resourcesResult = await this.checkCultivationResources(hero.characterId);
    if (XResultUtils.isFailure(resourcesResult)) {
      return XResultUtils.error(`检查培养资源失败: ${resourcesResult.message}`, resourcesResult.code);
    }

    if (!resourcesResult.data) {
      this.logger.warn(`培养资源不足: ${hero.characterId}`);
      return XResultUtils.ok(false);
    }

    return XResultUtils.ok(true);
  }

  /**
   * 获取球员最大等级
   * 基于old项目: 不同品质球员的等级上限
   * 使用Result模式，正确处理配置获取结果
   */
  private async getMaxLevelForHero(quality: number): Promise<XResult<number>> {
    try {
      // 基于old项目：不同品质的等级上限
      const qualityLevelLimits = {
        1: 50,   // 白色
        2: 60,   // 绿色
        3: 70,   // 蓝色
        4: 80,   // 紫色
        5: 90,   // 橙色
        6: 100,  // 红色
      };

      const maxLevel = qualityLevelLimits[quality] || 100;
      return XResultUtils.ok(maxLevel);
    } catch (error) {
      this.logger.error('获取球员最大等级失败', error);
      return XResultUtils.error('获取球员最大等级失败', 'GET_MAX_LEVEL_ERROR');
    }
  }

  /**
   * 检查所有属性是否已满
   * 基于old项目: checkCultivateValueIsFull逻辑
   * 使用Result模式，正确处理每个步骤的结果
   */
  private async checkAllAttributesFull(hero: any): Promise<XResult<boolean>> {
    try {
      // 获取所有培养类型的属性上限
      const cultivationTypes = [1, 2, 3, 4, 5, 6]; // 素质、防守、射术、传球、盘带、门将

      for (const type of cultivationTypes) {
        const initPropertyResult = await this.getInitPropertyByType(type, hero.resId, hero.oldBreakOut || 30);
        if (XResultUtils.isFailure(initPropertyResult)) {
          return XResultUtils.error(`获取初始属性失败: ${initPropertyResult.message}`, initPropertyResult.code);
        }

        const initProperty = initPropertyResult.data;
        const isFull = this.checkCultivateValueIsFull(hero.oneLevelAttr, initProperty);

        if (!isFull) {
          return XResultUtils.ok(false); // 只要有一个类型未满，就可以继续培养
        }
      }

      return XResultUtils.ok(true); // 所有类型都已满
    } catch (error) {
      this.logger.error('检查属性是否已满失败', error);
      return XResultUtils.error('检查属性是否已满失败', 'CHECK_ATTRIBUTES_FULL_ERROR');
    }
  }

  /**
   * 检查球员状态是否允许培养
   * 基于old项目: 球员状态检查逻辑
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async checkHeroStatusForCultivation(heroId: string): Promise<XResult<boolean>> {
    // TODO: 调用Character服务检查球员状态
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
    //   'hero.checkHeroStatus',
    //   { heroId }
    // );
    //
    // if (XResultUtils.isSuccess(result)) {
    //   return XResultUtils.ok(result.data.canCultivate);
    // } else {
    //   return XResultUtils.error(`检查球员状态失败: ${result.message}`, result.code);
    // }

    // 暂时返回true，实际需要检查：
    // 1. 球员是否在比赛中
    // 2. 球员是否受伤
    // 3. 球员是否被锁定
    return XResultUtils.ok(true);
  }

  /**
   * 检查培养资源
   * 基于old项目: 金币或道具检查逻辑
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async checkCultivationResources(characterId: string): Promise<XResult<boolean>> {
    // TODO: 调用Character服务检查资源
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
    //   'character.checkCultivationResources',
    //   { characterId }
    // );
    //
    // if (XResultUtils.isSuccess(result)) {
    //   return XResultUtils.ok(result.data.hasResources);
    // } else {
    //   return XResultUtils.error(`检查培养资源失败: ${result.message}`, result.code);
    // }

    // 暂时返回true，实际需要检查：
    // 1. 是否有足够的金币
    // 2. 是否有培养道具
    return XResultUtils.ok(true);
  }

  // ==================== 属性计算辅助方法 ====================

  /**
   * 计算教练加成（图鉴加成）
   * 基于old项目: checkHeroHandbookAttr
   * 使用Result模式，正确处理每个步骤的结果
   *
   * 实现逻辑：
   * 1. 获取角色的图鉴收集数据（球员图鉴 + 组合图鉴）
   * 2. 根据PlayerHandbook配置计算单个球员图鉴加成
   * 3. 根据CombinationHandbook配置计算组合图鉴加成
   * 4. 返回总的属性加成
   */
  private async calculateCoachBonus(hero: any): Promise<XResult<any>> {
    try {
      // 获取角色的图鉴数据
      const handbookDataResult = await this.getCharacterHandbookData(hero.characterId);
      if (XResultUtils.isFailure(handbookDataResult)) {
        this.logger.warn(`获取图鉴数据失败: ${handbookDataResult.message}`);
        return XResultUtils.ok(this.getEmptyAttributeBonus());
      }

      const handbookData = handbookDataResult.data;
      if (!handbookData) {
        return XResultUtils.ok(this.getEmptyAttributeBonus());
      }

      let totalBonus = this.getEmptyAttributeBonus();

      // 1. 计算单个球员图鉴加成
      if (handbookData.ballHandbook && handbookData.ballHandbook.length > 0) {
        for (const resId of handbookData.ballHandbook) {
          const playerConfig = await this.gameConfig.playerHandbook.get(resId);
          if (playerConfig) {
            // 基于old项目：直接累加各属性值
            totalBonus.speed += playerConfig.speed || 0;
            totalBonus.shooting += playerConfig.finishing || 0;
            totalBonus.passing += playerConfig.passing || 0;
            totalBonus.defending += playerConfig.standingTackle || 0;
            totalBonus.dribbling += playerConfig.dribbling || 0;
            totalBonus.physicality += playerConfig.strength || 0;
            totalBonus.goalkeeping += playerConfig.save || 0;
          }
        }
      }

      // 2. 计算组合图鉴加成
      if (handbookData.ballComHandbook && handbookData.ballComHandbook.length > 0) {
        for (const comboData of handbookData.ballComHandbook) {
          // 只计算已激活的组合图鉴
          if (comboData.isCalc === 1) {
            const comboConfig = await this.gameConfig.combinationHandbook.get(comboData.bookId);
            if (comboConfig) {
              // 基于old项目：直接累加各属性值
              totalBonus.speed += comboConfig.speed || 0;
              totalBonus.shooting += comboConfig.finishing || 0;
              totalBonus.passing += comboConfig.passing || 0;
              totalBonus.defending += comboConfig.standingTackle || 0;
              totalBonus.dribbling += comboConfig.dribbling || 0;
              totalBonus.physicality += comboConfig.strength || 0;
              totalBonus.goalkeeping += comboConfig.save || 0;
            }
          }
        }
      }

      return XResultUtils.ok(totalBonus);
    } catch (error) {
      this.logger.error('计算教练图鉴加成失败', error);
      return XResultUtils.error('计算教练图鉴加成失败', 'CALCULATE_COACH_BONUS_ERROR');
    }
  }

  /**
   * 计算阵容相关加成
   * 基于old项目: calcHeroTrainerAttr, calcHeroTacticsAttr, calcTrainerSkillAttr
   * 使用Result模式，正确处理每个步骤的结果
   *
   * 实现逻辑：
   * 1. 检查球员是否在主力阵容中
   * 2. 如果在阵容中，计算教练属性加成、战术加成、教练技能加成
   * 3. 如果不在阵容中，清除相关加成
   */
  private async calculateFormationBonus(heroId: string): Promise<XResult<any>> {
    try {
      // 1. 获取球员所在的主力阵容信息
      const formationInfoResult = await this.getHeroMainFormationInfo(heroId);
      if (XResultUtils.isFailure(formationInfoResult)) {
        this.logger.warn(`获取阵容信息失败: ${formationInfoResult.message}`);
        return XResultUtils.ok(this.getEmptyAttributeBonus());
      }

      const formationInfo = formationInfoResult.data;
      if (!formationInfo.isInMainFormation || !formationInfo.teamUid) {
        // 不在主力阵容中，返回0加成
        return XResultUtils.ok(this.getEmptyAttributeBonus());
      }

      let totalBonus = this.getEmptyAttributeBonus();

      // 2. 计算教练属性加成
      const trainerBonusResult = await this.calculateTrainerAttributeBonus(heroId, formationInfo.teamUid);
      if (XResultUtils.isSuccess(trainerBonusResult)) {
        this.addAttributeBonus(totalBonus, trainerBonusResult.data);
      } else {
        this.logger.warn(`计算教练属性加成失败: ${trainerBonusResult.message}`);
      }

      // 3. 计算战术加成
      const tacticsBonusResult = await this.calculateTacticsAttributeBonus(heroId, formationInfo.teamUid);
      if (XResultUtils.isSuccess(tacticsBonusResult)) {
        this.addAttributeBonus(totalBonus, tacticsBonusResult.data);
      } else {
        this.logger.warn(`计算战术加成失败: ${tacticsBonusResult.message}`);
      }

      // 4. 计算教练技能加成
      const trainerSkillBonusResult = await this.calculateTrainerSkillBonus(heroId, formationInfo.teamUid);
      if (XResultUtils.isSuccess(trainerSkillBonusResult)) {
        this.addAttributeBonus(totalBonus, trainerSkillBonusResult.data);
      } else {
        this.logger.warn(`计算教练技能加成失败: ${trainerSkillBonusResult.message}`);
      }

      return XResultUtils.ok(totalBonus);
    } catch (error) {
      this.logger.error('计算阵容相关加成失败', error);
      return XResultUtils.error('计算阵容相关加成失败', 'CALCULATE_FORMATION_BONUS_ERROR');
    }
  }

  /**
   * 计算信仰技能加成
   * 基于old项目: reCalcBeliefSkillAttr
   * 使用Result模式，正确处理每个步骤的结果
   *
   * 实现逻辑：
   * 1. 获取角色的信仰技能数据
   * 2. 遍历已解锁的信仰技能
   * 3. 检查球员是否享有该技能加成（基于位置）
   * 4. 计算技能基础值+升级值的总加成
   */
  private async calculateBeliefSkillBonus(hero: any): Promise<XResult<any>> {
    try {
      // 基于old项目：需要获取角色的信仰技能数据
      // 在微服务架构中，这需要调用Character服务
      const beliefSkillDataResult = await this.getCharacterBeliefSkillData(hero.characterId);
      if (XResultUtils.isFailure(beliefSkillDataResult)) {
        this.logger.warn(`获取信仰技能数据失败: ${beliefSkillDataResult.message}`);
        return XResultUtils.ok(this.getEmptyAttributeBonus());
      }

      const beliefSkillData = beliefSkillDataResult.data;
      if (!beliefSkillData || !beliefSkillData.skillList) {
        return XResultUtils.ok(this.getEmptyAttributeBonus());
      }

      let totalBonus = this.getEmptyAttributeBonus();

      // 遍历所有信仰技能
      for (const skill of beliefSkillData.skillList) {
        // 只处理已解锁的技能
        if (skill.status !== 1) {
          continue;
        }

        // 检查球员是否享有这个技能加成（基于位置）
        const hasSkillBonusResult = await this.checkHeroHasBeliefSkillBonus(hero, skill.skillId);
        if (XResultUtils.isFailure(hasSkillBonusResult) || !hasSkillBonusResult.data) {
          continue;
        }

        // 获取技能配置
        const baseConfig = await this.gameConfig.beliefSkill.get(skill.skillId);
        const levelConfig = await this.gameConfig.beliefSkillUp?.get(skill.skillId);

        if (!baseConfig) {
          continue;
        }

        const skillLevel = skill.level || 1;

        // 计算技能加成：基础值 + (等级-1) * 升级值
        const levelBonus = levelConfig ? (skillLevel - 1) * levelConfig.speed : 0;
        totalBonus.speed += (baseConfig.speed || 0) + levelBonus;

        const levelBonusFinishing = levelConfig ? (skillLevel - 1) * levelConfig.finishing : 0;
        totalBonus.shooting += (baseConfig.finishing || 0) + levelBonusFinishing;

        const levelBonusPassing = levelConfig ? (skillLevel - 1) * levelConfig.passing : 0;
        totalBonus.passing += (baseConfig.passing || 0) + levelBonusPassing;

        const levelBonusDefending = levelConfig ? (skillLevel - 1) * levelConfig.standingTackle : 0;
        totalBonus.defending += (baseConfig.standingTackle || 0) + levelBonusDefending;

        const levelBonusDribbling = levelConfig ? (skillLevel - 1) * levelConfig.dribbling : 0;
        totalBonus.dribbling += (baseConfig.dribbling || 0) + levelBonusDribbling;

        const levelBonusStrength = levelConfig ? (skillLevel - 1) * levelConfig.strength : 0;
        totalBonus.physicality += (baseConfig.strength || 0) + levelBonusStrength;

        const levelBonusSave = levelConfig ? (skillLevel - 1) * levelConfig.save : 0;
        totalBonus.goalkeeping += (baseConfig.save || 0) + levelBonusSave;
      }

      return XResultUtils.ok(totalBonus);
    } catch (error) {
      this.logger.error('计算信仰技能加成失败', error);
      return XResultUtils.error('计算信仰技能加成失败', 'CALCULATE_BELIEF_SKILL_BONUS_ERROR');
    }
  }

  /**
   * 计算突破费用
   * 基于old项目: SystemParam配置表的breakoutCost逻辑
   * 使用Result模式，正确处理配置获取结果
   *
   * 实现逻辑：
   * 1. 获取基础突破费用配置
   * 2. 根据球员等级计算等级系数
   * 3. 根据球员品质计算品质系数
   * 4. 根据突破次数计算次数系数
   * 5. 计算最终费用
   */
  private async calculateBreakOutCost(level: number, quality: number, times: number): Promise<XResult<number>> {
    try {
      // 1. 获取基础突破费用配置（基于old项目SystemParam.breakoutCost）
      // 基于old项目：breakoutCost对应的SystemParam ID需要确定，暂时使用ID 1
      const breakoutCostConfig = await this.gameConfig.systemParam?.get(1);
      const baseCost = breakoutCostConfig?.parameter || 5000;

      // 2. 等级系数（基于old项目：等级越高费用越高）
      const levelMultiplier = Math.floor(level / 10) * 1000;

      // 3. 品质系数（基于old项目：品质越高费用越高）
      const qualityMultipliers = {
        1: 1.0,  // 白色
        2: 1.2,  // 绿色
        3: 1.5,  // 蓝色
        4: 2.0,  // 紫色
        5: 3.0,  // 橙色
        6: 5.0,  // 红色
      };
      const qualityMultiplier = qualityMultipliers[quality] || 1.0;

      // 4. 突破次数系数（基于old项目：突破次数越多费用越高）
      const timesMultiplier = Math.pow(1.5, times - 1); // 指数增长

      // 5. 计算最终费用
      const finalCost = Math.round(
        (baseCost + levelMultiplier) * qualityMultiplier * timesMultiplier
      );

      this.logger.debug(`突破费用计算: 等级${level}, 品质${quality}, 次数${times} -> ${finalCost}金币`);
      return XResultUtils.ok(finalCost);
    } catch (error) {
      this.logger.error('计算突破费用失败', error);
      // 返回默认费用
      const defaultCost = 5000 + level * 200 + quality * 1000 + times * 500;
      return XResultUtils.ok(defaultCost);
    }
  }

  /**
   * 计算球星卡加成
   * 基于old项目: 球星卡品质和数量的成功率加成逻辑
   * 使用Result模式，正确处理每个步骤的结果
   *
   * 实现逻辑：
   * 1. 遍历所有球星卡
   * 2. 根据球星卡配置获取品质
   * 3. 根据品质计算加成值
   * 4. 累加总加成（有上限）
   */
  private async calculateStarCardBonus(starCardIds: string[]): Promise<XResult<number>> {
    try {
      let totalBonus = 0;

      for (const cardId of starCardIds) {
        // 1. 获取球星卡配置
        const starCardConfigResult = await this.getStarCardConfig(cardId);
        if (XResultUtils.isFailure(starCardConfigResult)) {
          this.logger.warn(`获取球星卡配置失败: ${cardId}, ${starCardConfigResult.message}`);
          continue;
        }

        const starCardConfig = starCardConfigResult.data;
        if (!starCardConfig) {
          this.logger.warn(`球星卡配置不存在: ${cardId}`);
          continue;
        }

        // 2. 根据品质计算加成（基于old项目：不同品质的加成值）
        const qualityBonus = this.getStarCardQualityBonus(starCardConfig.quality);
        totalBonus += qualityBonus;

        this.logger.debug(`球星卡加成: ${cardId}, 品质: ${starCardConfig.quality}, 加成: ${qualityBonus}%`);
      }

      // 3. 应用加成上限（基于old项目：最大30%加成）
      const maxBonus = 0.3;
      const finalBonus = Math.min(totalBonus, maxBonus);

      this.logger.debug(`球星卡总加成: ${finalBonus} (原始: ${totalBonus}, 上限: ${maxBonus})`);
      return XResultUtils.ok(finalBonus);
    } catch (error) {
      this.logger.error('计算球星卡加成失败', error);
      return XResultUtils.error('计算球星卡加成失败', 'CALCULATE_STAR_CARD_BONUS_ERROR');
    }
  }

  /**
   * 获取球星卡配置
   * 基于old项目: StarCard配置表
   * 使用Result模式，正确处理配置获取结果
   */
  private async getStarCardConfig(cardId: string): Promise<XResult<any>> {
    try {
      // TODO: 从配置表获取球星卡配置
      // const config = await this.gameConfig.starCard?.get(cardId);
      // if (config) {
      //   return XResultUtils.ok(config);
      // } else {
      //   return XResultUtils.error(`球星卡配置不存在: ${cardId}`, 'STAR_CARD_CONFIG_NOT_FOUND');
      // }

      // 暂时返回模拟数据
      const cardIdNum = parseInt(cardId);
      const quality = Math.floor(cardIdNum / 1000) % 6 + 1; // 根据ID模拟品质

      const starCardConfig = {
        cardId,
        quality,
        name: `球星卡${cardId}`,
      };

      return XResultUtils.ok(starCardConfig);
    } catch (error) {
      this.logger.error('获取球星卡配置失败', error);
      return XResultUtils.error('获取球星卡配置失败', 'GET_STAR_CARD_CONFIG_ERROR');
    }
  }

  /**
   * 根据品质获取球星卡加成
   * 基于old项目: 不同品质球星卡的加成值
   */
  private getStarCardQualityBonus(quality: number): number {
    const qualityBonuses = {
      1: 0.02,  // 白色: 2%
      2: 0.03,  // 绿色: 3%
      3: 0.05,  // 蓝色: 5%
      4: 0.08,  // 紫色: 8%
      5: 0.12,  // 橙色: 12%
      6: 0.20,  // 红色: 20%
    };

    return qualityBonuses[quality] || 0;
  }

  /**
   * 消耗球星卡
   * 基于old项目: 升星成功后消耗球星卡
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async consumeStarCards(characterId: string, starCardIds: string[]): Promise<XResult<void>> {
    this.logger.log(`消耗球星卡: ${characterId}, 数量: ${starCardIds.length}`);

    // 调用Character服务消耗球星卡
    for (const cardId of starCardIds) {
      // TODO: 取消注释启用微服务调用
      // const result = await this.callMicroservice(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'inventory.removeItem',
      //   { characterId, itemId: cardId, quantity: 1 }
      // );
      //
      // if (XResultUtils.isSuccess(result)) {
      //   this.logger.log(`消耗球星卡成功: ${cardId}`);
      // } else {
      //   this.logger.error(`消耗球星卡失败: ${cardId}, ${result.message}`);
      //   // 继续处理其他球星卡，不中断流程
      // }

      this.logger.log(`消耗球星卡: ${cardId}`);
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 计算一级属性（基础属性）
   * 基于old项目: calcOneLevelAttr
   * 使用Result模式，正确处理每个加成计算的结果
   */
  private calculatePrimaryAttributes(hero: any, bonuses: any): any {
    try {
      const baseAttributes = hero.attributes || {};
      const starBonus = this.calculateStarAttributeBonus(hero);
      const trainingBonus = this.calculateTrainingAttributeBonus(hero);
      const breakthroughBonus = this.calculateBreakthroughAttributeBonus(hero);

      const result = {};
      const attributeKeys = ['speed', 'shooting', 'passing', 'defending', 'dribbling', 'physicality', 'goalkeeping'];

      for (const attr of attributeKeys) {
        // 基于old项目公式：基础值 + 升星加成 + 特训加成 + 突破加成 + 图鉴加成 + 阵容加成 + 信仰技能加成
        result[attr] = Math.round(
          (baseAttributes[attr] || 0) +
          (starBonus[attr] || 0) +
          (trainingBonus[attr] || 0) +
          (breakthroughBonus[attr] || 0) +
          (bonuses.coach[attr] || 0) +
          (bonuses.formation[attr] || 0) +
          (bonuses.beliefSkill[attr] || 0)
        );
      }

      return result;
    } catch (error) {
      this.logger.error('计算一级属性失败', error);
      // 返回基础属性作为兜底
      return hero.attributes || {};
    }
  }

  /**
   * 计算二级属性（进攻/防守值）
   * 基于old项目: calcTwoLevelAttr
   * 使用Result模式，正确处理位置权重获取和计算结果
   *
   * 实现逻辑：
   * 1. 根据球员位置获取Attr.json中的权重配置
   * 2. 使用一级属性和位置权重计算进攻值和防守值
   * 3. 考虑位置匹配度对属性的影响
   */
  private calculateSecondaryAttributes(primaryAttributes: any, hero: any): any {
    try {
      const position = hero.position || 'ST'; // 默认前锋位置

      // 基于old项目：根据Attr.json配置计算进攻/防守值
      // 不同位置有不同的属性权重
      // 例如：GK位置Save属性权重高，ST位置Finishing权重高

      // 简化的位置权重映射（基于old项目Attr.json的逻辑）
      const positionWeights = this.getPositionWeights(position);

      // 计算进攻值
      const attack = Math.round(
        (primaryAttributes.speed || 0) * (positionWeights.attack.speed || 0) +
        (primaryAttributes.shooting || 0) * (positionWeights.attack.shooting || 0) +
        (primaryAttributes.passing || 0) * (positionWeights.attack.passing || 0) +
        (primaryAttributes.dribbling || 0) * (positionWeights.attack.dribbling || 0) +
        (primaryAttributes.physicality || 0) * (positionWeights.attack.physicality || 0)
      );

      // 计算防守值
      const defend = Math.round(
        (primaryAttributes.defending || 0) * (positionWeights.defend.defending || 0) +
        (primaryAttributes.physicality || 0) * (positionWeights.defend.physicality || 0) +
        (primaryAttributes.speed || 0) * (positionWeights.defend.speed || 0) +
        (primaryAttributes.goalkeeping || 0) * (positionWeights.defend.goalkeeping || 0)
      );

      return {
        attack: Math.max(attack, 0),
        defend: Math.max(defend, 0),
      };
    } catch (error) {
      this.logger.error('计算二级属性失败', error);
      return {
        attack: 0,
        defend: 0,
      };
    }
  }

  /**
   * 获取位置权重配置
   * 基于old项目Attr.json的位置权重
   */
  private getPositionWeights(position: string): any {
    // 基于old项目Attr.json的简化权重配置
    const weights = {
      'GK': {
        attack: { speed: 0, shooting: 0, passing: 0.1, dribbling: 0, physicality: 0.1 },
        defend: { defending: 0.2, physicality: 0.3, speed: 0.1, goalkeeping: 1.0 }
      },
      'DC': {
        attack: { speed: 0, shooting: 0, passing: 0.1, dribbling: 0, physicality: 0.2 },
        defend: { defending: 0.8, physicality: 0.6, speed: 0.2, goalkeeping: 0 }
      },
      'ST': {
        attack: { speed: 0.6, shooting: 0.8, passing: 0.2, dribbling: 0.4, physicality: 0.3 },
        defend: { defending: 0.1, physicality: 0.2, speed: 0.1, goalkeeping: 0 }
      },
      // 默认权重
      'DEFAULT': {
        attack: { speed: 0.3, shooting: 0.4, passing: 0.3, dribbling: 0.3, physicality: 0.2 },
        defend: { defending: 0.4, physicality: 0.3, speed: 0.2, goalkeeping: 0 }
      }
    };

    return weights[position] || weights['DEFAULT'];
  }

  /**
   * 获取角色信仰技能数据
   * 基于old项目：需要调用Character服务获取beliefSkill数据
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async getCharacterBeliefSkillData(characterId: string): Promise<XResult<any>> {
    // 调用Character服务获取信仰技能数据（基于old项目player.beliefSkill）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.getInfo',
      { characterId }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.warn(`获取角色信息失败: ${characterId}, ${result.message}`);
      return XResultUtils.ok({ skillList: [] });
    }

    const characterData = result.data;
    if (!characterData) {
      this.logger.warn(`角色数据不存在: ${characterId}`);
      return XResultUtils.ok({ skillList: [] });
    }

    // 从角色数据中提取信仰技能信息
    const beliefSkillData = characterData.beliefSkill || {};

    // 转换为标准格式
    const skillList = [];
    if (beliefSkillData.skillList && Array.isArray(beliefSkillData.skillList)) {
      for (const skill of beliefSkillData.skillList) {
        skillList.push({
          skillId: skill.skillId,
          level: skill.level || 1,
          status: skill.status || 0, // 0=未解锁，1=已解锁
        });
      }
    }

    return XResultUtils.ok({ skillList });
  }

  /**
   * 检查球员是否享有信仰技能加成
   * 基于old项目: checkHeroIsHaveBeliefSkillAttr
   */
  private async checkHeroHasBeliefSkillBonus(hero: any, skillId: number): Promise<XResult<boolean>> {
    try {
      // 获取信仰技能配置
      const skillConfig = await this.gameConfig.beliefSkill.get(skillId);
      if (!skillConfig) {
        return XResultUtils.ok(false);
      }

      // 获取球员配置
      const heroConfig = await this.gameConfig.hero.get(hero.resId);
      if (!heroConfig) {
        return XResultUtils.ok(false);
      }

      // 检查球员位置是否匹配技能的适用位置
      const heroPosition = heroConfig.position1; // 球员主位置

      // 检查技能的12个适用位置
      for (let i = 1; i <= 12; i++) {
        const positionKey = `addPosition${i}`;
        const skillPosition = skillConfig[positionKey];

        if (skillPosition && skillPosition === heroPosition) {
          return XResultUtils.ok(true);
        }
      }

      return XResultUtils.ok(false);
    } catch (error) {
      this.logger.error('检查球员信仰技能加成失败', error);
      return XResultUtils.error('检查球员信仰技能加成失败', 'CHECK_HERO_BELIEF_SKILL_BONUS_ERROR');
    }
  }

  /**
   * 获取角色图鉴数据
   * 基于old项目：需要调用Character服务获取图鉴数据
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async getCharacterHandbookData(characterId: string): Promise<XResult<any>> {
    // 调用Character服务获取图鉴数据（基于old项目player.footballGround.getBallHandbook）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.getInfo',
      { characterId }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.warn(`获取角色信息失败: ${characterId}, ${result.message}`);
      return XResultUtils.ok(this.getEmptyHandbookData());
    }

    const characterData = result.data;
    if (!characterData) {
      this.logger.warn(`角色数据不存在: ${characterId}`);
      return XResultUtils.ok(this.getEmptyHandbookData());
    }

    // 从角色数据中提取图鉴信息
    const footballGroundData = characterData.footballGround || {};

    const handbookData = {
      ballHandbook: footballGroundData.ballHandbook || [],        // 已激活的球员图鉴ID列表
      ballComHandbook: footballGroundData.ballComHandbook || [],  // 组合图鉴数据
      ballActual: footballGroundData.ballActual || 0,             // 球员实力加成
      ballComActual: footballGroundData.ballComActual || 0,       // 组合实力加成
      ballComCount: footballGroundData.ballComCount || 0          // 组合激活个数
    };

    return XResultUtils.ok(handbookData);
  }

  /**
   * 获取空的图鉴数据
   */
  private getEmptyHandbookData(): any {
    return {
      ballHandbook: [],
      ballComHandbook: [],
      ballActual: 0,
      ballComActual: 0,
      ballComCount: 0
    };
  }

  /**
   * 获取球员主力阵容信息
   * 基于old项目: getHeroInMainTeamUid
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async getHeroMainFormationInfo(heroId: string): Promise<XResult<any>> {
    // 调用Character服务获取球员阵容信息（基于old项目getHeroInMainTeamUid）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'formation.getHeroInMainTeamUid',
      { heroId }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.debug(`球员不在主力阵容中: ${heroId}, ${result.message}`);
      const emptyFormationInfo = {
        isInMainFormation: false,
        teamUid: null,
        position: null
      };
      return XResultUtils.ok(emptyFormationInfo);
    }

    const formationData = result.data || {};
    const formationInfo = {
      isInMainFormation: !!formationData.teamUid,
      teamUid: formationData.teamUid,
      position: formationData.position
    };

    return XResultUtils.ok(formationInfo);
  }

  /**
   * 计算教练属性加成
   * 基于old项目: calcHeroTrainerAttr
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async calculateTrainerAttributeBonus(heroId: string, teamUid: string): Promise<XResult<any>> {
    // 调用Character服务计算教练属性加成（基于old项目calcHeroTrainerAttr）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'formation.calcHeroTrainerAttr',
      { heroId, teamUid }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.debug(`获取教练属性加成失败: ${heroId}, ${teamUid}, ${result.message}`);
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }

    const attributeData = result.data;
    if (!attributeData) {
      this.logger.debug(`教练属性加成数据为空: ${heroId}, ${teamUid}`);
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }

    // 转换old项目属性格式到新格式
    const convertedAttributes = this.convertOldAttributesToNew(attributeData);
    return XResultUtils.ok(convertedAttributes);
  }

  /**
   * 计算战术加成
   * 基于old项目: calcHeroTacticsAttr
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async calculateTacticsAttributeBonus(heroId: string, teamUid: string): Promise<XResult<any>> {
    // 调用Character服务计算战术加成（基于old项目calcHeroTacticsAttr）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'formation.calcHeroTacticsAttr',
      { heroId, teamUid }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.debug(`获取战术加成失败: ${heroId}, ${teamUid}, ${result.message}`);
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }

    const attributeData = result.data;
    if (!attributeData) {
      this.logger.debug(`战术加成数据为空: ${heroId}, ${teamUid}`);
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }

    // 转换old项目属性格式到新格式
    const convertedAttributes = this.convertOldAttributesToNew(attributeData);
    return XResultUtils.ok(convertedAttributes);
  }

  /**
   * 计算教练技能加成
   * 基于old项目: calcTrainerSkillAttr
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async calculateTrainerSkillBonus(heroId: string, teamUid: string): Promise<XResult<any>> {
    // 调用Character服务计算教练技能加成（基于old项目calcTrainerSkillAttr）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'formation.calcTrainerSkillAttr',
      { heroId, teamUid }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.debug(`获取教练技能加成失败: ${heroId}, ${teamUid}, ${result.message}`);
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }

    const attributeData = result.data;
    if (!attributeData) {
      this.logger.debug(`教练技能加成数据为空: ${heroId}, ${teamUid}`);
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }

    // 转换old项目属性格式到新格式
    const convertedAttributes = this.convertOldAttributesToNew(attributeData);
    return XResultUtils.ok(convertedAttributes);
  }

  /**
   * 累加属性加成
   */
  private addAttributeBonus(target: any, source: any): void {
    target.speed += source.speed || 0;
    target.shooting += source.shooting || 0;
    target.passing += source.passing || 0;
    target.defending += source.defending || 0;
    target.dribbling += source.dribbling || 0;
    target.physicality += source.physicality || 0;
    target.goalkeeping += source.goalkeeping || 0;
  }

  /**
   * 转换old项目属性名到新架构
   * 基于old项目的属性名映射
   */
  private convertOldAttributesToNew(oldAttributes: any): any {
    return {
      speed: oldAttributes.Speed || 0,
      shooting: oldAttributes.Finishing || 0,
      passing: oldAttributes.Passing || 0,
      defending: oldAttributes.StandingTackle || 0,
      dribbling: oldAttributes.Dribbling || 0,
      physicality: oldAttributes.Strength || 0,
      goalkeeping: oldAttributes.Save || 0,
    };
  }



  /**
   * 根据球员品质获取最大星级
   * 基于old项目: 不同品质球员有不同的星级上限
   */
  private getMaxStarLevelByQuality(color: string): number {
    const maxStarLevels = {
      '白': 3,
      '绿': 4,
      '蓝': 5,
      '紫': 6,
      '金': 7,
      '红': 8,
    };
    return maxStarLevels[color] || 5;
  }

  /**
   * 计算升星属性加成
   * 基于old项目: _InnerCalcUpgradeStarValue
   */
  private calculateStarAttributeBonus(hero: any): any {
    const star = hero.star || 0;
    const bonus = star * 2; // 每星+2属性（简化计算）

    return {
      speed: bonus,
      shooting: bonus,
      passing: bonus,
      defending: bonus,
      dribbling: bonus,
      physicality: bonus,
      goalkeeping: bonus,
    };
  }

  /**
   * 计算特训属性加成
   * 基于old项目: training相关计算
   *
   * 实现逻辑：
   * 1. 基于新架构的training数据结构计算特训加成
   * 2. 根据各属性的训练阶段计算加成值
   * 3. 特训加成直接影响球员的基础属性
   */
  private calculateTrainingAttributeBonus(hero: any): any {
    try {
      const training = hero.training || {};

      // 基于新架构：使用训练阶段信息计算特训加成
      // 每个属性的训练阶段越高，加成越大

      let bonus = {
        speed: 0,
        shooting: 0,
        passing: 0,
        defending: 0,
        dribbling: 0,
        physicality: 0,
        goalkeeping: 0,
      };

      // 计算各属性的特训加成
      // 基于训练阶段计算：每个阶段提供固定的属性加成
      const stageBonus = 2; // 每个训练阶段提供2点属性加成

      if (training.speedStage) {
        bonus.speed = (training.speedStage.stage - 1) * stageBonus;
      }

      if (training.shootingStage) {
        bonus.shooting = (training.shootingStage.stage - 1) * stageBonus;
      }

      if (training.passingStage) {
        bonus.passing = (training.passingStage.stage - 1) * stageBonus;
      }

      if (training.defendingStage) {
        bonus.defending = (training.defendingStage.stage - 1) * stageBonus;
      }

      if (training.dribblingStage) {
        bonus.dribbling = (training.dribblingStage.stage - 1) * stageBonus;
      }

      if (training.physicalityStage) {
        bonus.physicality = (training.physicalityStage.stage - 1) * stageBonus;
      }

      // 门将特训（基于整体训练等级）
      const overallTrainingLevel = Math.max(
        training.speedStage?.stage || 1,
        training.shootingStage?.stage || 1,
        training.passingStage?.stage || 1,
        training.defendingStage?.stage || 1,
        training.dribblingStage?.stage || 1,
        training.physicalityStage?.stage || 1
      );

      bonus.goalkeeping = (overallTrainingLevel - 1) * stageBonus;

      return bonus;
    } catch (error) {
      this.logger.error('计算特训属性加成失败', error);
      return this.getEmptyAttributeBonus();
    }
  }

  /**
   * 计算突破属性加成
   * 基于old项目: breakthrough相关计算
   */
  private calculateBreakthroughAttributeBonus(hero: any): any {
    const breakthrough = hero.breakthrough || [];
    const totalBreakthrough = breakthrough.reduce((sum, value) => sum + value, 0);

    // 突破值平均分配到各属性
    const bonus = Math.floor(totalBreakthrough / 7);

    return {
      speed: bonus,
      shooting: bonus,
      passing: bonus,
      defending: bonus,
      dribbling: bonus,
      physicality: bonus,
      goalkeeping: bonus,
    };
  }

  /**
   * 计算球员总实力
   * 基于old项目的实力计算公式
   */
  private calculateTotalPower(attributes: any): number {
    const {
      speed = 0,
      shooting = 0,
      passing = 0,
      defending = 0,
      dribbling = 0,
      physicality = 0,
      goalkeeping = 0,
    } = attributes;

    // old项目实力计算公式（权重分配）
    const totalPower = Math.floor(
      speed * 0.15 +
      shooting * 0.20 +
      passing * 0.15 +
      defending * 0.15 +
      dribbling * 0.15 +
      physicality * 0.15 +
      goalkeeping * 0.05
    );

    return Math.max(totalPower, 1); // 最低实力为1
  }

  /**
   * 计算球员综合评分
   * 基于old项目的评分算法
   */
  private calculateOverallRating(attributes: any, level: number): number {
    const totalPower = this.calculateTotalPower(attributes);
    const levelBonus = level * 2; // 等级加成
    const rating = Math.floor((totalPower + levelBonus) / 10);

    return Math.min(Math.max(rating, 1), 100); // 评分范围1-100
  }

  /**
   * 计算升星费用
   * 基于old项目: 升星费用随等级和星级增加
   * 使用Result模式，正确处理费用计算结果
   */
  private async calculateStarUpgradeCost(level: number, starLevel: number): Promise<XResult<number>> {
    try {
      const baseCost = 10000;
      const levelMultiplier = level * 500;
      const starMultiplier = starLevel * 2000;
      const totalCost = baseCost + levelMultiplier + starMultiplier;

      return XResultUtils.ok(totalCost);
    } catch (error) {
      this.logger.error('计算升星费用失败', error);
      return XResultUtils.error('计算升星费用失败', 'CALCULATE_STAR_UPGRADE_COST_ERROR');
    }
  }

  /**
   * 计算重新突破费用
   * 基于old项目: SystemParam中的rebreakoutCost
   * 使用Result模式，正确处理配置获取结果
   */
  private async calculateReBreakOutCost(): Promise<XResult<number>> {
    try {
      // 基于old项目：重新突破使用钻石，费用固定
      // 根据old项目代码，rebreakoutCost对应SystemParam的ID需要确定
      // 暂时使用ID 100作为重新突破费用的配置ID
      const systemConfig = await this.gameConfig.systemParam.get(100);
      const baseCost = systemConfig?.parameter || 100; // 默认100钻石

      // TODO: 检查是否有活动折扣
      // const discount = this.getActivityPrice(baseCost, 33);

      return XResultUtils.ok(baseCost);
    } catch (error) {
      this.logger.error('计算重新突破费用失败', error);
      return XResultUtils.error('计算重新突破费用失败', 'CALCULATE_RE_BREAK_OUT_COST_ERROR');
    }
  }

  /**
   * 检查并扣除钻石
   * 基于old项目: checkResourceIsEnough + deductMoney
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async checkAndDeductDiamond(characterId: string, amount: number): Promise<XResult<any>> {
    this.logger.log(`检查并扣除钻石: ${characterId}, 金额: ${amount}`);

    // 1. 先获取角色信息检查余额
    const characterInfoResult = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.getInfo',
      { characterId }
    );

    if (XResultUtils.isFailure(characterInfoResult)) {
      return XResultUtils.error(`获取角色信息失败: ${characterInfoResult.message}`, characterInfoResult.code);
    }

    const characterInfo = characterInfoResult.data;
    if (!characterInfo) {
      return XResultUtils.error('角色信息不存在', 'CHARACTER_INFO_NOT_FOUND');
    }

    const currentCash = characterInfo.cash || 0;
    if (currentCash < amount) {
      this.logger.warn(`钻石不足: ${characterId}, 需要: ${amount}, 当前: ${currentCash}`);
      return XResultUtils.failure('钻石不足', 'INSUFFICIENT_CASH', {
        required: amount,
        current: currentCash,
        deficit: amount - currentCash,
      });
    }

    // 2. 扣除钻石
    const deductResult = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.currency.subtract',
      {
        characterId,
        currencyDto: {
          currencyType: 'cash',
          amount: amount,
          reason: 'hero_cultivation'
        }
      }
    );

    if (XResultUtils.isFailure(deductResult)) {
      return XResultUtils.error(`扣除钻石失败: ${deductResult.message}`, deductResult.code);
    }

    this.logger.log(`钻石扣除成功: ${characterId}, 金额: ${amount}`);
    return XResultUtils.ok({ deductedAmount: amount, remainingCash: currentCash - amount });
  }

  /**
   * 根据类型获取属性组
   * 基于old项目: getInitPropertyByType中的switch逻辑
   */
  private getAttributeGroupsByType(type: number): string[] {
    switch (type) {
      case 1: // 素质
        return ['speed', 'physicality', 'explosiveForce']; // Speed, Strength, ExplosiveForce
      case 2: // 防守
        return ['jumping', 'defending', 'slidingTackle']; // Jumping, StandingTackle, SlidingTackle
      case 3: // 射术
        return ['shooting', 'heading', 'longShots']; // Finishing, Heading, LongShots
      case 4: // 传球
        return ['passing', 'longPassing', 'cornerKick']; // Passing, LongPassing, CornerKick
      case 5: // 盘带
        return ['dribbling', 'volleys', 'freeKick']; // Dribbling, Volleys, FreeKick
      case 6: // 门将
        return ['goalkeeping', 'attack', 'penalties']; // Save, Attack, Penalties
      default:
        return [];
    }
  }

  /**
   * 从球员配置中获取属性值
   * 基于old项目: getInitProperty方法
   */
  private getHeroConfigAttribute(heroConfig: any, attrName: string): number {
    // 属性名映射：新架构 -> old项目
    const attributeMapping = {
      speed: 'speed',
      physicality: 'strength',
      explosiveForce: 'explosiveForce',
      jumping: 'jumping',
      defending: 'standingTackle',
      slidingTackle: 'slidingTackle',
      shooting: 'finishing',
      heading: 'heading',
      longShots: 'longShots',
      passing: 'passing',
      longPassing: 'longPassing',
      cornerKick: 'cornerKick',
      dribbling: 'dribbling',
      volleys: 'volleys',
      freeKick: 'freeKick',
      goalkeeping: 'save',
      attack: 'attack',
      penalties: 'penalties',
    };

    const configAttrName = attributeMapping[attrName];
    return configAttrName ? (heroConfig[configAttrName] || 0) : 0;
  }

  /**
   * 获取空的属性加成对象
   */
  private getEmptyAttributeBonus(): any {
    return {
      speed: 0,
      shooting: 0,
      passing: 0,
      defending: 0,
      dribbling: 0,
      physicality: 0,
      goalkeeping: 0,
    };
  }
}
