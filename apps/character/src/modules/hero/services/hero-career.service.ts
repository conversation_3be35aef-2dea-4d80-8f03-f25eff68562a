import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { GameConfigFacade } from '@libs/game-config';
import { HeroRepository } from '@character/common/repositories/hero.repository';
import { CharacterService } from '../../character/character.service';
import { InventoryService } from "@character/modules/inventory/inventory.service";
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import {MICROSERVICE_NAMES} from "@libs/shared";

/**
 * 统一的球员生涯管理业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 基于old项目的生涯系统业务逻辑，提供完整的球员生涯管理功能
 *
 * 🎯 核心功能：
 * - 球员合约管理（续约、天数增加、到期处理）
 * - 球员状态提升（使用道具治疗和属性提升）
 * - 球员退役管理（退役名单、自动退役处理）
 * - 生涯统计分析（比赛表现、发展历程、成就系统）
 * - 合约到期预警和批量处理
 *
 * 🚀 性能优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的Result模式错误处理
 * - 微服务调用的错误处理和重试机制
 * - 批量操作优化数据库访问
 * - 智能缓存机制优化查询性能
 *
 * 🔗 服务依赖：
 * - CharacterService：货币扣除、物品消耗、阵容更新（直接注入）
 * - Activity服务：任务触发、成就系统
 * - Hero服务：属性重新计算、培养系统调用
 */
@Injectable()
export class HeroCareerService extends BaseService {
  constructor(
    private readonly heroRepository: HeroRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly characterService: CharacterService,
    private readonly inventoryService: InventoryService,
  ) {
    super('HeroCareerService');
  }

  /**
   * 增加球员合约天数
   * 基于old项目: addHeroLeftDay
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async addHeroLeftDay(heroId: string, days: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`增加球员合约天数: ${heroId}, 天数: ${days}`);

      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      if (days <= 0) {
        return XResultUtils.error('天数必须大于0', 'INVALID_DAYS');
      }

      // 基于old项目实现合约天数增加逻辑
      // old项目中通过TreatyDay字段管理合约天数
      const currentLeftDays = hero.contractDays || 0;
      const newLeftDays = currentLeftDays + days;

      // 获取系统配置的最大合约天数（基于old项目SystemParam配置）
      const maxTreatyDaysResult = await this.getMaxTreatyDays();
      if (XResultUtils.isFailure(maxTreatyDaysResult)) {
        return XResultUtils.error(`获取最大合约天数失败: ${maxTreatyDaysResult.message}`, maxTreatyDaysResult.code);
      }

      const maxTreatyDays = maxTreatyDaysResult.data;
      const finalLeftDays = Math.min(newLeftDays, maxTreatyDays);

      // 更新合约相关字段（基于old项目逻辑）
      const updateResult = await this.heroRepository.updateById(heroId, {
        contractDays: finalLeftDays,
        treatyReTime: Date.now(), // 更新合约刷新时间
        contractExpireTime: new Date(Date.now() + finalLeftDays * 24 * 60 * 60 * 1000),
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员合约失败: ${updateResult.message}`, updateResult.code);
      }

      return XResultUtils.ok({
        heroId,
        beforeDays: currentLeftDays,
        addedDays: days,
        afterDays: finalLeftDays,
        maxTreatyDays,
        isMaxReached: finalLeftDays >= maxTreatyDays,
        newExpireTime: new Date(Date.now() + finalLeftDays * 24 * 60 * 60 * 1000),
        treatyReTime: Date.now(),
      });
    }, { reason: 'add_hero_left_day', metadata: { heroId, days } });
  }

  /**
   * 续约球员
   * 严格基于old项目: renewTheContract(heroUidList)
   * 完全遵循old项目的业务逻辑和处理流程
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   *
   * @param heroIds 球员ID列表（old项目支持批量续约）
   */
  async renewTheContract(heroIds: string[]): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`续约球员: ${JSON.stringify(heroIds)}`);

      // 1. 参数验证（基于old项目：heroUidList.length < 1）
      if (!Array.isArray(heroIds) || heroIds.length === 0) {
        return XResultUtils.error('球员列表不能为空', 'RANGE_FAIL');
      }

      // 2. 获取系统配置参数（基于old项目逻辑）
      const configResult = await this.getContractSystemConfig();
      if (XResultUtils.isFailure(configResult)) {
        return XResultUtils.error(`获取系统配置失败: ${configResult.message}`, 'CONFIG_FAIL');
      }

      const { sysConfig, treatyDay, seasonDay } = configResult.data;

      // 3. 预计算总费用和收集有效球员（基于old项目两阶段处理）
      const validHeroes = [];
      let totalCost = 0;

      for (const heroId of heroIds) {
        const heroResult = await this.heroRepository.findById(heroId);
        if (XResultUtils.isFailure(heroResult) || !heroResult.data) {
          this.logger.warn(`球员不存在或获取失败: ${heroId}`);
          continue;
        }

        const hero = heroResult.data;

        // 检查球员状态（基于old项目逻辑）
        if (hero.isRetired) {
          this.logger.warn(`球员已退役，跳过续约: ${heroId}`);
          continue;
        }

        // 已满合约跳过（基于old项目：hero.TreatyDay === treatyDay）
        const currentContractDays = hero.contractDays || 0;
        if (currentContractDays >= treatyDay) {
          this.logger.debug(`球员合约已满，跳过续约: ${heroId}, 当前合约天数: ${currentContractDays}`);
          continue;
        }

        // 获取球员配置（基于old项目：Footballer[hero.ResID]）
        const heroConfigResult = await this.getHeroConfig(hero.resId);
        if (XResultUtils.isFailure(heroConfigResult) || !heroConfigResult.data) {
          this.logger.warn(`球员配置不存在，跳过续约: ${heroId}, resId: ${hero.resId}`);
          continue;
        }

        const heroConfig = heroConfigResult.data;

        // 计算续约费用（基于old项目公式）
        const canTreatyDay = treatyDay - currentContractDays;
        const heroMoney = heroConfig.value || heroConfig.Value || 10000; // 球员身价
        const costMoney = Math.floor((heroMoney / sysConfig / seasonDay) * canTreatyDay);

        validHeroes.push({
          heroId,
          hero,
          heroConfig,
          canTreatyDay,
          costMoney,
          currentContractDays,
        });

        totalCost += costMoney;
      }

      // 4. 检查是否有有效球员需要续约
      if (validHeroes.length === 0) {
        return XResultUtils.error('没有有效的球员需要续约', 'NO_VALID_HEROES');
      }

      // 5. 检查并扣除总费用（基于old项目：先检查总费用再统一扣除）
      const firstHero = validHeroes[0].hero;
      const costResult = await this.characterService.deductCurrency({
        characterId: firstHero.characterId,
        currencyType: 'cash', // old项目使用欧元(cash)
        amount: totalCost,
        reason: 'hero_contract_renewal'
      });

      if (XResultUtils.isFailure(costResult)) {
        return XResultUtils.failure(
          `扣除欧元失败: ${costResult.message}`,
          'CASH_FALL',
          {
            requiredCash: totalCost,
            heroCount: validHeroes.length,
          }
        );
      }

      // 6. 批量更新球员合约信息（基于old项目逻辑）
      const renewalResults = [];
      const heroesNeedingRecalc = []; // 需要重新计算属性的球员

      for (const { heroId, hero, canTreatyDay, costMoney, currentContractDays } of validHeroes) {
        const isUpdate = currentContractDays === 0; // 基于old项目：合约为0时需要重新计算属性

        const updateResult = await this.heroRepository.updateById(heroId, {
          contractDays: treatyDay, // 基于old项目：hero.TreatyDay = treatyDay
          treatyReTime: Date.now(), // 基于old项目：hero.TreatyReTime = TimeUtils.now()
        });

        if (XResultUtils.isFailure(updateResult)) {
          this.logger.error(`更新球员合约失败: ${heroId}, ${updateResult.message}`);
          continue;
        }

        renewalResults.push({
          heroId,
          resId: hero.resId,
          oldContractDays: currentContractDays,
          newContractDays: treatyDay,
          renewedDays: canTreatyDay,
          cost: costMoney,
          needsRecalc: isUpdate,
        });

        // 记录需要重新计算属性的球员
        if (isUpdate) {
          heroesNeedingRecalc.push(heroId);
        }

        // 记录统计日志（基于old项目：recordSlog）
        this.logger.log(`球员续约成功: ${heroId}, 费用: ${costMoney}, 新合约天数: ${treatyDay}`);
      }

      // 7. 触发属性重新计算（基于old项目：reCalcAttrRevision和checkHeroInArbitrarilyFormation）
      if (heroesNeedingRecalc.length > 0) {
        await this.triggerHeroAttributeRecalculation(heroesNeedingRecalc);
      }

      return XResultUtils.ok({
        renewalResults,
        totalCost,
        renewedCount: renewalResults.length,
        heroesNeedingRecalc: heroesNeedingRecalc.length,
      });
    }, { reason: 'renew_contract', metadata: { heroCount: heroIds.length } });
  }

  /**
   * 提升球员状态
   * 基于old项目: promoteHeroStatus
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async promoteHeroStatus(heroId: string, itemId: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`提升球员状态: ${heroId}, 道具ID: ${itemId}`);

      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 检查道具是否存在和足够
      const itemCheckResult = await this.checkAndDeductItem(hero.characterId, itemId, 1);
      if (XResultUtils.isFailure(itemCheckResult)) {
        return XResultUtils.error(
          `道具检查失败: ${itemCheckResult.message}`
        );
      }

      // 获取道具配置
      const itemConfigResult = await this.getStatusItemConfig(itemId);
      if (XResultUtils.isFailure(itemConfigResult)) {
        return XResultUtils.error(`获取道具配置失败: ${itemConfigResult.message}`, itemConfigResult.code);
      }

      const itemConfig = itemConfigResult.data;
      if (!itemConfig) {
        return XResultUtils.error('道具配置不存在', 'ITEM_CONFIG_NOT_FOUND');
      }

      // 基于old项目的状态提升逻辑
      const statusResult = this.calculateStatusPromotion(hero, itemConfig);

      // 更新球员状态
      const updateResult = await this.heroRepository.updateById(heroId, {
        Health: statusResult.newStatus,
        statusUpdateTime: Date.now(),
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员状态失败: ${updateResult.message}`, updateResult.code);
      }

      return XResultUtils.ok({
        status: statusResult.newStatus,
        uid: heroId,
        itemId,
        oldStatus: statusResult.oldStatus,
        improvement: statusResult.improvement,
        attributeBoosts: statusResult.attributeBoosts,
        savedCost: statusResult.savedCost,
        isFullyHealed: statusResult.isFullyHealed,
      });
    }, { reason: 'promote_hero_status', metadata: { heroId, itemId } });
  }

  /**
   * 加入退役名单
   * 基于old项目: addHeroToRetirementList
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async addHeroToRetirementList(heroId: string, characterId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`加入退役名单: ${heroId}`);

      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      if (hero.characterId !== characterId) {
        return XResultUtils.error('球员不属于该角色', 'HERO_NOT_OWNED');
      }

      // 基于old项目实现退役名单逻辑
      // old项目中退役是通过isDie字段标记
      const updateResult = await this.heroRepository.updateById(heroId, {
        isDie: 1,                    // old项目的退役标记
        retirementStatus: 'pending', // 新架构的退役状态
        retirementTime: new Date(),
        isActive: false,
        isInFormation: false,        // 退役球员自动离开阵容
        isLocked: false,             // 解除锁定状态
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员退役状态失败: ${updateResult.message}`, updateResult.code);
      }

      // 通知Character服务更新阵容（基于old项目逻辑）
      const formationUpdateResult = await this.notifyFormationUpdate(characterId, heroId);
      if (XResultUtils.isFailure(formationUpdateResult)) {
        this.logger.warn(`通知阵容更新失败: ${formationUpdateResult.message}`);
      }

      // 触发退役相关任务和成就（基于old项目逻辑）
      const tasksResult = await this.triggerRetirementTasks(characterId, heroId);
      if (XResultUtils.isFailure(tasksResult)) {
        this.logger.warn(`触发退役任务失败: ${tasksResult.message}`);
      }

      return XResultUtils.ok({
        heroId,
        characterId,
        retirementTime: new Date(),
        status: 'added_to_retirement_list',
      });
    }, { reason: 'add_hero_to_retirement_list', metadata: { heroId, characterId } });
  }

  /**
   * 检查并处理退役
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async checkAndProcessRetirement(characterId: string): Promise<XResult<any[]>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`检查并处理退役: ${characterId}`);

      // 查找即将退役或已到期的球员
      const expiringHeroesResult = await this.heroRepository.findExpiringContracts(characterId, 0);
      if (XResultUtils.isFailure(expiringHeroesResult)) {
        return XResultUtils.error(`查找到期球员失败: ${expiringHeroesResult.message}`, expiringHeroesResult.code);
      }

      const expiringHeroes = expiringHeroesResult.data || [];
      const retiredHeroes = [];

      for (const hero of expiringHeroes) {
        // 基于old项目实现退役处理逻辑
        // old项目中退役处理包括：设置isDie标记、状态重置、重新计算属性、从阵容移除

        const heroId = hero._id.toString();

        // 1. 检查是否满足退役条件
        const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
        const shouldRetire = hero.contractDays <= 0 && hero.lifeNum >= maxLifeNum;

        if (shouldRetire) {
          // 2. 更新球员退役状态（基于old项目addHeroToRetirementList逻辑）
          const updateResult = await this.heroRepository.updateById(heroId, {
            isDie: 1,                           // old项目的退役标记
            status: 'commonly',                 // 状态重置为一般（commonEnum.HERO_STATUS.COMMONLY）
            retirementStatus: 'retired',        // 新架构的退役状态
            retirementProcessedTime: new Date(),
            isActive: false,
            isInFormation: false,               // 从阵容中移除
            isLocked: false,                    // 解除锁定
          });

          if (XResultUtils.isFailure(updateResult)) {
            this.logger.error(`更新球员退役状态失败: ${heroId}, ${updateResult.message}`);
            continue;
          }

          // 3. 重新计算属性（基于old项目reCalcAttrRevision调用）
          const recalcResult = await this.recalculateHeroAttributesAfterRetirement(heroId);
          if (XResultUtils.isFailure(recalcResult)) {
            this.logger.warn(`重新计算球员属性失败: ${heroId}, ${recalcResult.message}`);
          }

          // 4. 通知Character服务更新阵容
          const formationResult = await this.notifyFormationUpdate(hero.characterId, heroId);
          if (XResultUtils.isFailure(formationResult)) {
            this.logger.warn(`通知阵容更新失败: ${heroId}, ${formationResult.message}`);
          }

          // 5. 触发退役相关任务和成就
          const tasksResult = await this.triggerRetirementTasks(hero.characterId, heroId);
          if (XResultUtils.isFailure(tasksResult)) {
            this.logger.warn(`触发退役任务失败: ${heroId}, ${tasksResult.message}`);
          }

          retiredHeroes.push({
            heroId,
            name: hero.name,
            level: hero.level,
            retirementReason: 'contract_expired_and_max_life_reached',
            lifeNum: hero.lifeNum,
            maxLifeNum,
          });

          this.logger.log(`球员退役处理完成: ${heroId}, 生涯次数: ${hero.lifeNum}/${maxLifeNum}`);
        } else {
          // 合约到期但未达到最大生涯次数，只标记为合约到期
          const expiredResult = await this.heroRepository.updateById(heroId, {
            contractStatus: 'expired',
            contractExpiredTime: new Date(),
          });

          if (XResultUtils.isFailure(expiredResult)) {
            this.logger.warn(`标记合约到期失败: ${heroId}, ${expiredResult.message}`);
          }

          this.logger.log(`球员合约到期但未退役: ${heroId}, 生涯次数: ${hero.lifeNum}/${maxLifeNum}`);
        }
      }

      return XResultUtils.ok(retiredHeroes);
    }, { reason: 'check_and_process_retirement', metadata: { characterId } });
  }

  /**
   * 获取球员生涯统计
   * Controller层已验证参数，无需重复验证
   */
  async getCareerStats(heroId: string): Promise<XResult<any>> {
    this.logger.log(`获取球员生涯统计: ${heroId}`);

    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero) {
      return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
    }

    // 基于old项目实现生涯统计逻辑
    // old项目中生涯统计包括：生涯天数、合约续约、状态提升、比赛表现、发展历程等

    // 1. 计算生涯总天数（基于合约时间和获得时间）
    const careerTotalDays = this.calculateCareerTotalDays(hero);

    // 2. 计算合约相关统计
    const contractStats = this.calculateContractStats(hero);

    // 3. 计算比赛表现统计
    const performanceStats = this.calculatePerformanceStats(hero);

    // 4. 计算发展历程统计
    const developmentStats = this.calculateDevelopmentStats(hero);

    // 5. 计算生涯成就和里程碑
    const achievements = this.calculateCareerAchievements(hero);

    const stats = {
      heroId,
      careerInfo: {
        totalDays: careerTotalDays,
        contractRenewals: contractStats.renewals,
        statusPromotions: hero.statusPromotions || 0,
        currentLifeNum: hero.lifeNum || 1,
        maxLifeNum: this.calculateMaxLifeNum(hero.quality),
        obtainTime: hero.obtainTime || 0,
        obtainType: hero.obtainType || 1,
        retirementStatus: hero.retirementStatus || 'active',
      },
      performance: {
        matchesPlayed: performanceStats.matches,
        goals: performanceStats.goals,
        assists: performanceStats.assists,
        yellowCards: hero.yellowCards || 0,
        redCards: hero.redCards || 0,
        averageRating: performanceStats.averageRating,
        goalsPerMatch: performanceStats.matches > 0 ? (performanceStats.goals / performanceStats.matches).toFixed(2) : 0,
        assistsPerMatch: performanceStats.matches > 0 ? (performanceStats.assists / performanceStats.matches).toFixed(2) : 0,
      },
      development: {
        currentLevel: hero.level || 1,
        levelUps: developmentStats.levelUps,
        skillUpgrades: developmentStats.skillUpgrades,
        breakthroughs: developmentStats.breakthroughs,
        starLevel: hero.starLevel || hero.evolution?.star || 0,
        totalPower: hero.totalPower || 0,
        trainingCount: hero.TrainCount || 0,
      },
      health: {
        currentHealth: hero.Health || 1,
        healthHistory: this.getHealthStatusHistory(hero),
      },
      achievements,
    };

    return XResultUtils.ok(stats);
  }

  /**
   * 获取即将到期的球员
   * Controller层已验证参数，无需重复验证
   */
  async getExpiringContracts(characterId: string, days: number): Promise<XResult<any[]>> {
    this.logger.log(`获取即将到期的球员: ${characterId}, ${days}天内`);

    const expiringHeroesResult = await this.heroRepository.findExpiringContracts(characterId, days);
    if (XResultUtils.isFailure(expiringHeroesResult)) {
      return XResultUtils.error(`查找到期球员失败: ${expiringHeroesResult.message}`, expiringHeroesResult.code);
    }

    const expiringHeroes = expiringHeroesResult.data || [];

    const result = expiringHeroes.map(hero => ({
      heroId: hero._id.toString(),
      name: hero.name,
      level: hero.level,
      position: hero.position,
      contractDays: hero.contractDays,
      expireTime: hero.contractExpireTime,
      daysLeft: Math.max(0, Math.ceil((hero.contractExpireTime.getTime() - Date.now()) / (24 * 60 * 60 * 1000))),
    }));

    return XResultUtils.ok(result);
  }

  // ==================== old项目核心辅助方法 ====================

  /**
   * 获取合约系统配置
   * 基于old项目: 获取sysConfig, treatyDay, seasonDay三个关键参数
   * 使用Result模式，正确处理配置获取结果
   */
  private async getContractSystemConfig(): Promise<XResult<{
    sysConfig: number;
    treatyDay: number;
    seasonDay: number;
  }>> {
    try {
      // 基于old项目：commonEnum.HERO_TREATY.SEASON_NUM (ID: 103)
      const sysConfigParam = await this.gameConfig.systemParam.get(103);
      const sysConfig = sysConfigParam?.parameter || 100;

      // 基于old项目：commonEnum.HERO_TREATY.TREATY_DAY (ID: 101)
      const treatyDayParam = await this.gameConfig.systemParam.get(101);
      const treatyDay = treatyDayParam?.parameter || 21;

      // 基于old项目：commonEnum.TABLE_SYSTEM_PARAM.LeagueSeason (ID: 需要确认)
      const seasonDayParam = await this.gameConfig.systemParam.get(104);
      const seasonDay = seasonDayParam?.parameter || 21;

      if (!sysConfig || !treatyDay || !seasonDay) {
        return XResultUtils.error('系统配置参数不完整', 'CONFIG_FAIL');
      }

      return XResultUtils.ok({
        sysConfig,
        treatyDay,
        seasonDay,
      });
    } catch (error) {
      this.logger.error('获取合约系统配置失败', error);
      return XResultUtils.error('获取合约系统配置失败', 'GET_CONTRACT_SYSTEM_CONFIG_ERROR');
    }
  }

  /**
   * 获取最大合约天数
   * 基于old项目: SystemParam配置表的TREATY_DAY参数
   * 使用Result模式，正确处理配置获取结果
   */
  private async getMaxTreatyDays(): Promise<XResult<number>> {
    try {
      // 从SystemParam配置表获取最大合约天数（基于old项目commonEnum.HERO_TREATY.TREATY_DAY）
      const config = await this.gameConfig.systemParam.get(101); // TREATY_DAY的ID: 101
      const maxDays = config ? config.parameter : 21;
      return XResultUtils.ok(maxDays);
    } catch (error) {
      this.logger.error('获取最大合约天数失败', error);
      return XResultUtils.error('获取最大合约天数失败', 'GET_MAX_TREATY_DAYS_ERROR');
    }
  }

  /**
   * 计算续约费用
   * 基于old项目逻辑: 球员身价÷系统参数÷21×续约天数
   * 使用Result模式，正确处理配置获取结果
   */
  private async calculateRenewalCost(heroResId: number, canTreatyDay: number): Promise<XResult<number>> {
    try {
      // 获取球员配置（基于old项目Footballer配置表）
      const heroConfigResult = await this.getHeroConfig(heroResId);
      if (XResultUtils.isFailure(heroConfigResult)) {
        return XResultUtils.error(`获取球员配置失败: ${heroConfigResult.message}`, heroConfigResult.code);
      }

      const heroConfig = heroConfigResult.data;
      if (!heroConfig) {
        return XResultUtils.error(`球员配置不存在: ${heroResId}`, 'HERO_CONFIG_NOT_FOUND');
      }

      // 获取系统参数（基于old项目SystemParam配置表）
      const sysConfigResult = await this.getSystemParam('SEASON_NUM'); // ID103
      if (XResultUtils.isFailure(sysConfigResult)) {
        return XResultUtils.error(`获取系统参数失败: ${sysConfigResult.message}`, sysConfigResult.code);
      }

      const seasonDayResult = await this.getSystemParam('LeagueSeason'); // 赛季天数
      if (XResultUtils.isFailure(seasonDayResult)) {
        return XResultUtils.error(`获取赛季天数失败: ${seasonDayResult.message}`, seasonDayResult.code);
      }

      const sysConfig = sysConfigResult.data;
      const seasonDay = seasonDayResult.data;

      if (!sysConfig || !seasonDay) {
        return XResultUtils.error('系统参数配置不存在', 'SYSTEM_PARAM_NOT_FOUND');
      }

      // 基于old项目公式: 球员身价÷系统参数÷21×续约天数
      const heroMoney = heroConfig.value || heroConfig.Value || 10000; // 球员身价
      const costMoney = Math.floor((heroMoney / sysConfig / seasonDay) * canTreatyDay);

      return XResultUtils.ok(Math.max(costMoney, 100)); // 最少100金币
    } catch (error) {
      this.logger.error('计算续约费用失败', error);
      return XResultUtils.error('计算续约费用失败', 'CALCULATE_RENEWAL_COST_ERROR');
    }
  }

  /**
   * 触发球员属性重新计算
   * 基于old项目: reCalcAttrRevision(uid) 和 checkHeroInArbitrarilyFormation(uid)
   * 当球员合约从0天恢复时需要重新计算属性和阵容
   */
  private async triggerHeroAttributeRecalculation(heroIds: string[]): Promise<void> {
    try {
      this.logger.log(`触发球员属性重新计算: ${heroIds.length}个球员`);

      // TODO: 实现属性重新计算逻辑
      // 1. 重新计算球员属性 (reCalcAttrRevision)
      // 2. 检查并更新阵容属性 (checkHeroInArbitrarilyFormation)

      // 暂时记录日志，待后续实现具体逻辑
      for (const heroId of heroIds) {
        this.logger.debug(`需要重新计算属性的球员: ${heroId}`);
      }

      // 可以考虑发送事件通知其他服务进行属性重算
      // this.eventEmitter.emit('hero.attributes.recalc', { heroIds });

    } catch (error) {
      this.logger.error('触发球员属性重新计算失败', error);
    }
  }

  /**
   * 计算合约天数
   * 基于old项目逻辑: 从SystemParam获取标准合约天数
   * 使用Result模式，正确处理配置获取结果
   */
  private async calculateContractDays(quality: number, lifeNum: number): Promise<XResult<number>> {
    try {
      // 获取系统配置的标准合约天数（基于old项目SystemParam）
      const treatyDayResult = await this.getSystemParam('TREATY_DAY');
      if (XResultUtils.isFailure(treatyDayResult)) {
        return XResultUtils.error(`获取标准合约天数失败: ${treatyDayResult.message}`, treatyDayResult.code);
      }

      const treatyDay = treatyDayResult.data;

      // 基于old项目逻辑：新球员获得满合约天数
      // 续约时根据当前合约天数计算可续约天数
      let contractDays = treatyDay;

      // 根据品质调整合约天数（高品质球员合约更长）
      const qualityBonus = Math.floor(quality * 2);
      contractDays += qualityBonus;

      // 根据生涯次数调整（生涯次数越多，基础合约越短）
      const lifeReduction = Math.floor(lifeNum * 1.5);
      contractDays -= lifeReduction;

      // 确保合约天数在合理范围内
      const finalDays = Math.max(Math.min(contractDays, treatyDay), 7); // 最少7天，最多不超过标准天数
      return XResultUtils.ok(finalDays);
    } catch (error) {
      this.logger.error('计算合约天数失败', error);
      return XResultUtils.error('计算合约天数失败', 'CALCULATE_CONTRACT_DAYS_ERROR');
    }
  }

  /**
   * 获取球员配置
   * 基于old项目: Footballer配置表
   * 使用Result模式，正确处理配置获取结果
   */
  private async getHeroConfig(resId: number): Promise<XResult<any>> {
    try {
      // 从Hero配置表获取球员配置（footballer已重命名为hero）
      const config = await this.gameConfig.hero.get(resId);
      if (!config) {
        this.logger.warn(`球员配置不存在: ${resId}`);
        return XResultUtils.error(`球员配置不存在: ${resId}`, 'HERO_CONFIG_NOT_FOUND');
      }

      const heroConfig = {
        resId: config.id,
        value: config.value || (resId * 1000), // 球员身价
        Value: config.value || (resId * 1000), // 兼容字段
        name: config.cnName || config.name || `球员${resId}`,
        quality: this.getQualityFromColor(config.color),
        releaseClause: config.releaseClause,
        rating: config.rating,
      };

      return XResultUtils.ok(heroConfig);
    } catch (error) {
      this.logger.error('获取球员配置失败', error);
      return XResultUtils.error('获取球员配置失败', 'GET_HERO_CONFIG_ERROR');
    }
  }

  /**
   * 获取系统参数
   * 基于old项目: SystemParam配置表
   * 使用Result模式，正确处理配置获取结果
   */
  private async getSystemParam(paramName: string): Promise<XResult<number>> {
    try {
      // 从SystemParam配置表获取参数（基于old项目commonEnum映射）
      const paramIdMap = {
        'SEASON_NUM': 103,    // 系统参数ID103
        'LeagueSeason': 3002, // 赛季天数
        'TREATY_DAY': 3001,   // 最大合约天数
      };

      const paramId = paramIdMap[paramName];
      if (!paramId) {
        this.logger.warn(`未知的系统参数: ${paramName}`);
        return XResultUtils.error(`未知的系统参数: ${paramName}`, 'UNKNOWN_SYSTEM_PARAM');
      }

      const config = await this.gameConfig.systemParam.get(paramId);
      const paramValue = config ? config.parameter : 100;
      return XResultUtils.ok(paramValue);
    } catch (error) {
      this.logger.error('获取系统参数失败', error);
      return XResultUtils.error('获取系统参数失败', 'GET_SYSTEM_PARAM_ERROR');
    }
  }

  /**
   * 获取状态道具配置
   * 基于old项目逻辑: Item配置表中的状态提升道具
   * 使用Result模式，正确处理配置获取结果
   */
  private async getStatusItemConfig(itemId: number): Promise<XResult<any>> {
    try {
      // 从Item配置表获取道具配置
      const itemDefinition = await this.gameConfig.item.get(itemId);

      if (!itemDefinition) {
        this.logger.error(`状态道具配置不存在: ${itemId}`);
        return XResultUtils.error(`状态道具配置不存在: ${itemId}`, 'ITEM_CONFIG_NOT_FOUND');
      }

      // 验证是否为状态道具（基于old项目道具类型分类）
      if (itemDefinition.type !== 2 || itemDefinition.subType !== 1) { // type=2消耗品, subType=1状态道具
        this.logger.warn(`道具不是状态道具: ${itemId}, type: ${itemDefinition.type}, subType: ${itemDefinition.subType}`);
        return XResultUtils.error(`道具不是状态道具: ${itemId}`, 'INVALID_ITEM_TYPE');
      }

      // 解析状态效果参数（基于old项目ItemParameters字段）
      const statusEffect = this.parseStatusEffect(itemDefinition.itemParameters);

      const itemConfig = {
        itemId,
        name: itemDefinition.name,
        type: itemDefinition.type,
        subType: itemDefinition.subType,
        statusEffect,
        duration: itemDefinition.time || 7, // 基于old项目Time字段
        stackable: itemDefinition.isSingle !== 0, // 基于old项目IsSingle字段：0=不可叠加，非0=可叠加
        stackLimit: itemDefinition.isSingleParameter || 1, // 基于old项目IsSingleParameter字段：堆叠上限
      };

      return XResultUtils.ok(itemConfig);
    } catch (error) {
      this.logger.error('获取状态道具配置失败', error);
      return XResultUtils.error('获取状态道具配置失败', 'GET_STATUS_ITEM_CONFIG_ERROR');
    }
  }

  /**
   * 解析状态效果参数
   * 基于old项目: ItemParameters字段解析
   */
  private parseStatusEffect(itemParameters: any): any {
    try {
      // 基于old项目：ItemParameters可能是数字或对象
      if (typeof itemParameters === 'number') {
        // 简单数值类型：表示属性提升值
        return {
          type: 'attribute_boost',
          value: itemParameters,
          attributes: ['speed', 'shooting', 'passing', 'defending', 'dribbling', 'physicality'], // 全属性提升
        };
      }

      if (typeof itemParameters === 'object' && itemParameters !== null) {
        // 对象类型：详细的状态效果配置
        return {
          type: itemParameters.type || 'attribute_boost',
          value: itemParameters.value || 0,
          attributes: itemParameters.attributes || [],
          duration: itemParameters.duration || 7,
          stackable: itemParameters.stackable || false,
        };
      }

      // 默认效果
      return {
        type: 'attribute_boost',
        value: 5,
        attributes: ['speed'],
      };
    } catch (error) {
      this.logger.error('解析状态效果参数失败', error);
      return {
        type: 'attribute_boost',
        value: 1,
        attributes: ['speed'],
      };
    }
  }

  /**
   * 计算状态提升
   * 基于old项目逻辑: 状态道具的健康状态改善机制
   */
  private calculateStatusPromotion(hero: any, itemConfig: any): any {
    const oldStatus = hero.Health || 1; // 1=健康, 2=轻微伤, 3=轻伤, 4=重伤
    const statusEffect = itemConfig.statusEffect;

    // 基于old项目：不同道具有不同的治疗效果
    let newStatus = oldStatus;
    let attributeBoosts = {};

    if (statusEffect && statusEffect.type === 'attribute_boost') {
      // 属性提升类道具
      if (oldStatus > 1) {
        // 受伤状态下，先治疗再提升属性
        newStatus = Math.max(1, oldStatus - Math.floor(statusEffect.value / 2));
      }

      // 计算属性提升（基于old项目ItemParameters）
      if (statusEffect.attributes && statusEffect.attributes.length > 0) {
        statusEffect.attributes.forEach(attr => {
          attributeBoosts[attr] = statusEffect.value;
        });
      }
    } else if (statusEffect && statusEffect.type === 'health_recovery') {
      // 纯治疗类道具
      const recoveryPower = statusEffect.value || 1;
      newStatus = Math.max(1, oldStatus - recoveryPower);
    } else {
      // 默认治疗效果
      if (oldStatus > 1) {
        newStatus = Math.max(1, oldStatus - 1);
      }
    }

    // 计算治疗费用节省（基于old项目逻辑）
    const savedCost = this.calculateSavedHealingCost(oldStatus, newStatus);

    return {
      oldStatus,
      newStatus,
      improvement: oldStatus - newStatus,
      attributeBoosts,
      savedCost,
      duration: statusEffect?.duration || 0,
      isFullyHealed: newStatus === 1,
    };
  }

  /**
   * 计算治疗费用节省
   * 基于old项目: 使用道具治疗比医疗中心治疗更便宜
   */
  private calculateSavedHealingCost(oldStatus: number, newStatus: number): number {
    // 基于old项目：不同伤病等级的治疗费用
    const healingCosts = {
      1: 0,      // 健康：无需治疗
      2: 5000,   // 轻微伤：5000金币
      3: 15000,  // 轻伤：15000金币
      4: 50000,  // 重伤：50000金币
    };

    const oldCost = healingCosts[oldStatus] || 0;
    const newCost = healingCosts[newStatus] || 0;

    return Math.max(0, oldCost - newCost);
  }

  /**
   * 计算最大生涯次数
   * 基于old项目逻辑: 不同品质球员的续约次数限制
   */
  private calculateMaxLifeNum(quality: number): number {
    try {
      // TODO: 从配置表获取最大生涯次数配置
      // const config = await this.gameConfig.systemParam?.get('MAX_LIFE_NUM_BY_QUALITY');
      // if (config) {
      //   const qualityConfig = JSON.parse(config.Param);
      //   return qualityConfig[quality] || 3;
      // }

      // 基于old项目：不同品质的球员有不同的最大生涯次数
      const maxLifeByQuality = {
        1: 3,  // 白色：3次生涯（2次续约）
        2: 4,  // 绿色：4次生涯（3次续约）
        3: 5,  // 蓝色：5次生涯（4次续约）
        4: 6,  // 紫色：6次生涯（5次续约）
        5: 8,  // 橙色：8次生涯（7次续约）
        6: 10, // 红色：10次生涯（9次续约）
      };

      const maxLife = maxLifeByQuality[quality];
      if (!maxLife) {
        this.logger.warn(`未知的球员品质: ${quality}, 使用默认值3`);
        return 3;
      }

      return maxLife;
    } catch (error) {
      this.logger.error('计算最大生涯次数失败', error);
      return 3; // 返回默认值
    }
  }

  /**
   * 检查并扣除金币
   * 基于old项目: checkResourceIsEnough + deductMoney
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async checkAndDeductCash(characterId: string, amount: number): Promise<XResult<any>> {
    this.logger.log(`检查并扣除金钱: ${characterId}, 金额: ${amount}`);

    // 调用Character服务扣除货币
    const deductResult = await this.characterService.deductCurrencyInternal(characterId, 'gold', amount, 'hero_cultivation');
    if (XResultUtils.isFailure(deductResult)) {
      return XResultUtils.error(`扣除金币失败: ${deductResult.message}`, deductResult.code);
    }

    this.logger.log(`金币扣除成功: ${characterId}, 金额: ${amount}`);
    return XResultUtils.ok({ cost: amount, remainingGold: deductResult.data.newBalance });
  }

  /**
   * 检查并扣除道具
   * 基于old项目: checkItemIsEnough + deductItem
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async checkAndDeductItem(characterId: string, itemId: number, quantity: number): Promise<XResult<any>> {
    this.logger.log(`检查并扣除道具: ${characterId}, 道具ID: ${itemId}, 数量: ${quantity}`);

    // 转换道具格式为批量扣除接口需要的格式
    const itemsToDeduct = [{
      configId: itemId, // 配置ID
      quantity: quantity || 1 // 数量，默认为1
    }]

    // 调用Inventory服务的批量扣除道具接口
    const deductResult = await this.inventoryService.batchDeductItemsInternal(characterId, itemsToDeduct, 'hero_cultivation');
    if (XResultUtils.isFailure(deductResult)) {
      return XResultUtils.error(`批量扣除道具失败: ${deductResult.message}`, deductResult.code);
    }

    this.logger.log(`批量扣除道具成功: ${characterId}, 道具数量: ${itemsToDeduct.length}`);
    return XResultUtils.ok({ cost: itemsToDeduct, deductResult: deductResult.data });
  }

  /**
   * 重新计算球员退役后的属性
   * 基于old项目: reCalcAttrRevision调用
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async recalculateHeroAttributesAfterRetirement(heroId: string): Promise<XResult<void>> {
    // 调用Hero服务的属性重新计算方法（基于old项目reCalcAttrRevision）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.HERO_SERVICE,
      'cultivation.reCalcAttrRevision',
      { heroId }
    );

    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`球员退役后属性重新计算成功: ${heroId}`);
      return XResultUtils.ok(undefined);
    } else {
      this.logger.warn(`球员属性重新计算失败: ${heroId}, ${result.message}`);
      return XResultUtils.error(`球员属性重新计算失败: ${result.message}`, result.code);
    }
  }

  /**
   * 通知Character服务更新阵容
   * 基于old项目: 退役球员需要从阵容中移除
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async notifyFormationUpdate(characterId: string, heroId: string): Promise<XResult<void>> {
    // 调用Character服务更新阵容（基于old项目逻辑）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'formation.removeHeroFromAllFormations',
      { characterId, heroId }
    );

    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`球员已从阵容中移除: ${heroId}`);
    } else {
      this.logger.warn(`移除球员失败: ${result.message}`);
      // 不返回错误，因为这是非关键操作
    }

    // 同时更新球员状态为退役
    const updateResult = await this.heroRepository.updateById(heroId, {
      status: 'retired',
      retiredTime: new Date(),
      isActive: false,
    });

    if (XResultUtils.isFailure(updateResult)) {
      this.logger.warn(`更新球员退役状态失败: ${heroId}, ${updateResult.message}`);
    }

    this.logger.log(`通知阵容更新: ${characterId}, 移除球员: ${heroId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 触发退役相关任务和成就
   * 基于old项目: 退役可能触发特定任务和成就
   * 使用BaseService的callMicroservice方法，提供标准的微服务调用和错误处理
   */
  private async triggerRetirementTasks(characterId: string, heroId: string): Promise<XResult<void>> {
    // 获取退役球员信息用于任务触发
    const retiredHeroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(retiredHeroResult)) {
      return XResultUtils.error(`获取退役球员信息失败: ${retiredHeroResult.message}`, retiredHeroResult.code);
    }

    const retiredHero = retiredHeroResult.data;
    if (!retiredHero) {
      return XResultUtils.error(`退役球员不存在: ${heroId}`, 'RETIRED_HERO_NOT_FOUND');
    }

    // 调用任务系统触发退役相关任务（基于old项目TARGET_TYPE）
    const taskResult = await this.callMicroservice(
      MICROSERVICE_NAMES.ACTIVITY_SERVICE,
      'task.triggerTask',
      {
        characterId,
        triggerType: 'HERO_RETIREMENT', // 基于old项目TARGET_TYPE
        arg1: retiredHero.quality, // 球员品质
        arg2: retiredHero.lifeNum, // 生涯次数
        heroId
      }
    );

    if (XResultUtils.isSuccess(taskResult)) {
      this.logger.log(`退役任务触发成功: ${heroId}`);
    } else {
      this.logger.warn(`退役任务触发失败: ${heroId}, ${taskResult.message}`);
    }

    // 获取退役球员总数
    const retiredCountResult = await this.getRetiredHeroCount(characterId);
    const totalRetiredHeroes = XResultUtils.isSuccess(retiredCountResult) ? retiredCountResult.data : 0;

    // 调用成就系统触发退役相关成就（基于old项目成就系统）
    const achievementResult = await this.callMicroservice(
      MICROSERVICE_NAMES.ACTIVITY_SERVICE,
      'achievement.triggerAchievement',
      {
        characterId,
        achievementType: 'HERO_RETIREMENT',
        heroQuality: retiredHero.quality,
        totalRetiredHeroes,
        heroId
      }
    );

    if (XResultUtils.isSuccess(achievementResult)) {
      this.logger.log(`退役成就触发成功: ${heroId}`);
    } else {
      this.logger.warn(`退役成就触发失败: ${heroId}, ${achievementResult.message}`);
    }

    this.logger.log(`触发退役任务和成就: ${characterId}, 球员: ${heroId}, 品质: ${retiredHero.quality}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 计算生涯总天数
   * 基于old项目: 从获得时间到当前时间的总天数
   */
  private calculateCareerTotalDays(hero: any): number {
    const obtainTime = hero.obtainTime || Date.now();
    const currentTime = Date.now();
    const daysDiff = Math.floor((currentTime - obtainTime) / (24 * 60 * 60 * 1000));
    return Math.max(daysDiff, 0);
  }

  /**
   * 计算合约相关统计
   * 基于old项目: 合约续约次数等
   */
  private calculateContractStats(hero: any): any {
    return {
      renewals: hero.lifeNum ? hero.lifeNum - 1 : 0, // 生涯次数-1就是续约次数
      currentContractDays: hero.contractDays || 0,
      contractExpired: (hero.contractDays || 0) <= 0,
    };
  }

  /**
   * 计算比赛表现统计
   * 基于old项目: 比赛场次、进球、助攻等
   */
  private calculatePerformanceStats(hero: any): any {
    const matches = hero.matchesPlayed || 0;
    const goals = hero.goals || 0;
    const assists = hero.assists || 0;

    return {
      matches,
      goals,
      assists,
      averageRating: hero.averageRating || 0,
    };
  }

  /**
   * 计算发展历程统计
   * 基于old项目: 升级、技能升级、突破等
   */
  private calculateDevelopmentStats(hero: any): any {
    return {
      levelUps: hero.levelUps || 0,
      skillUpgrades: hero.skillUpgrades || 0,
      breakthroughs: hero.breakthroughs || (hero.breakthrough ? hero.breakthrough.length : 0),
    };
  }

  /**
   * 计算生涯成就和里程碑
   * 基于old项目: 各种成就条件判断
   */
  private calculateCareerAchievements(hero: any): any {
    const achievements = [];

    // 等级成就
    if (hero.level >= 50) achievements.push({ type: 'level', name: '资深球员', description: '达到50级' });
    if (hero.level >= 80) achievements.push({ type: 'level', name: '传奇球员', description: '达到80级' });

    // 比赛成就
    if (hero.goals >= 100) achievements.push({ type: 'goals', name: '百球射手', description: '进球达到100个' });
    if (hero.assists >= 50) achievements.push({ type: 'assists', name: '助攻大师', description: '助攻达到50次' });

    // 生涯成就
    const careerDays = this.calculateCareerTotalDays(hero);
    if (careerDays >= 365) achievements.push({ type: 'career', name: '一年老将', description: '生涯超过一年' });
    if (careerDays >= 1095) achievements.push({ type: 'career', name: '三年功勋', description: '生涯超过三年' });

    // 突破成就
    if (hero.breakthrough && hero.breakthrough.length >= 5) {
      achievements.push({ type: 'breakthrough', name: '突破专家', description: '完成5次突破' });
    }

    return achievements;
  }

  /**
   * 获取健康状态历史
   * 基于old项目: Health字段的历史记录
   */
  private getHealthStatusHistory(hero: any): any {
    const healthStatus = hero.Health || 1;
    const statusNames = {
      1: '健康',
      2: '轻微伤',
      3: '轻伤',
      4: '重伤',
    };

    return {
      current: statusNames[healthStatus] || '未知',
      statusCode: healthStatus,
      lastUpdateTime: hero.lastStatusUpdateTime || null,
    };
  }

  /**
   * 获取退役球员数量
   * 基于old项目: 统计角色的退役球员总数
   * 使用Result模式，正确处理Repository调用结果
   */
  private async getRetiredHeroCount(characterId: string): Promise<XResult<number>> {
    const countResult = await this.heroRepository.count({
      characterId,
      status: 'retired',
    });

    if (XResultUtils.isFailure(countResult)) {
      this.logger.error('获取退役球员数量失败', countResult.message);
      return XResultUtils.error('获取退役球员数量失败', countResult.code);
    }

    return XResultUtils.ok(countResult.data || 0);
  }

  /**
   * 从颜色获取品质
   * 基于old项目: 白=1星，绿=2星，蓝=3星，紫=4星，橙=5星，红=6星
   */
  private getQualityFromColor(color: string): number {
    const colorToQuality = {
      '白': 1,
      '绿': 2,
      '蓝': 3,
      '紫': 4,
      '橙': 5,
      '红': 6,
      '黑': 6, // 黑卡也是最高品质
    };

    return colorToQuality[color] || 3; // 默认3星
  }
}
