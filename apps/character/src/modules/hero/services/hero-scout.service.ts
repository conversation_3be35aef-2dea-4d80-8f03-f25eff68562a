import { Injectable, Logger } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { GameConfigFacade } from '@libs/game-config';
import { HeroRepository } from '@hero/common/repositories/hero.repository';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';

import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 统一的球探系统业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 严格基于old项目的球探系统业务逻辑
 *
 * 🎯 核心功能：
 * - 球探包管理（获取信息、删除球员、签约球员）
 * - 球探探索系统（初级、高级、顶级球探）
 * - 球探搜索功能（基于ScoutScope配置）
 * - 球探体力管理（购买、恢复、消耗）
 * - RP值兑换系统（高品质球员获取）
 *
 * 🚀 性能优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的Result模式错误处理
 * - 智能缓存机制优化球探数据访问
 * - 微服务调用的错误处理和重试机制
 *
 * 🔗 微服务依赖：
 * - Character服务：角色信息、资源检查、球探数据管理
 * - Hero服务：球员创建、属性计算
 * - Activity服务：任务触发、成就系统
 *
 * old项目数据结构：
 * Scout: {
 *   scoutRp: number,           // 球探RP值
 *   scoutEnergy: number,       // 球探体力
 *   scoutGroup: [],           // 球探组（初级、高级、顶级）
 *   scoutPack: [],            // 球探包（抽到的球员）
 * }
 */
@Injectable()
export class ScoutService extends BaseService {
  constructor(
    private readonly heroRepository: HeroRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient?: MicroserviceClientService,
  ) {
    super('ScoutService', microserviceClient);
  }

  /**
   * 获取球探包信息
   * 严格基于old项目: getScoutPackInfo
   * Controller层已验证参数，无需重复验证
   */
  async getScoutPackInfo(characterId: string): Promise<XResult<any>> {
    this.logger.log(`获取球探包信息: ${characterId}`);

    // 获取角色的球探数据
    const scoutDataResult = await this.getCharacterScoutData(characterId);
    if (XResultUtils.isFailure(scoutDataResult)) {
      return XResultUtils.error(`获取球探数据失败: ${scoutDataResult.message}`, scoutDataResult.code);
    }

    const scoutData = scoutDataResult.data;

    // 格式化球探包中的球员
    const formattedHeroesResult = await this.formatScoutPackHeroes(scoutData.scoutPack || []);
    if (XResultUtils.isFailure(formattedHeroesResult)) {
      return XResultUtils.error(`格式化球探包球员失败: ${formattedHeroesResult.message}`, formattedHeroesResult.code);
    }

    // 基于old项目的Scout实体结构返回数据
    const scoutPackInfo = {
      characterId,
      scoutRp: scoutData.scoutRp || 0,           // 球探RP值
      scoutEnergy: scoutData.scoutEnergy || 100, // 球探体力
      scoutGroup: scoutData.scoutGroup || [],    // 球探组（初级、高级、顶级）
      scoutPack: formattedHeroesResult.data,     // 球探包中的球员
    };

    return XResultUtils.ok(scoutPackInfo);
  }

  /**
   * 球探探索（核心功能）
   * 严格基于old项目: getScoutReward(uid, type)
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   *
   * @param characterId 角色ID
   * @param type 球探类型 (1=初级, 2=高级, 3=顶级)
   */
  async getScoutReward(characterId: string, type: number = 1): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`球探探索: ${characterId}, 类型: ${type}`);

      // 获取角色的球探数据
      const scoutDataResult = await this.getCharacterScoutData(characterId);
      if (XResultUtils.isFailure(scoutDataResult)) {
        return XResultUtils.error(`获取球探数据失败: ${scoutDataResult.message}`, scoutDataResult.code);
      }

      const scoutData = scoutDataResult.data;

      // 检查球探体力
      const energyCost = this.getScoutEnergyCost(type);
      if (scoutData.scoutEnergy < energyCost) {
        return XResultUtils.failure('球探体力不足', 'INSUFFICIENT_SCOUT_ENERGY', {
          required: energyCost,
          current: scoutData.scoutEnergy,
          deficit: energyCost - scoutData.scoutEnergy
        });
      }

      // 检查球探组是否解锁
      if (!this.isScoutGroupUnlocked(scoutData.scoutGroup, type)) {
        return XResultUtils.failure('球探组未解锁', 'SCOUT_GROUP_LOCKED', {
          scoutType: type,
          unlockedGroups: scoutData.scoutGroup.filter(g => g.unlocked).map(g => g.type)
        });
      }

      // 检查球探包是否已满
      const maxPackSize = this.getMaxScoutPackSize();
      if (scoutData.scoutPack.length >= maxPackSize) {
        return XResultUtils.failure('球探包已满', 'SCOUT_PACK_FULL', {
          maxSize: maxPackSize,
          currentSize: scoutData.scoutPack.length
        });
      }

      // 扣除球探体力
      scoutData.scoutEnergy -= energyCost;

      // 执行球探探索逻辑
      const scoutExplorationResult = await this.executeScoutExploration(type);
      if (XResultUtils.isFailure(scoutExplorationResult)) {
        // 探索失败，但仍然消耗体力
        const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
        if (XResultUtils.isFailure(updateResult)) {
          this.logger.warn(`更新球探数据失败: ${updateResult.message}`);
        }

        return XResultUtils.ok({
          success: false,
          message: '球探探索失败',
          scoutEnergy: scoutData.scoutEnergy,
        });
      }

      const scoutResult = scoutExplorationResult.data;
      if (scoutResult.success) {
        // 将球员添加到球探包
        scoutData.scoutPack.push(scoutResult.hero);

        // 更新球探数据
        const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
        if (XResultUtils.isFailure(updateResult)) {
          return XResultUtils.error(`更新球探数据失败: ${updateResult.message}`, updateResult.code);
        }

        return XResultUtils.ok({
          success: true,
          message: '球探探索成功',
          hero: scoutResult.hero,
          scoutEnergy: scoutData.scoutEnergy,
          scoutPackCount: scoutData.scoutPack.length,
        });
      } else {
        // 探索失败，但仍然消耗体力
        const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
        if (XResultUtils.isFailure(updateResult)) {
          this.logger.warn(`更新球探数据失败: ${updateResult.message}`);
        }

        return XResultUtils.ok({
          success: false,
          message: '球探探索失败',
          scoutEnergy: scoutData.scoutEnergy,
        });
      }
    }, { reason: 'scout_exploration', metadata: { characterId, scoutType: type } });
  }

  /**
   * 删除球探包中的球员
   * 严格基于old项目: delScoutPackHero(index)
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   *
   * @param characterId 角色ID
   * @param indexArray 要删除的索引数组（支持批量删除）
   */
  async deleteScoutPackHero(characterId: string, indexArray: number[]): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`删除球探包球员: ${characterId}, 索引: ${JSON.stringify(indexArray)}`);

      // 获取角色的球探数据
      const scoutDataResult = await this.getCharacterScoutData(characterId);
      if (XResultUtils.isFailure(scoutDataResult)) {
        return XResultUtils.error(`获取球探数据失败: ${scoutDataResult.message}`, scoutDataResult.code);
      }

      const scoutData = scoutDataResult.data;
      if (!scoutData || !scoutData.scoutPack) {
        return XResultUtils.error('球探数据不存在', 'SCOUT_DATA_NOT_FOUND');
      }

      const errorList = [];
      const successList = [];

      // 按索引从大到小排序，避免删除时索引偏移
      const sortedIndexes = [...indexArray].sort((a, b) => b - a);

      for (const index of sortedIndexes) {
        // 检查索引范围
        if (index < 0 || index >= scoutData.scoutPack.length) {
          errorList.push(index);
          continue; // RANGE_FAIL
        }

        // 删除指定索引的球员
        scoutData.scoutPack.splice(index, 1);
        successList.push(index);
      }

      // 如果有错误，返回部分成功的结果
      if (errorList.length > 0) {
        // 仍然更新成功删除的部分
        if (successList.length > 0) {
          const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
          if (XResultUtils.isFailure(updateResult)) {
            this.logger.warn(`更新球探数据失败: ${updateResult.message}`);
          }
        }

        return XResultUtils.failure('部分索引超出范围', 'PARTIAL_INDEX_OUT_OF_RANGE', {
          errorIndexes: errorList,
          successIndexes: successList,
          totalRequested: indexArray.length,
          successCount: successList.length
        });
      }

      // 更新球探数据
      const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球探数据失败: ${updateResult.message}`, updateResult.code);
      }

      return XResultUtils.ok({
        message: '删除成功',
        deletedIndexes: successList,
        deletedCount: successList.length
      });
    }, { reason: 'delete_scout_pack_hero', metadata: { characterId, indexCount: indexArray.length } });
  }

  /**
   * 签约球探球员
   * 严格基于old项目: signScoutHero(resId)
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   *
   * @param characterId 角色ID
   * @param resId 球员配置ID（不是索引）
   */
  async signScoutHero(characterId: string, resId: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`签约球探球员: ${characterId}, 配置ID: ${resId}`);

      // 获取角色的球探数据
      const scoutDataResult = await this.getCharacterScoutData(characterId);
      if (XResultUtils.isFailure(scoutDataResult)) {
        return XResultUtils.error(`获取球探数据失败: ${scoutDataResult.message}`, scoutDataResult.code);
      }

      const scoutData = scoutDataResult.data;
      if (!scoutData || !scoutData.scoutPack) {
        return XResultUtils.error('球探数据不存在', 'SCOUT_DATA_NOT_FOUND');
      }

      // 在球探包中查找指定的球员
      let targetIndex = -1;
      let targetHero = null;

      for (let i = 0; i < scoutData.scoutPack.length; i++) {
        if (scoutData.scoutPack[i].configId === resId) {
          targetIndex = i;
          targetHero = scoutData.scoutPack[i];
          break;
        }
      }

      if (targetIndex === -1 || !targetHero) {
        return XResultUtils.failure('球探包中没有找到指定球员', 'HERO_NOT_FOUND_IN_SCOUT_PACK', {
          requestedResId: resId,
          availableHeroes: scoutData.scoutPack.map(h => h.configId)
        });
      }

      // 检查是否已有相同球员（转换为球星卡）
      const checkSameHeroResult = await this.checkHaveSameHero([resId]);
      if (XResultUtils.isFailure(checkSameHeroResult)) {
        return XResultUtils.error(`检查相同球员失败: ${checkSameHeroResult.message}`, checkSameHeroResult.code);
      }

      const checkResult = checkSameHeroResult.data;
      if (checkResult.code !== 0) {
        return XResultUtils.failure('检查相同球员失败', 'CHECK_SAME_HERO_FAILED', {
          originalError: checkResult
        });
      }

      const finalResId = checkResult.scoutList[0];

      // 获取球员配置
      const heroConfig = await this.gameConfig.hero.get(finalResId);
      const itemConfig = await this.gameConfig.item.get(finalResId);

      if (!heroConfig && !itemConfig) {
        return XResultUtils.error('配置不存在', 'CONFIG_NOT_FOUND');
      }

      let cost = 0;
      let newHeroUid = '';

      // 如果不是球员（即球员卡）
      if (!heroConfig) {
        // 查找对应的球员配置来获取费用
        const allHeroConfigs = await this.gameConfig.hero.getAll();
        const relatedHeroConfig = allHeroConfigs.find(config => config.itemId === itemConfig.id);

        if (relatedHeroConfig) {
          cost = relatedHeroConfig.releaseClause || 0;
        }

        // 检查并扣除金币
        const moneyResult = await this.checkAndDeductMoney(characterId, cost);
        if (XResultUtils.isFailure(moneyResult)) {
          return XResultUtils.error(`金币操作失败: ${moneyResult.message}`, moneyResult.code);
        }

        const moneyCheckResult = moneyResult.data;
        if (moneyCheckResult.code !== 0) {
          return XResultUtils.failure('金币不足', 'INSUFFICIENT_GOLD', {
            required: cost,
            message: moneyCheckResult.message
          });
        }

        // 添加道具到背包
        const addItemResult = await this.addItemToBag(characterId, finalResId, 1);
        if (XResultUtils.isFailure(addItemResult)) {
          return XResultUtils.error(`添加道具到背包失败: ${addItemResult.message}`, addItemResult.code);
        }
      } else {
        // 是球员，直接签约
        cost = heroConfig.releaseClause || 0;

        // 检查并扣除金币
        const moneyResult = await this.checkAndDeductMoney(characterId, cost);
        if (XResultUtils.isFailure(moneyResult)) {
          return XResultUtils.error(`金币操作失败: ${moneyResult.message}`, moneyResult.code);
        }

        const moneyCheckResult = moneyResult.data;
        if (moneyCheckResult.code !== 0) {
          return XResultUtils.failure('金币不足', 'INSUFFICIENT_GOLD', {
            required: cost,
            message: moneyCheckResult.message
          });
        }

        // 创建新的球员实例
        const newHeroResult = await this.createHeroFromConfig(characterId, finalResId, heroConfig);
        if (XResultUtils.isFailure(newHeroResult)) {
          return XResultUtils.error(`创建球员失败: ${newHeroResult.message}`, newHeroResult.code);
        }

        const newHero = newHeroResult.data;
        newHeroUid = newHero._id.toString();
      }

      // 从球探包中删除已签约的球员
      scoutData.scoutPack.splice(targetIndex, 1);

      // 更新球探数据
      const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球探数据失败: ${updateResult.message}`, updateResult.code);
      }

      // 触发新手任务（基于old项目triggerNewerTask逻辑）
      const triggerTaskResult = await this.triggerNewerTask(characterId, 'SIGN_HERO');
      if (XResultUtils.isFailure(triggerTaskResult)) {
        this.logger.warn(`触发新手任务失败: ${triggerTaskResult.message}`);
      }

      return XResultUtils.ok({
        message: '签约成功',
        resId: finalResId,
        heroId: newHeroUid,
        cost: cost,
      });
    }, { reason: 'sign_scout_hero', metadata: { characterId, resId } });
  }

  /**
   * 球探搜索
   * 基于球探范围配置进行搜索，根据ScoutScope配置表的LowerLimit和UpperLimit筛选球员
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async scoutSearch(characterId: string, scoutType: string, targetQuality?: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`球探搜索: ${characterId}, 类型: ${scoutType}`);

      // 获取球探范围配置
      const scoutScopes = await this.gameConfig.scoutScope.getAll();
      const targetScope = scoutScopes.find(scope => scope.type.toString() === scoutType.toString());

      if (!targetScope) {
        return XResultUtils.error('无效的球探类型', 'INVALID_SCOUT_TYPE');
      }

      // 获取角色的球探数据，检查搜索条件
      const scoutDataResult = await this.getCharacterScoutData(characterId);
      if (XResultUtils.isFailure(scoutDataResult)) {
        return XResultUtils.error(`获取球探数据失败: ${scoutDataResult.message}`, scoutDataResult.code);
      }

      const scoutData = scoutDataResult.data;

      // 检查搜索冷却时间（基于old项目的搜索限制）
      const now = Date.now();
      const lastSearchTime = scoutData.lastSearchTime || 0;
      const searchCooldown = 2 * 60 * 60 * 1000; // 2小时冷却

      if (now - lastSearchTime < searchCooldown) {
        const remainingTime = searchCooldown - (now - lastSearchTime);
        return XResultUtils.failure('搜索冷却中', 'SEARCH_COOLDOWN_ACTIVE', {
          remainingTimeMs: remainingTime,
          remainingTimeMinutes: Math.ceil(remainingTime / 60000),
          nextSearchTime: new Date(lastSearchTime + searchCooldown)
        });
      }

      // 检查搜索费用
      const searchCost = this.getScoutSearchCost(parseInt(scoutType));

      // 检查角色是否有足够的金币
      const goldCheckResult = await this.checkCharacterGold(characterId, searchCost.gold);
      if (XResultUtils.isFailure(goldCheckResult)) {
        return XResultUtils.error(`检查金币失败: ${goldCheckResult.message}`, goldCheckResult.code);
      }

      const goldCheck = goldCheckResult.data;
      if (!goldCheck.success) {
        return XResultUtils.failure('金币不足', 'INSUFFICIENT_GOLD', {
          required: searchCost.gold,
          current: goldCheck.currentGold,
          deficit: searchCost.gold - goldCheck.currentGold
        });
      }

      // 根据球探范围筛选球员
      const foundHeroesResult = await this.searchHeroesByScope(targetScope, targetQuality);
      if (XResultUtils.isFailure(foundHeroesResult)) {
        return XResultUtils.error(`搜索球员失败: ${foundHeroesResult.message}`, foundHeroesResult.code);
      }

      const foundHeroes = foundHeroesResult.data;

      // 扣除搜索费用
      const deductResult = await this.deductCharacterGold(characterId, searchCost.gold);
      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除金币失败: ${deductResult.message}`, deductResult.code);
      }

      const deductData = deductResult.data;
      if (!deductData.success) {
        return XResultUtils.error('扣除金币失败', 'DEDUCT_GOLD_FAILED');
      }

      // 更新搜索时间
      scoutData.lastSearchTime = now;
      const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
      if (XResultUtils.isFailure(updateResult)) {
        this.logger.warn(`更新球探数据失败: ${updateResult.message}`);
      }

      const searchResult = {
        characterId,
        scoutType: parseInt(scoutType),
        targetQuality,
        foundHeroes,
        searchCost,
        nextSearchTime: new Date(now + searchCooldown),
        scopeInfo: {
          lowerLimit: targetScope.lowerLimit,
          upperLimit: targetScope.upperlimit,
          bg: targetScope.bg,
          icon: targetScope.icon,
        },
      };

      this.logger.log(`球探搜索完成: ${characterId}, 找到${foundHeroes.length}个球员`);
      return XResultUtils.ok(searchResult);
    }, { reason: 'scout_search', metadata: { characterId, scoutType, targetQuality } });
  }

  // ==================== old项目核心辅助方法 ====================

  /**
   * 获取角色的球探数据
   * 基于old项目: Scout实体
   */
  private async getCharacterScoutData(characterId: string): Promise<XResult<any>> {
    // 调用Character服务获取Scout实体数据
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.getScoutData',
      { characterId }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.error(`获取球探数据失败: ${result.message}`);
      return XResultUtils.error(`获取球探数据失败: ${result.message}`, result.code);
    }

    return XResultUtils.ok(result.data);
  }

  /**
   * 更新角色的球探数据
   */
  private async updateCharacterScoutData(characterId: string, scoutData: any): Promise<XResult<void>> {
    this.logger.log(`更新球探数据: ${characterId}`);

    // 调用Character服务更新Scout实体数据
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.updateScoutData',
      { characterId, scoutData }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.error(`更新球探数据失败: ${result.message}`);
      return XResultUtils.error(`更新球探数据失败: ${result.message}`, result.code);
    }

    this.logger.debug('球探数据更新成功');
    return XResultUtils.ok(undefined);
  }

  /**
   * 格式化球探包中的球员信息
   */
  private async formatScoutPackHeroes(scoutPack: any[]): Promise<XResult<any[]>> {
    try {
      const formattedHeroes = [];

      for (let i = 0; i < scoutPack.length; i++) {
        const hero = scoutPack[i];
        const heroConfig = await this.gameConfig.hero.get(hero.configId);

        formattedHeroes.push({
          index: i,
          configId: hero.configId,
          name: heroConfig?.name || '未知球员',
          quality: hero.quality,
          position: heroConfig?.position1 || 'Unknown',
          level: hero.level || 1,
          attributes: hero.attributes || {},
          signCost: hero.signCost || { gold: 1000, items: [] },
        });
      }

      return XResultUtils.ok(formattedHeroes);
    } catch (error) {
      this.logger.error('格式化球探包球员信息失败', error);
      return XResultUtils.error('格式化球探包球员信息失败', 'FORMAT_SCOUT_PACK_HEROES_ERROR');
    }
  }

  /**
   * 获取球探体力消耗
   */
  private getScoutEnergyCost(type: number): number {
    const costs = { 1: 10, 2: 20, 3: 30 }; // 初级、高级、顶级
    return costs[type] || 10;
  }

  /**
   * 检查球探组是否解锁
   */
  private isScoutGroupUnlocked(scoutGroup: any[], type: number): boolean {
    const group = scoutGroup.find(g => g.type === type);
    return group ? group.unlocked : false;
  }

  /**
   * 获取球探包最大容量
   */
  private getMaxScoutPackSize(): number {
    return 10; // 球探包最多容纳10个球员
  }

  /**
   * 执行球探探索逻辑
   * 基于old项目的球探算法
   */
  private async executeScoutExploration(type: number): Promise<XResult<any>> {
    try {
      // 获取球员池配置（权重表）
      const heroPool = await this.gameConfig.heroPool.getAll();

      // 根据球探类型筛选对应的池子
      const targetPool = this.getPoolByScoutType(type);
      const poolHeroes = heroPool.filter(pool => pool.pool === targetPool);

      if (poolHeroes.length === 0) {
        return XResultUtils.ok({ success: false });
      }

      // 根据权重随机选择球员
      const selectedPoolHero = this.selectHeroByWeight(poolHeroes);

      // 获取球员配置
      const heroConfig = await this.gameConfig.hero.get(selectedPoolHero.resId);
      if (!heroConfig) {
        return XResultUtils.ok({ success: false });
      }

      // 计算成功率
      const successRate = this.getScoutSuccessRate(type);
      const isSuccess = Math.random() < successRate;

      if (isSuccess) {
        const explorationResult = {
          success: true,
          hero: {
            configId: heroConfig.id,
            quality: this.getHeroQuality(heroConfig),
            level: 1,
            attributes: this.generateRandomAttributes(heroConfig),
            signCost: this.calculateSignCost(heroConfig),
          },
        };
        return XResultUtils.ok(explorationResult);
      } else {
        return XResultUtils.ok({ success: false });
      }
    } catch (error) {
      this.logger.error('执行球探探索逻辑失败', error);
      return XResultUtils.error('执行球探探索逻辑失败', 'EXECUTE_SCOUT_EXPLORATION_ERROR');
    }
  }

  /**
   * 根据球探类型获取对应的池子
   */
  private getPoolByScoutType(type: number): number {
    switch (type) {
      case 1: return 1; // 初级球探对应池子1
      case 2: return 2; // 高级球探对应池子2
      case 3: return 3; // 顶级球探对应池子3
      default: return 1;
    }
  }

  /**
   * 根据权重随机选择球员
   */
  private selectHeroByWeight(poolHeroes: any[]): any {
    const totalWeight = poolHeroes.reduce((sum, hero) => sum + hero.weight, 0);
    let random = Math.random() * totalWeight;

    for (const hero of poolHeroes) {
      random -= hero.weight;
      if (random <= 0) {
        return hero;
      }
    }

    // 兜底返回第一个
    return poolHeroes[0];
  }

  /**
   * 获取球员品质
   * 基于old项目: HeroDefinition的star字段表示品质
   */
  private getHeroQuality(heroConfig: any): number {
    // 基于old项目Footballer配置表，star字段表示球员品质（1-6星）
    // 1星=白色，2星=绿色，3星=蓝色，4星=紫色，5星=橙色，6星=红色
    return heroConfig.star || heroConfig.quality || 1;
  }

  /**
   * 获取球探成功率
   */
  private getScoutSuccessRate(type: number): number {
    const rates = { 1: 0.8, 2: 0.6, 3: 0.4 }; // 初级80%，高级60%，顶级40%
    return rates[type] || 0.5;
  }

  /**
   * 生成随机属性
   * 基于old项目: 球员属性随机生成逻辑
   *
   * 实现逻辑：
   * 1. 根据球员配置获取基础属性
   * 2. 应用品质倍率
   * 3. 应用随机浮动范围
   * 4. 根据位置调整属性权重
   * 5. 确保属性在合理范围内
   */
  private generateRandomAttributes(heroConfig: any): any {
    // 1. 获取球员基础属性（基于old项目Footballer配置表）
    const baseAttributes = {
      speed: heroConfig.speed || 60,
      shooting: heroConfig.finishing || 60,
      passing: heroConfig.passing || 60,
      defending: heroConfig.standingTackle || 60,
      dribbling: heroConfig.dribbling || 60,
      physicality: heroConfig.strength || 60,
      goalkeeping: heroConfig.save || 60,
    };

    // 2. 应用品质倍率（基于old项目品质系统）
    const quality = heroConfig.quality || 1;
    const qualityMultiplier = this.getQualityMultiplier(quality);

    // 3. 应用随机浮动（基于old项目：±10%的随机浮动）
    const randomAttributes = {};
    for (const [key, baseValue] of Object.entries(baseAttributes)) {
      // 先应用品质倍率
      const qualityValue = Math.floor(baseValue * qualityMultiplier);

      // 再应用随机浮动
      const fluctuationRange = Math.floor(qualityValue * 0.1); // 10%浮动
      const fluctuation = Math.floor(Math.random() * (fluctuationRange * 2 + 1)) - fluctuationRange;
      const randomValue = qualityValue + fluctuation;

      // 4. 确保属性在合理范围内（1-100）
      randomAttributes[key] = Math.max(1, Math.min(100, randomValue));
    }

    // 5. 根据位置调整属性权重（基于old项目位置特性）
    const position = heroConfig.position1 || 'Unknown';
    this.applyPositionBonus(randomAttributes, position);

    return randomAttributes;
  }



  /**
   * 计算签约费用
   */
  private calculateSignCost(heroConfig: any): any {
    const baseCost = (heroConfig.quality || 1) * 1000;
    return {
      gold: baseCost,
      items: [],
    };
  }

  /**
   * 检查是否已有相同球员
   * 基于old项目: checkHaveSameHero方法
   *
   * 实现逻辑：
   * 1. 获取玩家已有球员列表
   * 2. 遍历待检查的球员ResID数组
   * 3. 如果发现相同球员，转换为对应的球星卡ItemID
   * 4. 返回处理后的列表
   */
  private async checkHaveSameHero(resIdArray: number[]): Promise<XResult<any>> {
    try {
      // TODO: 获取玩家已有球员列表
      // const heroList = await this.getPlayerHeroList(characterId);

      // 暂时使用模拟的球员列表进行演示
      const heroListResult = await this.getPlayerHeroList();
      if (XResultUtils.isFailure(heroListResult)) {
        this.logger.warn(`获取球员列表失败: ${heroListResult.message}`);
        // 如果获取失败，假设没有重复球员
        const defaultCheckResult = {
          code: 0,
          scoutList: resIdArray,
        };
        return XResultUtils.ok(defaultCheckResult);
      }

      const heroList = heroListResult.data || [];
      const processedList = [];
      let hasError = false;

      // 遍历待检查的球员ResID数组（基于old项目逻辑）
      for (let i = 0; i < resIdArray.length; i++) {
        let currentResId = resIdArray[i];
        let isFound = false;

        // 检查是否已有相同球员（基于old项目：比较ResID）
        for (let j = 0; j < heroList.length; j++) {
          if (currentResId !== heroList[j].resId) {
            continue;
          }

          // 找到相同球员，需要转换为球星卡
          const heroConfigResult = await this.getHeroConfig(currentResId);
          if (XResultUtils.isFailure(heroConfigResult)) {
            this.logger.error(`获取球员配置失败: ${currentResId}, ${heroConfigResult.message}`);
            hasError = true;
            break;
          }

          const heroConfig = heroConfigResult.data;
          if (!heroConfig) {
            this.logger.error(`球员配置不存在: ${currentResId}`);
            hasError = true;
            break;
          }

          // 转换为对应的球星卡ItemID（基于old项目逻辑）
          currentResId = heroConfig.itemId;
          isFound = true;
          break;
        }

        if (hasError) {
          break;
        }

        processedList.push(currentResId);
      }

      if (hasError) {
        return XResultUtils.error('球员配置错误', 'HERO_CONFIG_ERROR');
      }

      const successCheckResult = {
        code: 0,
        scoutList: processedList,
      };
      return XResultUtils.ok(successCheckResult);
    } catch (error) {
      this.logger.error('检查相同球员失败', error);
      return XResultUtils.error('检查相同球员失败', 'CHECK_SAME_HERO_ERROR');
    }
  }

  /**
   * 检查角色金币是否足够
   * 基于old项目: checkResourceIsEnough
   */
  private async checkCharacterGold(characterId: string, amount: number): Promise<XResult<any>> {
    this.logger.log(`检查角色金币: ${characterId}, 需要金额: ${amount}`);

    // 调用Character服务获取角色信息（包含金币）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.getInfo',
      {
        characterId,
        serverId: this.getServerId(), // 从环境变量或配置获取
      }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.error(`获取角色信息失败: ${result.message}`);
      const defaultGoldCheck = {
        success: false,
        currentGold: 0,
        message: '获取金币信息失败',
      };
      return XResultUtils.ok(defaultGoldCheck);
    }

    const currentGold = result.data?.gold || 0; // Character服务中金币字段是gold
    const hasEnough = currentGold >= amount;

    const goldCheckResult = {
      success: hasEnough,
      currentGold,
      required: amount,
      message: hasEnough ? '金币充足' : '金币不足',
    };

    return XResultUtils.ok(goldCheckResult);
  }

  /**
   * 扣除角色金币
   * 基于old项目: deductMoney
   */
  private async deductCharacterGold(characterId: string, amount: number, serverId?: string): Promise<XResult<any>> {
    this.logger.log(`扣除角色金币: ${characterId}, 金额: ${amount}`);

    // 调用Character服务扣除金币
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.currency.subtract',
      {
        characterId,
        currencyDto: {
          currencyType: 'gold', // 基于old项目：球探搜索使用gold
          amount,
          reason: 'scout_search',
        },
        serverId: serverId || 'server_001', // 从参数获取，默认server_001
      }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.error(`扣除金币失败: ${result.message}`);
      const defaultDeductResult = {
        success: false,
        message: '扣除金币失败',
      };
      return XResultUtils.ok(defaultDeductResult);
    }

    const deductResult = {
      success: true,
      deductedAmount: amount,
      remainingGold: result.data?.newBalance || 0,
      message: '扣除金币成功',
    };

    return XResultUtils.ok(deductResult);
  }

  /**
   * 检查角色钻石是否足够
   * 基于old项目: checkResourceIsEnough
   */
  private async checkCharacterDiamond(characterId: string, amount: number): Promise<XResult<any>> {
    this.logger.log(`检查角色钻石: ${characterId}, 需要数量: ${amount}`);

    // 调用Character服务获取角色信息（包含钻石）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.getInfo',
      {
        characterId,
        serverId: this.getServerId(), // 从环境变量或配置获取
      }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.error(`获取角色信息失败: ${result.message}`);
      const defaultDiamondCheck = {
        success: false,
        currentDiamond: 0,
        message: '获取钻石信息失败',
      };
      return XResultUtils.ok(defaultDiamondCheck);
    }

    const currentDiamond = result.data?.diamond || 0; // Character服务中钻石字段是diamond
    const hasEnough = currentDiamond >= amount;

    const diamondCheckResult = {
      success: hasEnough,
      currentDiamond,
      required: amount,
      message: hasEnough ? '钻石充足' : '钻石不足',
    };

    return XResultUtils.ok(diamondCheckResult);
  }

  /**
   * 扣除角色钻石
   * 基于old项目: deductDiamond
   */
  private async deductCharacterDiamond(characterId: string, amount: number, serverId?: string): Promise<XResult<any>> {
    this.logger.log(`扣除角色钻石: ${characterId}, 数量: ${amount}`);

    // 调用Character服务扣除钻石
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.currency.subtract',
      {
        characterId,
        currencyDto: {
          currencyType: 'diamond',
          amount,
          reason: 'scout_energy_buy',
        },
        serverId: serverId || 'server_001', // 从参数获取，默认server_001
      }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.error(`扣除钻石失败: ${result.message}`);
      const defaultDeductResult = {
        success: false,
        message: '扣除钻石失败',
      };
      return XResultUtils.ok(defaultDeductResult);
    }

    const deductResult = {
      success: true,
      deductedAmount: amount,
      remainingDiamond: result.data?.newBalance || 0,
      message: '扣除钻石成功',
    };

    return XResultUtils.ok(deductResult);
  }

  /**
   * 检查并扣除金币（兼容旧方法）
   * 基于old项目: checkResourceIsEnough + deductMoney
   */
  private async checkAndDeductMoney(characterId: string, amount: number): Promise<XResult<any>> {
    this.logger.log(`检查并扣除金币: ${characterId}, 金额: ${amount}`);

    // 先检查金币是否足够
    const checkResult = await this.checkCharacterGold(characterId, amount);
    if (XResultUtils.isFailure(checkResult)) {
      return XResultUtils.error(`检查金币失败: ${checkResult.message}`, checkResult.code);
    }

    const goldCheck = checkResult.data;
    if (!goldCheck.success) {
      return XResultUtils.failure('金币不足', 'INSUFFICIENT_GOLD', {
        required: amount,
        current: goldCheck.currentGold,
        deficit: amount - goldCheck.currentGold
      });
    }

    // 扣除金币
    const deductResult = await this.deductCharacterGold(characterId, amount);
    if (XResultUtils.isFailure(deductResult)) {
      return XResultUtils.error(`扣除金币失败: ${deductResult.message}`, deductResult.code);
    }

    const deductData = deductResult.data;
    if (!deductData.success) {
      return XResultUtils.error('扣除金币操作失败', 'DEDUCT_GOLD_OPERATION_FAILED');
    }

    return XResultUtils.ok({ cost: amount });
  }

  /**
   * 添加道具到背包
   * 基于old项目: bag.addItem
   */
  private async addItemToBag(characterId: string, itemId: number, quantity: number): Promise<XResult<void>> {
    // 调用Character服务添加道具到背包（基于old项目bag.addItem）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'item.addItem',
      {
        characterId,
        resId: itemId,
        num: quantity
      }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.error(`添加道具到背包失败: ${characterId}, 道具ID: ${itemId}, 错误: ${result.message}`);
      return XResultUtils.error(`添加道具到背包失败: ${result.message}`, result.code);
    }

    this.logger.log(`添加道具到背包成功: ${characterId}, 道具ID: ${itemId}, 数量: ${quantity}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 从配置创建球员
   * 基于old项目: heros.addHero
   */
  private async createHeroFromConfig(characterId: string, resId: number, heroConfig: any): Promise<XResult<any>> {
    // 创建新的球员实例
    const createHeroResult = await this.heroRepository.create({
      characterId,
      serverId: 'server_001', // TODO: 从参数获取
      resId: resId,
      name: heroConfig.name || '未知球员',
      position: heroConfig.position1 || 'Unknown',
      quality: heroConfig.star || 1,
      level: 1,
      // 其他属性根据CreateHeroDto的结构初始化
      nationality: heroConfig.nationality || 'Unknown',
      club: heroConfig.club || 'Unknown',
    });

    if (XResultUtils.isFailure(createHeroResult)) {
      return XResultUtils.error(`创建球员失败: ${createHeroResult.message}`, createHeroResult.code);
    }

    return XResultUtils.ok(createHeroResult.data);
  }

  /**
   * 球探RP值兑换球员
   * 基于old项目Scout.exchangeScout方法
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async exchangeScout(characterId: string, type: number = 3): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`球探RP值兑换: ${characterId}, 类型: ${type}`);

      // 获取角色的球探数据
      const scoutDataResult = await this.getCharacterScoutData(characterId);
      if (XResultUtils.isFailure(scoutDataResult)) {
        return XResultUtils.error(`获取球探数据失败: ${scoutDataResult.message}`, scoutDataResult.code);
      }

      const scoutData = scoutDataResult.data;

      // 检查RP值是否足够（基于old项目的RP值要求）
      const requiredRpResult = await this.getRequiredRpForExchange();
      if (XResultUtils.isFailure(requiredRpResult)) {
        return XResultUtils.error(`获取RP值配置失败: ${requiredRpResult.message}`, requiredRpResult.code);
      }

      const requiredRp = requiredRpResult.data;
      if (scoutData.scoutRp < requiredRp) {
        return XResultUtils.failure('RP值不足', 'INSUFFICIENT_RP', {
          required: requiredRp,
          current: scoutData.scoutRp,
          deficit: requiredRp - scoutData.scoutRp
        });
      }

      // 检查体力是否足够（基于old项目的体力消耗）
      const requiredEnergyResult = await this.getRequiredEnergyForExchange();
      if (XResultUtils.isFailure(requiredEnergyResult)) {
        return XResultUtils.error(`获取体力配置失败: ${requiredEnergyResult.message}`, requiredEnergyResult.code);
      }

      const requiredEnergy = requiredEnergyResult.data;
      if (scoutData.scoutEnergy < requiredEnergy) {
        return XResultUtils.failure('球探体力不足', 'INSUFFICIENT_SCOUT_ENERGY', {
          required: requiredEnergy,
          current: scoutData.scoutEnergy,
          deficit: requiredEnergy - scoutData.scoutEnergy
        });
      }

      // 检查球探包是否已满
      const maxPackSize = this.getMaxScoutPackSize();
      if (scoutData.scoutPack.length >= maxPackSize) {
        return XResultUtils.failure('球探包已满', 'SCOUT_PACK_FULL', {
          maxSize: maxPackSize,
          currentSize: scoutData.scoutPack.length
        });
      }

      // 扣除RP值和体力
      scoutData.scoutRp = 0; // 基于old项目，兑换后RP值清零
      scoutData.scoutEnergy -= requiredEnergy;
      if (scoutData.scoutEnergy < 0) {
        scoutData.scoutEnergy = 0;
      }

      // 执行兑换逻辑（基于old项目，写死获得黑卡）
      const exchangeLogicResult = await this.executeExchangeLogic(type);
      if (XResultUtils.isFailure(exchangeLogicResult)) {
        // 兑换失败，但仍然消耗资源
        const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
        if (XResultUtils.isFailure(updateResult)) {
          this.logger.warn(`更新球探数据失败: ${updateResult.message}`);
        }

        return XResultUtils.ok({
          success: false,
          message: 'RP值兑换失败',
          scoutRp: scoutData.scoutRp,
          scoutEnergy: scoutData.scoutEnergy,
        });
      }

      const exchangeResult = exchangeLogicResult.data;
      if (exchangeResult.success) {
        // 将球员添加到球探包
        scoutData.scoutPack.push(exchangeResult.hero);

        // 更新球探数据
        const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
        if (XResultUtils.isFailure(updateResult)) {
          return XResultUtils.error(`更新球探数据失败: ${updateResult.message}`, updateResult.code);
        }

        return XResultUtils.ok({
          success: true,
          message: 'RP值兑换成功',
          hero: exchangeResult.hero,
          scoutRp: scoutData.scoutRp,
          scoutEnergy: scoutData.scoutEnergy,
          scoutPackCount: scoutData.scoutPack.length,
        });
      } else {
        // 兑换失败，但仍然消耗资源
        const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
        if (XResultUtils.isFailure(updateResult)) {
          this.logger.warn(`更新球探数据失败: ${updateResult.message}`);
        }

        return XResultUtils.ok({
          success: false,
          message: 'RP值兑换失败',
          scoutRp: scoutData.scoutRp,
          scoutEnergy: scoutData.scoutEnergy,
        });
      }
    }, { reason: 'exchange_scout', metadata: { characterId, type } });
  }

  // ==================== 球探搜索相关方法 ====================

  /**
   * 根据球探范围搜索球员
   * 基于ScoutScope配置表的LowerLimit和UpperLimit筛选球员
   */
  private async searchHeroesByScope(scope: any, targetQuality?: string): Promise<XResult<any[]>> {
    try {
      // 获取所有球员配置
      const allHeroes = await this.gameConfig.hero.getAll();

      // 根据球探范围筛选球员（基于球员的综合评分）
      const filteredHeroes = allHeroes.filter(hero => {
        // 计算球员综合评分（基于old项目的评分算法）
        const overallRating = this.calculateHeroOverallRating(hero);

        // 检查是否在球探范围内
        const inRange = overallRating >= scope.lowerLimit && overallRating <= scope.upperlimit;

        // 如果指定了目标品质，还要检查品质
        if (targetQuality && hero.color) {
          return inRange && hero.color.toString() === targetQuality.toString();
        }

        return inRange;
      });

      // 随机选择3-5个球员作为搜索结果
      const resultCount = Math.floor(Math.random() * 3) + 3; // 3-5个
      const shuffled = filteredHeroes.sort(() => 0.5 - Math.random());
      const selectedHeroes = shuffled.slice(0, Math.min(resultCount, filteredHeroes.length));

      // 格式化搜索结果
      const searchResults = selectedHeroes.map(hero => ({
        configId: hero.id,
        name: hero.cnName || hero.name,
        position: hero.position1, // 使用正确的字段名
        color: hero.color,
        overallRating: this.calculateHeroOverallRating(hero),
        signCost: this.calculateSignCost(hero),
        attributes: {
          speed: hero.speed || 0,
          shoot: hero.finishing || 0, // 使用finishing作为射门
          pass: hero.passing || 0, // 使用正确的字段名
          dribble: hero.dribbling || 0, // 使用正确的字段名
          defend: hero.standingTackle || 0, // 使用standingTackle作为防守
          physical: hero.stamina || 0, // 使用stamina作为体能
        },
      }));

      return XResultUtils.ok(searchResults);
    } catch (error) {
      this.logger.error('搜索球员失败', error);
      return XResultUtils.error('搜索球员失败', 'SEARCH_HEROES_BY_SCOPE_ERROR');
    }
  }

  /**
   * 计算球员综合评分
   * 基于old项目的评分算法
   */
  private calculateHeroOverallRating(hero: any): number {
    // 基于球员的六维属性计算综合评分
    const speed = hero.speed || 0;
    const shoot = hero.shoot || 0;
    const pass = hero.pass || 0;
    const dribble = hero.dribble || 0;
    const defend = hero.defend || 0;
    const physical = hero.physical || 0;

    // 简单的平均值算法，实际项目中可能有更复杂的权重计算
    const overall = Math.round((speed + shoot + pass + dribble + defend + physical) / 6);
    return overall;
  }

  /**
   * 获取球探搜索费用
   * 基于球探类型确定搜索费用
   */
  private getScoutSearchCost(scoutType: number): { gold: number; energy: number } {
    switch (scoutType) {
      case 1: // 初级球探
        return { gold: 5000, energy: 5 };
      case 2: // 高级球探
        return { gold: 10000, energy: 10 };
      case 3: // 顶级球探
        return { gold: 20000, energy: 15 };
      default:
        return { gold: 5000, energy: 5 };
    }
  }

  /**
   * 购买球探体力
   * 基于old项目的体力购买机制
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async buyScoutEnergy(characterId: string, amount: number = 50): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`购买球探体力: ${characterId}, 数量: ${amount}`);

      // 获取角色的球探数据
      const scoutDataResult = await this.getCharacterScoutData(characterId);
      if (XResultUtils.isFailure(scoutDataResult)) {
        return XResultUtils.error(`获取球探数据失败: ${scoutDataResult.message}`, scoutDataResult.code);
      }

      const scoutData = scoutDataResult.data;

      // 检查体力是否已满
      const maxEnergy = 100; // 最大体力值
      if (scoutData.scoutEnergy >= maxEnergy) {
        return XResultUtils.failure('体力已满，无需购买', 'ENERGY_ALREADY_FULL', {
          currentEnergy: scoutData.scoutEnergy,
          maxEnergy: maxEnergy
        });
      }

      // 基于old项目：从FootballerEnergy配置表获取体力和费用
      const heroEnergyDefinition = await this.gameConfig.heroEnergy.get(1);
      if (!heroEnergyDefinition) {
        return XResultUtils.error('体力配置不存在', 'ENERGY_CONFIG_NOT_FOUND');
      }

      const energyAmount = heroEnergyDefinition.energy;
      const cost = heroEnergyDefinition.gold;

      // 基于old项目：球探体力购买使用金币(gold)，不是钻石
      const goldCheckResult = await this.checkCharacterGold(characterId, cost);
      if (XResultUtils.isFailure(goldCheckResult)) {
        return XResultUtils.error(`检查金币失败: ${goldCheckResult.message}`, goldCheckResult.code);
      }

      const goldCheck = goldCheckResult.data;
      if (!goldCheck.success) {
        return XResultUtils.failure('金币不足', 'INSUFFICIENT_GOLD', {
          required: cost,
          current: goldCheck.currentGold,
          deficit: cost - goldCheck.currentGold
        });
      }

      // 扣除金币
      const deductResult = await this.deductCharacterGold(characterId, cost);
      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除金币失败: ${deductResult.message}`, deductResult.code);
      }

      const deductData = deductResult.data;
      if (!deductData.success) {
        return XResultUtils.error('扣除金币失败', 'DEDUCT_GOLD_FAILED');
      }

      // 增加体力（基于old项目的逻辑）
      const newEnergy = Math.min(maxEnergy, scoutData.scoutEnergy + energyAmount);
      const actualAdded = newEnergy - scoutData.scoutEnergy;
      scoutData.scoutEnergy = newEnergy;

      // 更新球探数据
      const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球探数据失败: ${updateResult.message}`, updateResult.code);
      }

      return XResultUtils.ok({
        message: '购买体力成功',
        addedEnergy: actualAdded,
        currentEnergy: scoutData.scoutEnergy,
        maxEnergy,
        cost,
      });
    }, { reason: 'buy_scout_energy', metadata: { characterId, amount } });
  }

  /**
   * 手动恢复球探体力
   * 基于old项目的体力恢复机制
   * Controller层已验证参数，无需重复验证
   */
  async recoverScoutEnergy(characterId: string): Promise<XResult<any>> {
    this.logger.log(`手动恢复球探体力: ${characterId}`);

    // 获取角色的球探数据
    const scoutDataResult = await this.getCharacterScoutData(characterId);
    if (XResultUtils.isFailure(scoutDataResult)) {
      return XResultUtils.error(`获取球探数据失败: ${scoutDataResult.message}`, scoutDataResult.code);
    }

    const scoutData = scoutDataResult.data;

    // 计算可恢复的体力
    const now = new Date();
    const lastRegenTime = new Date(scoutData.lastEnergyRegenTime);
    const timeDiff = now.getTime() - lastRegenTime.getTime();

    // 每5分钟恢复1点体力
    const regenInterval = 5 * 60 * 1000; // 5分钟
    const regenPoints = Math.floor(timeDiff / regenInterval);

    if (regenPoints > 0 && scoutData.scoutEnergy < 100) {
      const oldEnergy = scoutData.scoutEnergy;
      const newEnergy = Math.min(100, scoutData.scoutEnergy + regenPoints);
      const actualRecovered = newEnergy - oldEnergy;

      scoutData.scoutEnergy = newEnergy;
      scoutData.lastEnergyRegenTime = now;

      // 更新球探数据
      const updateResult = await this.updateCharacterScoutData(characterId, scoutData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球探数据失败: ${updateResult.message}`, updateResult.code);
      }

      return XResultUtils.ok({
        success: true,
        message: '体力恢复成功',
        recoveredEnergy: actualRecovered,
        currentEnergy: scoutData.scoutEnergy,
        maxEnergy: 100,
        nextRegenTime: new Date(now.getTime() + regenInterval),
      });
    } else {
      return XResultUtils.ok({
        success: false,
        message: '暂无可恢复的体力',
        currentEnergy: scoutData.scoutEnergy,
        maxEnergy: 100,
        nextRegenTime: new Date(lastRegenTime.getTime() + regenInterval),
      });
    }
  }

  // ==================== RP值兑换相关方法 ====================

  /**
   * 执行RP值兑换逻辑
   * 基于old项目，写死获得黑卡
   */
  private async executeExchangeLogic(type: number): Promise<XResult<any>> {
    // 基于old项目的兑换逻辑，使用权重随机选择球员
    const exchangeExplorationResult = await this.executeScoutExploration(type);
    if (XResultUtils.isFailure(exchangeExplorationResult)) {
      return XResultUtils.error(`执行球探探索失败: ${exchangeExplorationResult.message}`, exchangeExplorationResult.code);
    }

    const exchangeResult = exchangeExplorationResult.data;

    // 基于old项目，RP值兑换通常有更高的成功率或保底机制
    if (exchangeResult.success) {
      // 确保获得高品质球员（黑卡）
      if (exchangeResult.hero && exchangeResult.hero.quality) {
        exchangeResult.hero.quality = Math.max(exchangeResult.hero.quality, 5); // 至少5星
      }
    }

    return XResultUtils.ok(exchangeResult);
  }

  /**
   * 获取RP值兑换所需的RP值
   * 基于old项目SystemParam配置
   */
  private async getRequiredRpForExchange(): Promise<XResult<number>> {
    try {
      // 从SystemParam配置表获取scoutMaxRp参数（ID=3009）
      const scoutMaxRpConfig = await this.gameConfig.systemParam.get(3009);
      const requiredRp = scoutMaxRpConfig ? scoutMaxRpConfig.parameter : 100;
      return XResultUtils.ok(requiredRp);
    } catch (error) {
      this.logger.error('获取RP值配置失败', error);
      return XResultUtils.ok(100); // 默认值
    }
  }

  /**
   * 获取RP值兑换所需的体力
   * 基于old项目SystemParam配置
   */
  private async getRequiredEnergyForExchange(): Promise<XResult<number>> {
    try {
      // 从SystemParam配置表获取needEnergy参数（ID=3010）
      const needEnergyConfig = await this.gameConfig.systemParam.get(3010);
      const requiredEnergy = needEnergyConfig ? needEnergyConfig.parameter : 20;
      return XResultUtils.ok(requiredEnergy);
    } catch (error) {
      this.logger.error('获取体力配置失败', error);
      return XResultUtils.ok(20); // 默认值
    }
  }

  // ==================== 体力购买相关方法 ====================

  /**
   * 计算体力购买费用
   * 基于old项目的钻石消耗算法
   */
  private calculateEnergyBuyCost(amount: number): number {
    // 基于old项目，通常是1钻石=1体力，或者有阶梯价格
    const baseCost = 1; // 1钻石1体力
    return amount * baseCost;
  }

  /**
   * 获取体力购买配置
   * 基于old项目: FootballerEnergy配置表
   */
  private getEnergyConfig(energyId: number): { energy: number; gold: number } {
    // TODO: 实际应该从GameConfigFacade获取FootballerEnergy配置
    // 基于old项目的FootballerEnergy配置表结构
    const energyConfigs = {
      1: { energy: 50, gold: 100 },   // 档位1：50体力，100金币
      2: { energy: 100, gold: 180 },  // 档位2：100体力，180金币
      3: { energy: 200, gold: 320 },  // 档位3：200体力，320金币
    };

    return energyConfigs[energyId] || energyConfigs[1];
  }

  /**
   * 获取玩家球员列表
   * 基于old项目: player.heros.makeClientHeroList()
   */
  private async getPlayerHeroList(): Promise<XResult<any[]>> {
    // TODO: 调用Hero服务获取玩家球员列表
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.HERO_SERVICE,
    //   'hero.getPlayerHeroList',
    //   { characterId }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   this.logger.error(`获取玩家球员列表失败: ${result.message}`);
    //   return XResultUtils.error(`获取玩家球员列表失败: ${result.message}`, result.code);
    // }
    //
    // return XResultUtils.ok(result.data.heroList || []);

    // 暂时返回模拟数据
    const mockHeroList = [
      { resId: 1001, heroId: 'hero_1', level: 10 },
      { resId: 1002, heroId: 'hero_2', level: 15 },
      { resId: 1003, heroId: 'hero_3', level: 8 },
    ];
    return XResultUtils.ok(mockHeroList);
  }

  /**
   * 获取球员配置
   * 基于old项目: dataApi.allData.data["Footballer"][resId]
   */
  private async getHeroConfig(resId: number): Promise<XResult<any>> {
    try {
      // 从Hero配置表获取球员配置（footballer已重命名为hero）
      const config = await this.gameConfig.hero.get(resId);
      if (!config) {
        this.logger.warn(`球员配置不存在: ${resId}`);
        return XResultUtils.ok(null);
      }

      // 转换为old项目兼容的格式
      const heroConfig = {
        id: config.id,
        itemId: config.itemId || (resId + 2000), // 球星卡ID
        name: config.cnName || config.name || `球员${resId}`,
        quality: this.getQualityFromColor(config.color), // 从color字段获取品质
        position: config.position1 || 'ST',
        releaseClause: config.releaseClause || (resId * 1000),
        nationality: 'Unknown', // HeroDefinition中没有nationality字段
        club: 'Unknown', // HeroDefinition中没有club字段
        star: this.getQualityFromColor(config.color), // 从color字段获取星级
        color: config.color,
        rating: config.rating,
      };

      return XResultUtils.ok(heroConfig);
    } catch (error) {
      this.logger.error('获取球员配置失败', error);
      return XResultUtils.error('获取球员配置失败', 'GET_HERO_CONFIG_ERROR');
    }
  }

  /**
   * 获取品质倍率
   * 基于old项目: 品质系统的属性倍率
   */
  private getQualityMultiplier(quality: number): number {
    // 基于old项目：不同品质的属性倍率
    const qualityMultipliers = {
      1: 1.0,    // 白色
      2: 1.2,    // 绿色
      3: 1.4,    // 蓝色
      4: 1.6,    // 紫色
      5: 1.8,    // 橙色
      6: 2.0,    // 红色
    };

    return qualityMultipliers[quality] || 1.0;
  }

  /**
   * 应用位置加成
   * 基于old项目: Attr.json配置表的位置权重
   */
  private applyPositionBonus(attributes: any, position: string): void {
    // 基于old项目：不同位置对不同属性有加成
    const positionBonuses = {
      'GK': { // 门将
        goalkeeping: 1.5,
        physicality: 1.2,
        speed: 0.8,
        shooting: 0.5,
      },
      'DC': { // 中后卫
        defending: 1.4,
        physicality: 1.3,
        passing: 1.1,
        speed: 0.9,
      },
      'DL': { // 左后卫
        defending: 1.2,
        speed: 1.3,
        passing: 1.1,
        dribbling: 1.1,
      },
      'DR': { // 右后卫
        defending: 1.2,
        speed: 1.3,
        passing: 1.1,
        dribbling: 1.1,
      },
      'MC': { // 中场
        passing: 1.4,
        dribbling: 1.2,
        defending: 1.1,
        shooting: 1.1,
      },
      'ST': { // 前锋
        shooting: 1.5,
        dribbling: 1.3,
        speed: 1.2,
        defending: 0.7,
      },
      'WL': { // 左边锋
        speed: 1.4,
        dribbling: 1.3,
        shooting: 1.2,
        defending: 0.8,
      },
      'WR': { // 右边锋
        speed: 1.4,
        dribbling: 1.3,
        shooting: 1.2,
        defending: 0.8,
      },
    };

    const bonus = positionBonuses[position];
    if (bonus) {
      for (const [attr, multiplier] of Object.entries(bonus)) {
        if (attributes[attr] && typeof multiplier === 'number') {
          attributes[attr] = Math.floor(attributes[attr] * multiplier);
          // 确保不超过100
          attributes[attr] = Math.min(100, attributes[attr]);
        }
      }
    }
  }

  /**
   * 触发新手任务
   * 基于old项目: triggerNewerTask
   */
  private async triggerNewerTask(characterId: string, taskType: string): Promise<XResult<void>> {
    // TODO: 调用Activity服务触发新手任务
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.ACTIVITY_SERVICE,
    //   'task.triggerNewerTask',
    //   { characterId, taskType }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   this.logger.error(`触发新手任务失败: ${result.message}`);
    //   return XResultUtils.error(`触发新手任务失败: ${result.message}`, result.code);
    // }

    this.logger.debug(`触发新手任务: ${characterId}, 任务类型: ${taskType}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 获取服务器ID
   * 从环境变量或配置获取
   */
  private getServerId(): string {
    return process.env.SERVER_ID || 'server_001';
  }

  /**
   * 从颜色获取品质
   * 基于old项目: 白=1星，绿=2星，蓝=3星，紫=4星，橙=5星，红=6星
   */
  private getQualityFromColor(color: string): number {
    const colorToQuality = {
      '白': 1,
      '绿': 2,
      '蓝': 3,
      '紫': 4,
      '橙': 5,
      '红': 6,
      '黑': 6, // 黑卡也是最高品质
    };

    return colorToQuality[color] || 3; // 默认3星
  }
}
