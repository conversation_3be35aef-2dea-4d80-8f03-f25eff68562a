import { Injectable, Logger } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { SkillType, SkillPosition } from '@character/common/types';
import { SkillDocument, SkillActiveStatus } from '@character/common/schemas/skill.schema';
import { SkillRepository } from '@character/common/repositories/skill.repository';
import { HeroRepository } from '@character/common/repositories/hero.repository';
import {
  LearnSkillDto,
  UpgradeSkillDto,
  ActivateSkillDto,
  SkillInfoDto,
  GetSkillListDto,
  GetSkillConfigListDto
} from '@character/common/dto/skill.dto';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { GameConfigFacade } from '@libs/game-config';
import { CharacterService } from '../../character/character.service';

import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 统一的技能业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 提供完整的技能管理功能，包括配置查询、学习、升级、激活等核心功能
 *
 * 🎯 核心功能：
 * - 技能配置管理（静态配置查询、位置筛选、等级分类）
 * - 球员技能管理（学习、升级、激活、取消激活）
 * - 技能资源检查（金币、信仰活跃度、道具验证）
 * - 技能统计分析（使用统计、升级进度、效果计算）
 *
 * 🚀 性能优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的Result模式错误处理
 * - 智能缓存机制优化技能数据访问
 * - 微服务调用的错误处理和重试机制
 *
 * 🔗 服务依赖：
 * - CharacterService：角色信息、资源检查、道具验证（直接注入）
 * - Hero服务：球员信息、属性计算
 */
@Injectable()
export class HeroSkillService extends BaseService {
  constructor(
    private readonly skillRepository: SkillRepository,
    private readonly heroRepository: HeroRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly characterService: CharacterService,
  ) {
    super('HeroSkillService');
  }

  // ==================== 技能配置管理 ====================

  /**
   * 获取技能配置信息
   * 修复：使用GameConfigFacade获取静态配置，而不是通过Repository
   * Controller层已验证参数，无需重复验证
   */
  async getSkillConfig(skillId: number): Promise<XResult<any>> {
    try {
      const skillDefinition = await this.gameConfig.heroSkill.get(skillId);
      if (!skillDefinition) {
        return XResultUtils.error('技能配置不存在', 'SKILL_CONFIG_NOT_FOUND');
      }

      const skillConfigDto = this.toSkillConfigDto(skillDefinition);
      return XResultUtils.ok(skillConfigDto);
    } catch (error) {
      this.logger.error('获取技能配置信息失败', error);
      return XResultUtils.error('获取技能配置信息失败', 'GET_SKILL_CONFIG_ERROR');
    }
  }

  /**
   * 获取技能配置列表
   * 修复：使用GameConfigFacade的高级查询方法，充分利用filter功能
   * Controller层已验证参数，无需重复验证
   */
  async getSkillConfigList(query: GetSkillConfigListDto): Promise<XResult<any>> {
    try {
      let filteredSkills;

      // 使用GameConfigFacade的高级查询方法
      if (query.skillRank) {
        // 根据技能等级筛选（S, A, B, C等）
        filteredSkills = await this.gameConfig.heroSkill.findBy('skillRank', query.skillRank);
      } else if (query.skillType) {
        // 根据技能类型筛选（使用typeA字段）
        filteredSkills = await this.gameConfig.heroSkill.findBy('typeA', query.skillType);
      } else if (query.position) {
        // 根据位置筛选，使用filter方法进行复杂条件筛选
        filteredSkills = await this.gameConfig.heroSkill.filter(skill => {
          // 根据typeA, typeB, typeC, typeD字段判断适用位置
          // 这些字段通常对应不同的位置类型
          return skill.typeA === query.position ||
                 skill.typeB === query.position ||
                 skill.typeC === query.position ||
                 skill.typeD === query.position;
        });
      } else {
        // 获取所有技能配置
        filteredSkills = await this.gameConfig.heroSkill.getAll();
      }

      // 如果有多个筛选条件，使用filter进行组合筛选
      if (query.minStarValue || query.maxStarValue) {
        filteredSkills = await this.gameConfig.heroSkill.filter(skill => {
          const starMatch = (!query.minStarValue || skill.starValue >= query.minStarValue) &&
                           (!query.maxStarValue || skill.starValue <= query.maxStarValue);

          // 组合其他已有的筛选条件
          const rankMatch = !query.skillRank || skill.skillRank === query.skillRank;
          const typeMatch = !query.skillType || skill.typeA === query.skillType;

          return starMatch && rankMatch && typeMatch;
        });
      }

      // 分页处理
      const page = query.page || 1;
      const limit = query.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedSkills = filteredSkills.slice(startIndex, endIndex);

      const skillConfigListData = {
        list: paginatedSkills.map(skillDef => this.toSkillConfigDto(skillDef)),
        total: filteredSkills.length,
        page: page,
        limit: limit,
        pages: Math.ceil(filteredSkills.length / limit),
        hasNext: endIndex < filteredSkills.length,
        hasPrev: page > 1,
      };

      return XResultUtils.ok(skillConfigListData);
    } catch (error) {
      this.logger.error('获取技能配置列表失败', error);
      return XResultUtils.error('获取技能配置列表失败', 'GET_SKILL_CONFIG_LIST_ERROR');
    }
  }

  /**
   * 根据位置获取可用技能配置
   * 修复：使用GameConfigFacade的filter方法进行高效筛选
   * Controller层已验证参数，无需重复验证
   */
  async getSkillConfigsByPosition(position: SkillPosition, limit: number = 50): Promise<XResult<any[]>> {
    try {
      // 使用GameConfigFacade的filter方法进行位置筛选
      const positionSkills = await this.gameConfig.heroSkill.filter(skill => {
        // 根据typeA, typeB, typeC, typeD字段判断技能是否适用于指定位置
        // 这些字段通常对应不同的位置类型或触发条件
        return skill.typeA === position ||
               skill.typeB === position ||
               skill.typeC === position ||
               skill.typeD === position;
      });

      // 应用数量限制并按星级排序（高星级优先）
      const limitedSkills = positionSkills
        .sort((a, b) => b.starValue - a.starValue) // 按星级降序排序
        .slice(0, limit);

      const skillConfigDtos = limitedSkills.map(skillDef => this.toSkillConfigDto(skillDef));
      return XResultUtils.ok(skillConfigDtos);
    } catch (error) {
      this.logger.error('根据位置获取技能配置失败', error);
      return XResultUtils.error('根据位置获取技能配置失败', 'GET_SKILL_CONFIGS_BY_POSITION_ERROR');
    }
  }

  /**
   * 根据技能等级获取技能配置列表
   * 充分利用GameConfigFacade的findBy方法
   * Controller层已验证参数，无需重复验证
   */
  async getSkillConfigsByRank(skillRank: string): Promise<XResult<any[]>> {
    try {
      // 使用findBy方法直接根据skillRank字段筛选
      const rankSkills = await this.gameConfig.heroSkill.findBy('skillRank', skillRank);

      // 按星级降序排序
      const sortedSkills = rankSkills.sort((a, b) => b.starValue - a.starValue);

      const skillConfigDtos = sortedSkills.map(skillDef => this.toSkillConfigDto(skillDef));
      return XResultUtils.ok(skillConfigDtos);
    } catch (error) {
      this.logger.error('根据技能等级获取技能配置失败', error);
      return XResultUtils.error('根据技能等级获取技能配置失败', 'GET_SKILL_CONFIGS_BY_RANK_ERROR');
    }
  }

  /**
   * 获取高星级技能配置
   * 使用GameConfigFacade的filter方法进行复杂筛选
   * Controller层已验证参数，无需重复验证
   */
  async getHighStarSkillConfigs(minStarValue: number = 7): Promise<XResult<any[]>> {
    try {
      // 使用filter方法筛选高星级技能
      const highStarSkills = await this.gameConfig.heroSkill.filter(skill =>
        skill.starValue >= minStarValue
      );

      // 按星级降序排序
      const sortedSkills = highStarSkills.sort((a, b) => b.starValue - a.starValue);

      const skillConfigDtos = sortedSkills.map(skillDef => this.toSkillConfigDto(skillDef));
      return XResultUtils.ok(skillConfigDtos);
    } catch (error) {
      this.logger.error('获取高星级技能配置失败', error);
      return XResultUtils.error('获取高星级技能配置失败', 'GET_HIGH_STAR_SKILL_CONFIGS_ERROR');
    }
  }

  /**
   * 根据技能名称查找技能配置
   * 使用GameConfigFacade的findOneBy方法
   * Controller层已验证参数，无需重复验证
   */
  async getSkillConfigByName(skillName: string): Promise<XResult<any | null>> {
    try {
      // 使用findOneBy方法根据技能名称查找
      const skill = await this.gameConfig.heroSkill.findOneBy('skillName', skillName);

      const result = skill ? this.toSkillConfigDto(skill) : null;
      return XResultUtils.ok(result);
    } catch (error) {
      this.logger.error('根据技能名称查找技能配置失败', error);
      return XResultUtils.error('根据技能名称查找技能配置失败', 'GET_SKILL_CONFIG_BY_NAME_ERROR');
    }
  }

  // ==================== 球员技能管理 ====================

  /**
   * 球员学习技能
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async learnSkill(learnDto: LearnSkillDto): Promise<XResult<SkillDocument>> {
    return this.executeBusinessOperation(async () => {
      // 检查技能配置是否存在
      // 修复：使用GameConfigFacade获取静态配置
      const skillDefinition = await this.gameConfig.heroSkill.get(learnDto.configId);
      if (!skillDefinition) {
        return XResultUtils.error('技能配置不存在', 'SKILL_CONFIG_NOT_FOUND');
      }

      // 检查球员是否已拥有该技能
      const hasSkillResult = await this.skillRepository.hasSkill(learnDto.heroId, learnDto.configId);
      if (XResultUtils.isFailure(hasSkillResult)) {
        return XResultUtils.error(`检查技能拥有状态失败: ${hasSkillResult.message}`, hasSkillResult.code);
      }

      if (hasSkillResult.data) {
        return XResultUtils.failure('球员已拥有该技能', 'SKILL_ALREADY_LEARNED', {
          heroId: learnDto.heroId,
          configId: learnDto.configId
        });
      }

      // 获取球员信息以获取characterId和serverId
      const heroResult = await this.heroRepository.findById(learnDto.heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 生成球员技能实例ID
      const skillId = this.generateSkillId();

      // 创建球员技能实例
      const skillData = {
        skillId,
        heroId: learnDto.heroId,
        characterId: hero.characterId, // 从Hero数据获取characterId
        serverId: hero.serverId || 'server_001', // 从Hero数据获取serverId，默认server_001
        configId: learnDto.configId,
        level: 1,
        experience: 0,
        activeStatus: SkillActiveStatus.INACTIVE,
        slotPosition: 0,
        obtainSource: learnDto.obtainSource || 'manual',
        obtainCost: learnDto.obtainCost || 0,
      };

      const createSkillResult = await this.skillRepository.createSkill(skillData);
      if (XResultUtils.isFailure(createSkillResult)) {
        return XResultUtils.error(`创建技能失败: ${createSkillResult.message}`, createSkillResult.code);
      }

      this.logger.log(`球员学习技能成功: ${learnDto.heroId}, 技能: ${learnDto.configId}`);
      return XResultUtils.ok(createSkillResult.data);
    }, { reason: 'learn_skill', metadata: { heroId: learnDto.heroId, configId: learnDto.configId } });
  }

  /**
   * 升级球员技能
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async upgradeSkill(upgradeDto: UpgradeSkillDto): Promise<XResult<SkillDocument>> {
    return this.executeBusinessOperation(async () => {
      const skillResult = await this.skillRepository.findSkillById(upgradeDto.skillId);
      if (XResultUtils.isFailure(skillResult)) {
        return XResultUtils.error(`获取技能信息失败: ${skillResult.message}`, skillResult.code);
      }

      const skill = skillResult.data;
      if (!skill) {
        return XResultUtils.error('技能不存在', 'SKILL_NOT_FOUND');
      }

      // 检查是否可以升级
      if (!skill.canUpgrade) {
        return XResultUtils.failure('技能无法升级', 'SKILL_CANNOT_UPGRADE', {
          skillId: upgradeDto.skillId,
          currentLevel: skill.level,
          reason: '技能已达到升级限制'
        });
      }

      // 检查目标等级是否有效
      if (upgradeDto.targetLevel <= skill.level) {
        return XResultUtils.failure('无效的升级等级', 'INVALID_UPGRADE_LEVEL', {
          currentLevel: skill.level,
          targetLevel: upgradeDto.targetLevel
        });
      }

      // 检查是否超过最大等级（假设最大等级为10）
      if (upgradeDto.targetLevel > 10) {
        return XResultUtils.failure('超过最大等级', 'EXCEED_MAX_LEVEL', {
          maxLevel: 10,
          targetLevel: upgradeDto.targetLevel
        });
      }

      // 计算升级费用
      const upgradeCost = this.calculateUpgradeCost(skill.level, upgradeDto.targetLevel);

      // 检查玩家是否有足够的资源进行升级（基于old项目逻辑）
      const resourceCheckResult = await this.checkUpgradeResources(skill, upgradeDto.targetLevel, upgradeCost);
      if (XResultUtils.isFailure(resourceCheckResult)) {
        return XResultUtils.error(`资源检查失败: ${resourceCheckResult.message}`, resourceCheckResult.code);
      }

      if (!resourceCheckResult.data.sufficient) {
        return XResultUtils.failure('升级资源不足', 'INSUFFICIENT_UPGRADE_RESOURCES', {
          required: resourceCheckResult.data.required,
          current: resourceCheckResult.data.current,
          message: resourceCheckResult.data.message
        });
      }

      // 执行升级
      const updateData = {
        level: upgradeDto.targetLevel,
        upgradeCount: skill.upgradeCount + (upgradeDto.targetLevel - skill.level),
        totalUpgradeCost: skill.totalUpgradeCost + upgradeCost,
        lastUpgradeTime: Date.now(),
      };

      const updateResult = await this.skillRepository.updateSkill(upgradeDto.skillId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新技能失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`技能升级成功: ${upgradeDto.skillId}, 等级: ${upgradeDto.targetLevel}`);
      return XResultUtils.ok(updateResult.data);
    }, { reason: 'upgrade_skill', metadata: { skillId: upgradeDto.skillId, targetLevel: upgradeDto.targetLevel } });
  }

  /**
   * 激活球员技能
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async activateSkill(activateDto: ActivateSkillDto): Promise<XResult<SkillDocument>> {
    return this.executeBusinessOperation(async () => {
      const skillResult = await this.skillRepository.findSkillById(activateDto.skillId);
      if (XResultUtils.isFailure(skillResult)) {
        return XResultUtils.error(`获取技能信息失败: ${skillResult.message}`, skillResult.code);
      }

      const skill = skillResult.data;
      if (!skill) {
        return XResultUtils.error('技能不存在', 'SKILL_NOT_FOUND');
      }

      // 检查技能是否可以激活
      if (skill.isLocked) {
        return XResultUtils.failure('技能已锁定', 'SKILL_IS_LOCKED', {
          skillId: activateDto.skillId,
          reason: '技能处于锁定状态，无法激活'
        });
      }

      // 清空目标槽位的其他技能
      const clearSlotResult = await this.skillRepository.clearSkillSlot(skill.heroId, activateDto.slotPosition);
      if (XResultUtils.isFailure(clearSlotResult)) {
        this.logger.warn(`清空技能槽位失败: ${clearSlotResult.message}`);
      }

      // 激活技能
      const updateData = {
        activeStatus: SkillActiveStatus.ACTIVE,
        slotPosition: activateDto.slotPosition,
      };

      const updateResult = await this.skillRepository.updateSkill(activateDto.skillId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`激活技能失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`技能激活成功: ${activateDto.skillId}, 槽位: ${activateDto.slotPosition}`);
      return XResultUtils.ok(updateResult.data);
    }, { reason: 'activate_skill', metadata: { skillId: activateDto.skillId, slotPosition: activateDto.slotPosition } });
  }

  /**
   * 取消激活球员技能
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async deactivateSkill(skillId: string): Promise<XResult<SkillDocument>> {
    return this.executeBusinessOperation(async () => {
      const skillResult = await this.skillRepository.findSkillById(skillId);
      if (XResultUtils.isFailure(skillResult)) {
        return XResultUtils.error(`获取技能信息失败: ${skillResult.message}`, skillResult.code);
      }

      const skill = skillResult.data;
      if (!skill) {
        return XResultUtils.error('技能不存在', 'SKILL_NOT_FOUND');
      }

      const updateData = {
        activeStatus: SkillActiveStatus.INACTIVE,
        slotPosition: 0,
      };

      const updateResult = await this.skillRepository.updateSkill(skillId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`取消激活技能失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`技能取消激活成功: ${skillId}`);
      return XResultUtils.ok(updateResult.data);
    }, { reason: 'deactivate_skill', metadata: { skillId } });
  }

  /**
   * 获取球员技能列表
   * Controller层已验证参数，无需重复验证
   */
  async getSkillList(query: GetSkillListDto): Promise<XResult<SkillInfoDto[]>> {
    const skillsResult = await this.skillRepository.findSkillsByHeroId(query);
    if (XResultUtils.isFailure(skillsResult)) {
      return XResultUtils.error(`获取球员技能列表失败: ${skillsResult.message}`, skillsResult.code);
    }

    const skills = skillsResult.data || [];
    const skillInfoDtos = skills.map(skill => this.toSkillInfoDto(skill));
    return XResultUtils.ok(skillInfoDtos);
  }

  /**
   * 获取球员已激活的技能
   * Controller层已验证参数，无需重复验证
   */
  async getActiveSkills(heroId: string): Promise<XResult<SkillInfoDto[]>> {
    const activeSkillsResult = await this.skillRepository.findActiveSkills(heroId);
    if (XResultUtils.isFailure(activeSkillsResult)) {
      return XResultUtils.error(`获取球员已激活技能失败: ${activeSkillsResult.message}`, activeSkillsResult.code);
    }

    const activeSkills = activeSkillsResult.data || [];
    const skillInfoDtos = activeSkills.map(skill => this.toSkillInfoDto(skill));
    return XResultUtils.ok(skillInfoDtos);
  }

  /**
   * 获取技能统计
   * Controller层已验证参数，无需重复验证
   */
  async getSkillStats(heroId: string): Promise<XResult<any>> {
    const statsResult = await this.skillRepository.getSkillStats(heroId);
    if (XResultUtils.isFailure(statsResult)) {
      return XResultUtils.error(`获取技能统计失败: ${statsResult.message}`, statsResult.code);
    }

    return XResultUtils.ok(statsResult.data);
  }

  /**
   * 计算升级费用
   */
  private calculateUpgradeCost(fromLevel: number, toLevel: number): number {
    let totalCost = 0;
    
    for (let level = fromLevel + 1; level <= toLevel; level++) {
      // 基础升级费用公式
      totalCost += level * 1000;
    }
    
    return totalCost;
  }

  /**
   * 生成球员技能实例ID
   */
  private generateSkillId(): string {
    return `skill_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 转换为技能配置DTO
   */
  private toSkillConfigDto(skillDefinition: any): any {
    return {
      skillId: skillDefinition.skillId,
      name: skillDefinition.name,
      description: skillDefinition.description,
      icon: skillDefinition.icon,
      type: skillDefinition.type,
      position: skillDefinition.position,
      rarity: skillDefinition.rarity,
      maxLevel: skillDefinition.maxLevel,
      cooldown: skillDefinition.cooldown,
      energyCost: skillDefinition.energyCost,
      upgrades: skillDefinition.upgrades,
      unlockLevel: skillDefinition.unlockLevel,
      unlockItems: skillDefinition.unlockItems,
      unlockCondition: skillDefinition.unlockCondition,
      isActive: skillDefinition.isActive,
      isLimited: skillDefinition.isLimited,
      limitEndTime: skillDefinition.limitEndTime,
      isExpired: skillDefinition.isExpired,
      isAvailable: skillDefinition.isAvailable,
      baseEffects: skillDefinition.baseEffects,
      maxLevelEffects: skillDefinition.maxLevelEffects,
    };
  }

  /**
   * 转换为球员技能信息DTO
   */
  private toSkillInfoDto(skill: SkillDocument): SkillInfoDto {
    return {
      skillId: skill.skillId,
      heroId: skill.heroId,
      configId: skill.configId,
      level: skill.level,
      experience: skill.experience,
      activeStatus: skill.activeStatus,
      slotPosition: skill.slotPosition,
      usageCount: skill.usageCount,
      lastUsedTime: skill.lastUsedTime,
      upgradeCount: skill.upgradeCount,
      totalUpgradeCost: skill.totalUpgradeCost,
      obtainTime: skill.obtainTime,
      obtainSource: skill.obtainSource,
      isLocked: skill.isLocked,
      isActive: skill.isActive,
      isEquipped: skill.isEquipped,
      canUpgrade: skill.canUpgrade,
      nextLevelExp: skill.nextLevelExp,
      upgradeProgress: skill.upgradeProgress,
    };
  }

  /**
   * 检查升级资源
   * 基于old项目: checkResourceIsEnough和BeliefSkillExpend配置表
   *
   * 实现逻辑：
   * 1. 获取升级配置
   * 2. 检查不同类型的资源需求
   * 3. 验证资源是否足够
   */
  private async checkUpgradeResources(skill: any, targetLevel: number, upgradeCost: number): Promise<XResult<any>> {
    // 1. 获取升级配置（基于old项目BeliefSkillExpend配置表）
    const upgradeConfigResult = await this.getSkillUpgradeConfig(skill.skillType, targetLevel);
    if (XResultUtils.isFailure(upgradeConfigResult)) {
      return XResultUtils.error(`获取升级配置失败: ${upgradeConfigResult.message}`, upgradeConfigResult.code);
    }

    const upgradeConfig = upgradeConfigResult.data;
    if (!upgradeConfig) {
      const defaultResourceCheck = {
        sufficient: false,
        message: '升级配置不存在',
        required: {},
        current: {},
      };
      return XResultUtils.ok(defaultResourceCheck);
    }

    // 2. 检查金币资源（基于old项目逻辑）
    if (upgradeCost > 0) {
      const goldCheckResult = await this.checkCharacterGold(skill.characterId, upgradeCost);
      if (XResultUtils.isFailure(goldCheckResult)) {
        return XResultUtils.error(`检查金币失败: ${goldCheckResult.message}`, goldCheckResult.code);
      }

      if (!goldCheckResult.data.sufficient) {
        const goldInsufficientCheck = {
          sufficient: false,
          message: '金币不足',
          required: { gold: upgradeCost },
          current: { gold: goldCheckResult.data.current },
        };
        return XResultUtils.ok(goldInsufficientCheck);
      }
    }

    // 3. 检查信仰活跃度（基于old项目beliefLiveness）
    if (upgradeConfig.contribution > 0) {
      const beliefCheckResult = await this.checkCharacterBelief(skill.characterId, upgradeConfig.contribution);
      if (XResultUtils.isFailure(beliefCheckResult)) {
        return XResultUtils.error(`检查信仰活跃度失败: ${beliefCheckResult.message}`, beliefCheckResult.code);
      }

      if (!beliefCheckResult.data.sufficient) {
        const beliefInsufficientCheck = {
          sufficient: false,
          message: '信仰活跃度不足',
          required: { belief: upgradeConfig.contribution },
          current: { belief: beliefCheckResult.data.current },
        };
        return XResultUtils.ok(beliefInsufficientCheck);
      }
    }

    // 4. 检查道具资源（基于old项目ItemID和ItemNumber）
    if (upgradeConfig.itemId && upgradeConfig.itemNumber > 0) {
      const itemCheckResult = await this.checkCharacterItem(skill.characterId, upgradeConfig.itemId, upgradeConfig.itemNumber);
      if (XResultUtils.isFailure(itemCheckResult)) {
        return XResultUtils.error(`检查道具失败: ${itemCheckResult.message}`, itemCheckResult.code);
      }

      if (!itemCheckResult.data.sufficient) {
        const itemInsufficientCheck = {
          sufficient: false,
          message: '道具不足',
          required: { [`item_${upgradeConfig.itemId}`]: upgradeConfig.itemNumber },
          current: { [`item_${upgradeConfig.itemId}`]: itemCheckResult.data.current },
        };
        return XResultUtils.ok(itemInsufficientCheck);
      }
    }

    const sufficientResourceCheck = {
      sufficient: true,
      message: '资源充足',
      required: {
        gold: upgradeCost,
        belief: upgradeConfig.contribution || 0,
        [`item_${upgradeConfig.itemId}`]: upgradeConfig.itemNumber || 0,
      },
      current: {},
    };

    return XResultUtils.ok(sufficientResourceCheck);
  }

  /**
   * 获取技能升级配置
   * 基于old项目: BeliefSkillExpend配置表
   * 修复：使用GameConfigFacade的高效查询方法
   * 注意：BeliefSkillExpend表中id字段就是等级，只有id和contribution两个字段
   */
  private async getSkillUpgradeConfig(skillType: string, level: number): Promise<XResult<any>> {
    try {
      // 使用get方法根据等级获取配置（id就是等级）
      const config = await this.gameConfig.beliefSkillExpend?.get(level);

      if (config) {
        // 返回包含等级信息的配置
        const upgradeConfig = {
          level: config.id, // id就是等级
          contribution: config.contribution, // 信仰活跃度消耗
          skillType, // 传入的技能类型
          itemId: 20001, // 道具ID（从old项目逻辑推断）
          itemNumber: Math.floor(level / 2) + 1, // 道具数量（从old项目逻辑推断）
        };
        return XResultUtils.ok(upgradeConfig);
      }

      // 如果配置不存在，返回默认配置
      this.logger.warn(`技能升级配置不存在: skillType=${skillType}, level=${level}, 使用默认配置`);
      const defaultConfig = {
        level,
        skillType,
        contribution: level * 100, // 信仰活跃度消耗
        itemId: 20001, // 道具ID
        itemNumber: Math.floor(level / 2) + 1, // 道具数量
      };
      return XResultUtils.ok(defaultConfig);
    } catch (error) {
      this.logger.error('获取技能升级配置失败', error);
      return XResultUtils.error('获取技能升级配置失败', 'GET_SKILL_UPGRADE_CONFIG_ERROR');
    }
  }

  /**
   * 检查角色金币
   * 基于old项目: checkResourceIsEnough逻辑
   */
  private async checkCharacterGold(characterId: string, requiredAmount: number): Promise<XResult<any>> {
    // TODO: 调用Character服务检查金币
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
    //   'character.checkCurrency',
    //   { characterId, currencyType: 'gold', amount: requiredAmount }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`检查金币失败: ${result.message}`, result.code);
    // }
    //
    // const goldCheckData = {
    //   sufficient: result.data.sufficient,
    //   current: result.data.current,
    // };
    // return XResultUtils.ok(goldCheckData);

    // 暂时返回模拟数据
    const mockCurrentGold = Math.floor(Math.random() * 1000000) + requiredAmount;
    const goldCheckData = {
      sufficient: mockCurrentGold >= requiredAmount,
      current: mockCurrentGold,
    };
    return XResultUtils.ok(goldCheckData);
  }

  /**
   * 检查角色信仰活跃度
   * 基于old项目: beliefLiveness资源检查
   */
  private async checkCharacterBelief(characterId: string, requiredAmount: number): Promise<XResult<any>> {
    // TODO: 调用Character服务检查信仰活跃度
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
    //   'character.checkResource',
    //   { characterId, resourceType: 'beliefLiveness', amount: requiredAmount }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`检查信仰活跃度失败: ${result.message}`, result.code);
    // }
    //
    // const beliefCheckData = {
    //   sufficient: result.data.sufficient,
    //   current: result.data.current,
    // };
    // return XResultUtils.ok(beliefCheckData);

    // 暂时返回模拟数据
    const mockCurrentBelief = Math.floor(Math.random() * 10000) + requiredAmount;
    const beliefCheckData = {
      sufficient: mockCurrentBelief >= requiredAmount,
      current: mockCurrentBelief,
    };
    return XResultUtils.ok(beliefCheckData);
  }

  /**
   * 检查角色道具
   * 基于old项目: 道具数量检查逻辑
   */
  private async checkCharacterItem(characterId: string, itemId: number, requiredQuantity: number): Promise<XResult<any>> {
    // 调用Character服务检查道具
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'inventory.checkItem',
      { characterId, itemId, quantity: requiredQuantity }
    );

    if (XResultUtils.isFailure(result)) {
      this.logger.error(`检查角色道具失败: ${result.message}`);
      const defaultItemCheck = { sufficient: false, current: 0 };
      return XResultUtils.ok(defaultItemCheck);
    }

    const itemCheckData = {
      sufficient: result.data?.sufficient || false,
      current: result.data?.current || 0,
    };
    return XResultUtils.ok(itemCheckData);
  }
}
