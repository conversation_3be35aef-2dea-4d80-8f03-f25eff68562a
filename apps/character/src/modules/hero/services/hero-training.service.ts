import { Injectable, Logger } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { GameConfigFacade } from '@libs/game-config';
import { HeroRepository } from '@hero/common/repositories/hero.repository';
import { TrainHeroDto, TrainResultDto } from '@hero/common/dto/hero.dto';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { MicroserviceClientService } from '@libs/service-mesh';

import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 统一的球员训练业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 基于old项目的训练系统业务逻辑，从hero.service.ts迁移而来，提供完整的训练功能
 *
 * 🎯 核心功能：
 * - 球员特训系统（4种训练类型：初级、中级、高级、定向训练）
 * - 阶段性培养系统（9个阶段，每个阶段有不同的训练效果）
 * - 训练状态管理（训练中、锁定训练、冷却时间）
 * - 特训替换系统（将特训属性应用到球员身上）
 * - 训练消耗计算（金币消耗、道具消耗、阶段递增）
 *
 * 🚀 性能优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的Result模式错误处理
 * - 智能缓存机制优化训练数据访问
 * - 批量操作减少数据库访问次数
 *
 * 📊 old项目数据结构映射：
 * - HeroTrain: 特训数据结构（type1-type4，additive累积属性）
 * - TrainingStage: 阶段性培养数据（stage、progress、totalTrainingCount）
 * - TrainingEffect: 训练效果计算（属性提升、阶段进度、品质加成）
 */
@Injectable()
export class TrainingService extends BaseService {
  constructor(
    private readonly heroRepository: HeroRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient?: MicroserviceClientService,
  ) {
    super('TrainingService', microserviceClient);
  }

  /**
   * 获取球员特训信息
   * 基于old项目: getTrainInfo
   * Controller层已验证参数，无需重复验证
   */
  async getTrainInfo(heroId: string): Promise<XResult<any>> {
    this.logger.log(`获取球员特训信息: ${heroId}`);

    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero) {
      return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
    }

    // 基于当前HeroTraining结构返回训练信息
    // 适配old项目的HeroTrain结构体格式
    const heroTrain = {
      uid: heroId,
      // 基于当前阶段性训练系统构建type1-type4数据
      type1: { // 初级训练
        property: [
          hero.training?.speedStage?.stage || 1,
          hero.training?.shootingStage?.stage || 1,
          hero.training?.passingStage?.stage || 1
        ],
        random: [0, 0, 0] // 随机属性，当前系统暂不使用
      },
      type2: { // 中级训练
        property: [
          hero.training?.defendingStage?.stage || 1,
          hero.training?.dribblingStage?.stage || 1,
          hero.training?.physicalityStage?.stage || 1
        ],
        random: [0, 0, 0]
      },
      type3: { // 高级训练
        property: [
          Math.max(hero.training?.speedStage?.stage || 1, hero.training?.shootingStage?.stage || 1),
          hero.training?.trainingCount || 0,
          0
        ],
        random: [0, 0, 0]
      },
      type4: { // 定向训练
        property: [0, 0, 0], // 定向训练数据，当前系统暂不使用
        random: [0, 0, 0]
      },
      additive: [0, 0, 0], // 累积的训练属性，当前系统暂不使用
    };

    return XResultUtils.ok(heroTrain);
  }

  /**
   * 设置球员训练状态
   * 从hero.service.ts迁移而来
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async setHeroTrainStatus(heroId: string, isTrain: boolean, isLockTrain?: boolean): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      const updateData: any = { isTrain };
      if (isLockTrain !== undefined) {
        updateData.isLockTrain = isLockTrain;
      }

      const updateResult = await this.heroRepository.updateById(heroId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员状态失败: ${updateResult.message}`, updateResult.code);
      }

      const updatedHeroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(updatedHeroResult)) {
        return XResultUtils.error(`获取更新后球员信息失败: ${updatedHeroResult.message}`, updatedHeroResult.code);
      }

      this.logger.log(`球员训练状态更新: ${heroId}, 训练中: ${isTrain}, 锁定: ${isLockTrain}`);
      return XResultUtils.ok(updatedHeroResult.data);
    }, { reason: 'set_hero_train_status', metadata: { heroId, isTrain, isLockTrain } });
  }

  /**
   * 训练球员（阶段性培养系统）
   * 从hero.service.ts迁移而来
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async trainHero(trainDto: TrainHeroDto): Promise<XResult<TrainResultDto>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(trainDto.heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 检查是否可以训练
      if (!hero.canTrain) {
        return XResultUtils.error('球员训练冷却中', 'HERO_TRAINING_COOLDOWN');
      }

      // 检查球员是否在训练中或治疗中
      if (hero.isTrain || hero.isTreat) {
        return XResultUtils.error('球员正在训练或治疗中', 'HERO_IN_TRAINING');
      }

      const count = trainDto.count || 1;
      const trainingTime = Date.now();

      // 根据训练类型执行不同的训练逻辑
      let trainingResult;
      switch (trainDto.trainingType) {
        case 1: // 初级训练
        case 2: // 中级训练
        case 3: // 高级训练
          trainingResult = await this.executeGeneralTraining(hero, trainDto, count);
          break;
        case 4: // 定向训练
          trainingResult = await this.executeTargetedTraining(hero, trainDto, count);
          break;
        default:
          return XResultUtils.error('无效的训练类型', 'INVALID_TRAINING_TYPE');
      }

      if (XResultUtils.isFailure(trainingResult)) {
        return XResultUtils.error(`训练执行失败: ${trainingResult.message}`, trainingResult.code);
      }

      // 更新球员训练数据
      const updateData = {
        'training.trainingCount': (hero.training?.trainingCount || 0) + count,
        'training.lastTrainingTime': trainingTime,
        'training.trainingCooldown': this.calculateTrainingCooldown(trainDto.trainingType),
        ...trainingResult.data.updateData,
      };

      const updateResult = await this.heroRepository.update(trainDto.heroId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员训练数据失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`球员训练完成: ${trainDto.heroId}, 类型: ${trainDto.trainingType}, 次数: ${count}`);

      const trainResultDto: TrainResultDto = {
        success: true,
        heroId: trainDto.heroId,
        trainingType: trainDto.trainingType,
        trainingMethod: trainDto.trainingMethod,
        attributeChanges: trainingResult.data.attributeChanges,
        goldCost: trainingResult.data.goldCost,
        itemCost: trainingResult.data.itemCost,
        trainCount: count,
        currentStage: trainingResult.data.currentStage,
        reachedStageLimit: trainingResult.data.reachedStageLimit,
        nextTrainTime: trainingTime + this.calculateTrainingCooldown(trainDto.trainingType),
        trainingTime,
      };

      return XResultUtils.ok(trainResultDto);
    }, { reason: 'train_hero', metadata: { heroId: trainDto.heroId, trainingType: trainDto.trainingType, count: trainDto.count } });
  }

  /**
   * 替换特训
   * 基于old项目: replaceHeroTrain
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async replaceHeroTrain(heroId: string, index: number): Promise<XResult<number>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`替换特训: ${heroId}, 方式: ${index}`);

      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 基于old项目实现替换特训逻辑
      // old项目中replaceHeroTrain会将特训属性应用到球员身上

      // 获取球员的特训数据
      const heroTrainResult = await this.getHeroTrainingData(heroId);
      if (XResultUtils.isFailure(heroTrainResult)) {
        return XResultUtils.error(`获取特训数据失败: ${heroTrainResult.message}`, heroTrainResult.code);
      }

      const heroTrain = heroTrainResult.data;
      if (!heroTrain) {
        return XResultUtils.error('球员特训数据不存在', 'HERO_TRAINING_DATA_NOT_FOUND');
      }

      // 检查特训类型是否有效
      if (index < 1 || index > 4) {
        return XResultUtils.error('无效的特训类型', 'INVALID_TRAINING_INDEX');
      }

      // 获取对应类型的特训数据
      const trainingTypeData = heroTrain[index];
      if (!trainingTypeData) {
        return XResultUtils.error('没有对应的特训数据', 'NO_TRAINING_DATA_FOR_INDEX');
      }

      // 基于old项目：将特训属性应用到球员身上
      const updateData: any = {};
      const attributeNames = this.getAttributeNames();

      // 先初始化additive（清零之前的特训加成）
      for (const attrName of attributeNames) {
        if (heroTrain.additive && heroTrain.additive[attrName] !== undefined) {
          heroTrain.additive[attrName] = 0;
          // 清除球员身上的特训加成
          updateData[`attributes.${attrName}`] =
            (hero.attributes?.[attrName] || 0) - (heroTrain.additive[attrName] || 0);
        }
      }

      // 将当前特训类型的属性值应用到球员身上
      for (const attrName of attributeNames) {
        const trainingValue = trainingTypeData[attrName];
        if (trainingValue && trainingValue > 0) {
          // 将特训值赋给additive
          if (!heroTrain.additive) {
            heroTrain.additive = {};
          }
          heroTrain.additive[attrName] = trainingValue;

          // 将特训值加到球员属性上
          const currentValue = hero.attributes?.[attrName] || 0;
          updateData[`attributes.${attrName}`] = currentValue + trainingValue;

          // 清空该特训类型的对应属性值
          trainingTypeData[attrName] = 0;
        }
      }

      // 更新特训数据
      updateData[`training.type${index}`] = trainingTypeData;
      updateData['training.additive'] = heroTrain.additive;

      const updateResult = await this.heroRepository.updateById(heroId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员特训数据失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`特训替换完成: ${heroId}, 方式: ${index}`);
      return XResultUtils.ok(index);
    }, { reason: 'replace_hero_train', metadata: { heroId, index } });
  }

  // ==================== 训练系统辅助方法（从hero.service.ts迁移） ====================

  /**
   * 执行通用训练（初级、中级、高级）
   * 使用Result模式，正确处理训练逻辑的每个步骤
   */
  private async executeGeneralTraining(hero: any, trainDto: any, count: number): Promise<XResult<any>> {
    try {
      const attributeChanges = {};
      const updateData = {};
      let goldCost = 0;
      let itemCost = [];
      let currentStage = 1;
      let reachedStageLimit = false;

      // 根据训练类型确定影响的属性
      const affectedAttributes = this.getAffectedAttributesByTrainingType(trainDto.trainingType);

      for (const attribute of affectedAttributes) {
        const stageInfo = hero.training?.[`${attribute}Stage`] || { stage: 1, stageProgress: 0, totalTrainingCount: 0 };
        const currentAttributeValue = hero.attributes?.[attribute] || 0;

        // 计算训练效果
        const trainingEffect = this.calculateTrainingEffect(
          trainDto.trainingType,
          stageInfo.stage,
          hero.quality,
          count
        );

        // 计算消耗
        const cost = this.calculateTrainingCost(trainDto.trainingType, trainDto.trainingMethod, stageInfo.stage, count);
        goldCost += cost.gold;
        itemCost = itemCost.concat(cost.items);

        // 更新属性值
        const newAttributeValue = Math.min(currentAttributeValue + trainingEffect.attributeIncrease, 100);

        attributeChanges[attribute] = {
          oldValue: currentAttributeValue,
          newValue: newAttributeValue,
          increase: trainingEffect.attributeIncrease,
        };

        // 更新阶段进度
        const newStageProgress = stageInfo.stageProgress + trainingEffect.stageProgress;
        const newStage = this.calculateNewStage(stageInfo.stage, newStageProgress);

        updateData[`attributes.${attribute}`] = newAttributeValue;
        updateData[`training.${attribute}Stage.stage`] = newStage.stage;
        updateData[`training.${attribute}Stage.stageProgress`] = newStage.progress;
        updateData[`training.${attribute}Stage.totalTrainingCount`] = stageInfo.totalTrainingCount + count;

        currentStage = Math.max(currentStage, newStage.stage);
        if (newStage.stage >= 9) {
          reachedStageLimit = true;
        }
      }

      return XResultUtils.ok({
        attributeChanges,
        updateData,
        goldCost,
        itemCost,
        currentStage,
        reachedStageLimit,
      });
    } catch (error) {
      this.logger.error('执行通用训练失败', error);
      return XResultUtils.error('执行通用训练失败', 'EXECUTE_GENERAL_TRAINING_ERROR');
    }
  }

  /**
   * 执行定向训练
   * 使用Result模式，正确处理定向训练逻辑的每个步骤
   */
  private async executeTargetedTraining(hero: any, trainDto: any, count: number): Promise<XResult<any>> {
    try {
      const attributeChanges = {};
      const updateData = {};
      let goldCost = 0;
      let itemCost = [];
      let currentStage = 1;
      let reachedStageLimit = false;

      // 定向训练只影响指定的属性
      const targetAttributes = trainDto.targetAttributes || ['speed']; // 默认训练速度

      for (const attribute of targetAttributes) {
        const stageInfo = hero.training?.[`${attribute}Stage`] || { stage: 1, stageProgress: 0, totalTrainingCount: 0 };
        const currentAttributeValue = hero.attributes?.[attribute] || 0;

        // 定向训练效果更强
        const trainingEffect = this.calculateTargetedTrainingEffect(
          stageInfo.stage,
          hero.quality,
          count
        );

        // 定向训练消耗更高
        const cost = this.calculateTargetedTrainingCost(trainDto.trainingMethod, stageInfo.stage, count);
        goldCost += cost.gold;
        itemCost = itemCost.concat(cost.items);

        // 更新属性值
        const newAttributeValue = Math.min(currentAttributeValue + trainingEffect.attributeIncrease, 100);

        attributeChanges[attribute] = {
          oldValue: currentAttributeValue,
          newValue: newAttributeValue,
          increase: trainingEffect.attributeIncrease,
        };

        // 更新阶段进度
        const newStageProgress = stageInfo.stageProgress + trainingEffect.stageProgress;
        const newStage = this.calculateNewStage(stageInfo.stage, newStageProgress);

        updateData[`attributes.${attribute}`] = newAttributeValue;
        updateData[`training.${attribute}Stage.stage`] = newStage.stage;
        updateData[`training.${attribute}Stage.stageProgress`] = newStage.progress;
        updateData[`training.${attribute}Stage.totalTrainingCount`] = stageInfo.totalTrainingCount + count;

        currentStage = Math.max(currentStage, newStage.stage);
        if (newStage.stage >= 9) {
          reachedStageLimit = true;
        }
      }

      return XResultUtils.ok({
        attributeChanges,
        updateData,
        goldCost,
        itemCost,
        currentStage,
        reachedStageLimit,
      });
    } catch (error) {
      this.logger.error('执行定向训练失败', error);
      return XResultUtils.error('执行定向训练失败', 'EXECUTE_TARGETED_TRAINING_ERROR');
    }
  }

  /**
   * 根据训练类型获取影响的属性
   */
  private getAffectedAttributesByTrainingType(trainingType: number): string[] {
    switch (trainingType) {
      case 1: // 初级训练 - 全属性小幅提升
        return ['speed', 'shooting', 'passing', 'defending', 'dribbling', 'physicality'];
      case 2: // 中级训练 - 主要属性中幅提升
        return ['speed', 'shooting', 'passing', 'defending'];
      case 3: // 高级训练 - 核心属性大幅提升
        return ['speed', 'shooting'];
      default:
        return ['speed'];
    }
  }

  /**
   * 计算训练效果
   */
  private calculateTrainingEffect(trainingType: number, stage: number, quality: number, count: number): any {
    // 基础效果根据训练类型和阶段计算
    const baseEffect = this.getBaseTrainingEffect(trainingType, stage);

    // 品质加成
    const qualityMultiplier = 1 + (quality - 1) * 0.1;

    // 次数加成
    const countMultiplier = count;

    const attributeIncrease = Math.round(baseEffect.attribute * qualityMultiplier * countMultiplier);
    const stageProgress = Math.round(baseEffect.progress * countMultiplier);

    return { attributeIncrease, stageProgress };
  }

  /**
   * 获取基础训练效果
   */
  private getBaseTrainingEffect(trainingType: number, stage: number): any {
    const stageMultiplier = Math.max(1, 10 - stage); // 阶段越高，效果递减

    switch (trainingType) {
      case 1: // 初级训练
        return {
          attribute: 1 * stageMultiplier,
          progress: 10 * stageMultiplier,
        };
      case 2: // 中级训练
        return {
          attribute: 2 * stageMultiplier,
          progress: 15 * stageMultiplier,
        };
      case 3: // 高级训练
        return {
          attribute: 3 * stageMultiplier,
          progress: 20 * stageMultiplier,
        };
      default:
        return { attribute: 1, progress: 10 };
    }
  }

  /**
   * 计算定向训练效果
   */
  private calculateTargetedTrainingEffect(stage: number, quality: number, count: number): any {
    // 定向训练效果是高级训练的1.5倍
    const baseEffect = this.getBaseTrainingEffect(3, stage);
    const qualityMultiplier = 1 + (quality - 1) * 0.1;
    const countMultiplier = count;
    const targetedMultiplier = 1.5;

    const attributeIncrease = Math.round(baseEffect.attribute * qualityMultiplier * countMultiplier * targetedMultiplier);
    const stageProgress = Math.round(baseEffect.progress * countMultiplier * targetedMultiplier);

    return { attributeIncrease, stageProgress };
  }

  /**
   * 计算训练消耗
   */
  private calculateTrainingCost(trainingType: number, trainingMethod: number, stage: number, count: number): any {
    const baseCost = this.getBaseTrainingCost(trainingType, stage);
    const countMultiplier = count;
    const stageMultiplier = Math.pow(1.2, stage - 1); // 阶段越高，消耗越大

    if (trainingMethod === 1) { // 使用金币
      return {
        gold: Math.round(baseCost.gold * countMultiplier * stageMultiplier),
        items: [],
      };
    } else { // 使用道具
      return {
        gold: 0,
        items: baseCost.items.map(item => ({ ...item, quantity: item.quantity * count })),
      };
    }
  }

  /**
   * 获取基础训练消耗
   */
  private getBaseTrainingCost(trainingType: number, stage: number): any {
    switch (trainingType) {
      case 1: // 初级训练
        return {
          gold: 1000,
          items: [{ itemId: 'training_item_1', quantity: 1 }],
        };
      case 2: // 中级训练
        return {
          gold: 2000,
          items: [{ itemId: 'training_item_2', quantity: 1 }],
        };
      case 3: // 高级训练
        return {
          gold: 5000,
          items: [{ itemId: 'training_item_3', quantity: 1 }],
        };
      default:
        return { gold: 1000, items: [] };
    }
  }

  /**
   * 计算定向训练消耗
   */
  private calculateTargetedTrainingCost(trainingMethod: number, stage: number, count: number): any {
    const baseCost = this.getBaseTrainingCost(3, stage); // 基于高级训练
    const countMultiplier = count;
    const stageMultiplier = Math.pow(1.3, stage - 1); // 定向训练消耗更高
    const targetedMultiplier = 2; // 定向训练消耗是高级训练的2倍

    if (trainingMethod === 1) { // 使用金币
      return {
        gold: Math.round(baseCost.gold * countMultiplier * stageMultiplier * targetedMultiplier),
        items: [],
      };
    } else { // 使用道具
      return {
        gold: 0,
        items: [{ itemId: 'targeted_training_item', quantity: count }],
      };
    }
  }

  /**
   * 计算新阶段
   */
  private calculateNewStage(currentStage: number, newProgress: number): any {
    const progressPerStage = 100; // 每阶段需要100进度
    let stage = currentStage;
    let progress = newProgress;

    while (progress >= progressPerStage && stage < 9) {
      progress -= progressPerStage;
      stage++;
    }

    return { stage, progress };
  }

  /**
   * 计算训练冷却时间
   */
  private calculateTrainingCooldown(trainingType: number): number {
    switch (trainingType) {
      case 1: // 初级训练 - 10分钟
        return 10 * 60 * 1000;
      case 2: // 中级训练 - 20分钟
        return 20 * 60 * 1000;
      case 3: // 高级训练 - 30分钟
        return 30 * 60 * 1000;
      case 4: // 定向训练 - 60分钟
        return 60 * 60 * 1000;
      default:
        return 10 * 60 * 1000;
    }
  }

  /**
   * 获取球员特训数据
   * 基于old项目: getOneHeroTrain
   * 使用Result模式，正确处理数据获取结果
   */
  private async getHeroTrainingData(heroId: string): Promise<XResult<any>> {
    // 在新架构中，特训数据存储在Hero的training字段中
    // 但old项目中是独立的heroTrain数据结构
    // 这里需要构造兼容old项目的数据结构

    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero || !hero.training) {
      return XResultUtils.error('球员特训数据不存在', 'HERO_TRAINING_DATA_NOT_FOUND');
    }

    // 构造old项目兼容的特训数据结构
    const trainingData = {
      uid: heroId,
      additive: hero.training.additive || {},
      1: hero.training.type1 || {}, // 初级特训
      2: hero.training.type2 || {}, // 中级特训
      3: hero.training.type3 || {}, // 高级特训
      4: hero.training.type4 || {}, // 定向特训
    };

    return XResultUtils.ok(trainingData);
  }

  /**
   * 获取属性名列表
   * 基于old项目: commonEnum.ONE_LEVEL_ATTR_NAMES
   */
  private getAttributeNames(): string[] {
    return [
      'speed',        // 速度
      'shooting',     // 射门 (对应old项目的Finishing)
      'passing',      // 传球
      'defending',    // 防守 (对应old项目的StandingTackle)
      'dribbling',    // 盘带
      'physicality',  // 身体 (对应old项目的Strength)
      'goalkeeping',  // 门将 (对应old项目的Save)
    ];
  }
}
