import { Injectable, Logger, forwardRef, Inject } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { GameConfigFacade } from '@libs/game-config';
import { BeliefRepository } from './repositories/belief.repository';
import { CharacterService } from '../character/character.service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import {
  CreateBeliefPayloadDto,
  JoinBeliefPayloadDto,
  LeaveBeliefPayloadDto,
  DonateBeliefPayloadDto,
  UpdateBeliefNoticePayloadDto,
  GetBeliefInfoPayloadDto,
  GetBeliefListPayloadDto,
  SetBeliefLeaderPayloadDto,
  GetBeliefMembersPayloadDto,
  GetBeliefRankPayloadDto,
  GetBeliefNotificationsPayloadDto,
  TransferChairmanPayloadDto,
  DisbandBeliefPayloadDto,
  SearchBeliefPayloadDto,
  GetMyBeliefPayloadDto,
  KickBeliefMemberPayloadDto,
  GetBeliefStatsPayloadDto
} from '../../common/dto/belief-payload.dto';

/**
 * 信仰系统业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 严格基于old项目的信仰系统业务逻辑
 *
 * 🎯 核心功能：
 * - 信仰创建和管理
 * - 信仰成员管理（加入、退出）
 * - 信仰捐献系统（欧元、球币）
 * - 信仰等级和经验管理
 * - 信仰领导者管理
 * - 信仰排行和统计
 *
 * 🚀 架构优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的Result模式错误处理
 * - 与CharacterService紧密集成，保证数据一致性
 * - 事件驱动的跨服务通信
 *
 * 🔗 服务依赖：
 * - CharacterService：角色信息、资源检查（直接注入）
 * - BeliefRepository：信仰数据持久化（直接注入）
 * - GameConfigFacade：配置数据获取（直接注入）
 *
 * old项目核心逻辑：
 * - 信仰捐献：donateBeliefGold(type, num)
 * - 信仰升级：addBeliefLiveness(value)
 * - 成员管理：playerList数组操作
 * - 领导者管理：leader数组操作
 */
@Injectable()
export class BeliefService extends BaseService {
  constructor(
    private readonly beliefRepository: BeliefRepository,
    private readonly gameConfig: GameConfigFacade,
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
  ) {
    super('BeliefService');
  }

  /**
   * 创建信仰
   * 基于old项目: 创建新的信仰组织
   */
  async createBelief(dto: CreateBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`创建信仰: ${dto.characterId}, 名称: ${dto.name}`);

      // 1. 检查角色是否已有信仰
      const existingBeliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(existingBeliefResult)) {
        return XResultUtils.error(`检查现有信仰失败: ${existingBeliefResult.message}`, existingBeliefResult.code);
      }

      if (existingBeliefResult.data) {
        return XResultUtils.error('角色已加入信仰，无法创建新信仰', 'ALREADY_IN_BELIEF');
      }

      // 2. 检查信仰名称是否可用
      const nameAvailableResult = await this.beliefRepository.isNameAvailable(dto.name);
      if (XResultUtils.isFailure(nameAvailableResult)) {
        return XResultUtils.error(`检查名称可用性失败: ${nameAvailableResult.message}`, nameAvailableResult.code);
      }

      if (!nameAvailableResult.data) {
        return XResultUtils.error('信仰名称已被使用', 'BELIEF_NAME_TAKEN');
      }

      // 3. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 4. 检查创建费用（基于old项目逻辑）
      const createCost = await this.getBeliefCreateCost();
      if (character.cash < createCost) {
        return XResultUtils.error('现金不足，无法创建信仰', 'INSUFFICIENT_CASH');
      }

      // 5. 扣除创建费用
      const deductResult = await this.characterService.deductCurrency({
        characterId: dto.characterId,
        currencyType: 'cash',
        amount: createCost,
        reason: 'create_belief'
      });

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除费用失败: ${deductResult.message}`, deductResult.code);
      }

      // 6. 获取下一个信仰ID
      const nextIdResult = await this.beliefRepository.getNextBeliefId();
      if (XResultUtils.isFailure(nextIdResult)) {
        return XResultUtils.error(`获取信仰ID失败: ${nextIdResult.message}`, nextIdResult.code);
      }

      const beliefId = nextIdResult.data;

      // 7. 创建信仰实体
      const beliefData = {
        beliefId,
        name: dto.name,
        notice: dto.notice || '',
        creatorId: dto.characterId,
        leader: [{
          uid: dto.characterId,
          gid: '', // 预留字段
          name: character.name,
          faceUrl: character.avatar || '',
          pos: 1, // 董事长
          appointTime: Date.now()
        }],
        playerList: [{
          playerId: dto.characterId,
          playerName: character.name,
          faceUrl: character.avatar || '',
          contribution: 0,
          weekContribution: 0,
          joinTime: Date.now(),
          lastActiveTime: Date.now(),
          isActive: true
        }],
        level: 1,
        beliefExp: 0,
        beliefGold: 0,
        status: 'active',
        createTime: Date.now()
      };

      const createResult = await this.beliefRepository.createOne(beliefData);
      if (XResultUtils.isFailure(createResult)) {
        // 创建失败，退还费用
        await this.characterService.addCurrency({
          characterId: dto.characterId,
          currencyType: 'cash',
          amount: createCost,
          reason: 'refund_belief_creation'
        });
        return XResultUtils.error(`创建信仰失败: ${createResult.message}`, createResult.code);
      }

      const belief = createResult.data;

      // 8. 更新角色的信仰信息（暂时跳过，待实现）
      // TODO: 实现角色信仰信息更新逻辑
      this.logger.log(`更新角色信仰信息: ${dto.characterId}, 信仰ID: ${beliefId}`);

      // 9. 添加创建动态
      belief.addNotify(`信仰"${dto.name}"成立，${character.name}成为首任董事长`, 'system');
      await belief.save();

      // 10. 触发信仰创建事件（通知其他服务）
      await this.notifyBeliefCreated(beliefId, dto.characterId);

      return XResultUtils.ok({
        beliefId,
        name: dto.name,
        level: 1,
        memberCount: 1,
        leaderPosition: 1,
        createCost
      });
    }, { reason: 'create_belief', metadata: { characterId: dto.characterId, name: dto.name } });
  }

  /**
   * 加入信仰
   * 基于old项目: 玩家申请加入信仰
   */
  async joinBelief(dto: JoinBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`加入信仰: ${dto.characterId}, 信仰ID: ${dto.beliefId}`);

      // 1. 检查角色是否已有信仰
      const existingBeliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(existingBeliefResult)) {
        return XResultUtils.error(`检查现有信仰失败: ${existingBeliefResult.message}`, existingBeliefResult.code);
      }

      if (existingBeliefResult.data) {
        return XResultUtils.error('角色已加入其他信仰', 'ALREADY_IN_BELIEF');
      }

      // 2. 获取目标信仰
      const beliefResult = await this.beliefRepository.findByBeliefId(dto.beliefId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief || belief.status !== 'active') {
        return XResultUtils.error('信仰不存在或已解散', 'BELIEF_NOT_FOUND');
      }

      // 3. 检查信仰是否已满
      if (belief.playerList.length >= belief.maxMembers) {
        return XResultUtils.error('信仰成员已满', 'BELIEF_FULL');
      }

      // 4. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 5. 添加成员到信仰
      try {
        belief.addMember({
          playerId: dto.characterId,
          playerName: character.name,
          faceUrl: character.avatar || '',
          contribution: 0,
          weekContribution: 0
        });

        // 6. 添加加入动态
        belief.addNotify(`${character.name}加入了信仰`, 'player', dto.characterId);

        // 7. 保存信仰数据
        const saveResult = await this.beliefRepository.updateById(belief._id, belief);
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
        }

        // 8. 更新角色的信仰信息（暂时跳过，待实现）
        // TODO: 实现角色信仰信息更新逻辑
        this.logger.log(`更新角色信仰信息: ${dto.characterId}, 信仰ID: ${dto.beliefId}`);

        // 9. 触发加入信仰事件
        await this.notifyBeliefJoined(dto.beliefId, dto.characterId);

        return XResultUtils.ok({
          beliefId: dto.beliefId,
          beliefName: belief.name,
          memberCount: belief.playerList.length,
          joinTime: Date.now()
        });

      } catch (error) {
        return XResultUtils.error(`加入信仰失败: ${error.message}`, 'JOIN_BELIEF_ERROR');
      }
    }, { reason: 'join_belief', metadata: { characterId: dto.characterId, beliefId: dto.beliefId } });
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 获取信仰创建费用
   */
  private async getBeliefCreateCost(): Promise<number> {
    try {
      // 从SystemParam配置表获取创建费用
      const config = await this.gameConfig.systemParam.get(201); // BELIEF_CREATE_COST
      return config ? config.parameter : 10000; // 默认10000现金
    } catch (error) {
      this.logger.warn('获取信仰创建费用配置失败，使用默认值', error);
      return 10000;
    }
  }

  /**
   * 通知信仰创建事件
   */
  private async notifyBeliefCreated(beliefId: number, creatorId: string): Promise<void> {
    try {
      // TODO: 发送事件到其他服务
      // this.eventEmitter.emit('belief.created', { beliefId, creatorId });
      this.logger.log(`信仰创建事件: ${beliefId}, 创建者: ${creatorId}`);
    } catch (error) {
      this.logger.error('通知信仰创建事件失败', error);
    }
  }

  /**
   * 通知加入信仰事件
   */
  private async notifyBeliefJoined(beliefId: number, playerId: string): Promise<void> {
    try {
      // TODO: 发送事件到其他服务
      // this.eventEmitter.emit('belief.joined', { beliefId, playerId });
      this.logger.log(`加入信仰事件: ${beliefId}, 玩家: ${playerId}`);
    } catch (error) {
      this.logger.error('通知加入信仰事件失败', error);
    }
  }

  /**
   * 退出信仰
   * 基于old项目: 玩家主动退出信仰
   */
  async leaveBelief(dto: LeaveBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`退出信仰: ${dto.characterId}`);

      // 1. 获取角色当前信仰
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 检查是否为董事长
      const isChairman = belief.leader.some(leader => leader.uid === dto.characterId && leader.pos === 1);
      if (isChairman && belief.playerList.length > 1) {
        return XResultUtils.error('董事长不能直接退出，请先转让职位或解散信仰', 'CHAIRMAN_CANNOT_LEAVE');
      }

      // 3. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 4. 从信仰中移除成员
      try {
        belief.removeMember(dto.characterId);

        // 5. 移除领导职位（如果有）
        belief.leader = belief.leader.filter(leader => leader.uid !== dto.characterId);

        // 6. 添加退出动态
        belief.addNotify(`${character.name}退出了信仰`, 'player', dto.characterId);

        // 7. 如果信仰没有成员了，解散信仰
        if (belief.playerList.length === 0) {
          belief.status = 'disbanded';
          belief.addNotify('信仰因无成员而自动解散', 'system');
        }

        // 8. 保存信仰数据
        const saveResult = await this.beliefRepository.updateById(belief._id, belief);
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
        }

        // 9. 清除角色的信仰信息（暂时跳过，待实现）
        // TODO: 实现角色信仰信息清除逻辑
        this.logger.log(`清除角色信仰信息: ${dto.characterId}`);

        // 10. 触发退出信仰事件
        await this.notifyBeliefLeft(belief.beliefId, dto.characterId);

        return XResultUtils.ok({
          beliefId: belief.beliefId,
          beliefName: belief.name,
          leftTime: Date.now(),
          isDisbanded: belief.status === 'disbanded'
        });

      } catch (error) {
        return XResultUtils.error(`退出信仰失败: ${error.message}`, 'LEAVE_BELIEF_ERROR');
      }
    }, { reason: 'leave_belief', metadata: { characterId: dto.characterId } });
  }

  /**
   * 信仰捐献
   * 基于old项目: donateBeliefGold(type, num)
   */
  async donateBelief(dto: DonateBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`信仰捐献: ${dto.characterId}, 类型: ${dto.type}, 数量: ${dto.amount}`);

      // 1. 获取角色当前信仰
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 3. 计算捐献获得的信仰资金和经验
      const donationResult = await this.calculateDonation(dto.type, dto.amount);
      if (XResultUtils.isFailure(donationResult)) {
        return XResultUtils.error(`计算捐献失败: ${donationResult.message}`, donationResult.code);
      }

      const { beliefGoldGain, expGain, costAmount, currencyType } = donationResult.data;

      // 4. 检查角色资源是否足够
      const currentAmount = character[currencyType] || 0;
      if (currentAmount < costAmount) {
        const errorMsg = currencyType === 'cash' ? '现金不足' : '金币不足';
        return XResultUtils.error(errorMsg, 'INSUFFICIENT_CURRENCY');
      }

      // 5. 扣除角色资源
      const deductResult = await this.characterService.deductCurrency({
        characterId: dto.characterId,
        currencyType,
        amount: costAmount,
        reason: 'belief_donation'
      });

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除资源失败: ${deductResult.message}`, deductResult.code);
      }

      // 6. 更新信仰资金和经验
      belief.beliefGold += beliefGoldGain;
      belief.totalDonation += beliefGoldGain;

      // 7. 更新信仰经验和等级
      const levelUpResult = belief.addExperience(expGain);

      // 8. 更新成员贡献
      const member = belief.playerList.find(m => m.playerId === dto.characterId);
      if (member) {
        member.contribution += beliefGoldGain;
        member.weekContribution += beliefGoldGain;
        member.lastActiveTime = Date.now();
        member.isActive = true;
      }

      // 9. 更新当日捐献记录（球币捐献）
      if (dto.type === 2) {
        belief.todayGoldNum += dto.amount;
      }

      // 10. 添加捐献动态
      const currencyName = currencyType === 'cash' ? '现金' : '金币';
      belief.addNotify(
        `${character.name}捐献了${costAmount}${currencyName}，信仰获得${beliefGoldGain}资金`,
        'player',
        dto.characterId
      );

      // 11. 如果升级了，添加升级动态
      if (levelUpResult.levelUp) {
        belief.addNotify(
          `信仰升级到${levelUpResult.newLevel}级！`,
          'system'
        );
      }

      // 12. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 13. 触发捐献事件
      await this.notifyBeliefDonation(belief.beliefId, dto.characterId, beliefGoldGain);

      return XResultUtils.ok({
        beliefGoldGain,
        expGain,
        costAmount,
        currencyType,
        newBeliefGold: belief.beliefGold,
        newLevel: belief.level,
        levelUp: levelUpResult.levelUp,
        memberContribution: member?.contribution || 0
      });
    }, { reason: 'donate_belief', metadata: { characterId: dto.characterId, type: dto.type, amount: dto.amount } });
  }

  // ==================== 私有辅助方法（续） ====================

  /**
   * 计算捐献收益
   * 基于old项目逻辑
   */
  private async calculateDonation(type: number, amount: number): Promise<XResult<{
    beliefGoldGain: number;
    expGain: number;
    costAmount: number;
    currencyType: string;
  }>> {
    try {
      let beliefGoldGain = 0;
      let costAmount = 0;
      let currencyType = '';

      if (type === 1) {
        // 现金捐献
        currencyType = 'cash';
        costAmount = amount;

        // 从配置获取现金转信仰资金比例
        const config = await this.gameConfig.systemParam.get(202); // CASH_BELIEF_GOLD_RATIO
        const ratio = config ? config.parameter : 100;
        beliefGoldGain = amount * ratio;
      } else {
        // 球币捐献
        currencyType = 'gold';
        costAmount = amount;

        // 从配置获取球币转信仰资金比例
        const config = await this.gameConfig.systemParam.get(203); // GOLD_BELIEF_GOLD_RATIO
        const ratio = config ? config.parameter : 1000;
        beliefGoldGain = amount * ratio;
      }

      // 经验获得 = 信仰资金获得 / 10
      const expGain = Math.floor(beliefGoldGain / 10);

      return XResultUtils.ok({
        beliefGoldGain,
        expGain,
        costAmount,
        currencyType
      });
    } catch (error) {
      return XResultUtils.error(`计算捐献收益失败: ${error.message}`, 'CALCULATE_DONATION_ERROR');
    }
  }

  /**
   * 通知退出信仰事件
   */
  private async notifyBeliefLeft(beliefId: number, playerId: string): Promise<void> {
    try {
      this.logger.log(`退出信仰事件: ${beliefId}, 玩家: ${playerId}`);
    } catch (error) {
      this.logger.error('通知退出信仰事件失败', error);
    }
  }

  /**
   * 通知信仰捐献事件
   */
  private async notifyBeliefDonation(beliefId: number, playerId: string, amount: number): Promise<void> {
    try {
      this.logger.log(`信仰捐献事件: ${beliefId}, 玩家: ${playerId}, 金额: ${amount}`);
    } catch (error) {
      this.logger.error('通知信仰捐献事件失败', error);
    }
  }

  // ==================== 控制器调用的其他方法 ====================

  /**
   * 获取信仰信息
   */
  async getBeliefInfo(dto: GetBeliefInfoPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰信息: ${dto.beliefId}`);

    const beliefResult = await this.beliefRepository.findByBeliefId(dto.beliefId);
    if (XResultUtils.isFailure(beliefResult)) {
      return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
    }

    const belief = beliefResult.data;
    if (!belief || belief.status !== 'active') {
      return XResultUtils.error('信仰不存在或已解散', 'BELIEF_NOT_FOUND');
    }

    return XResultUtils.ok({
      beliefId: belief.beliefId,
      name: belief.name,
      notice: belief.notice,
      level: belief.level,
      beliefExp: belief.beliefExp,
      beliefGold: belief.beliefGold,
      memberCount: belief.playerList.length,
      maxMembers: belief.maxMembers,
      leader: belief.leader,
      createTime: belief.createTime,
      beliefRank: belief.beliefRank
    });
  }

  /**
   * 获取信仰列表
   */
  async getBeliefList(dto: GetBeliefListPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰列表: 页码${dto.page || 1}`);

    const page = dto.page || 1;
    const limit = dto.limit || 20;

    const result = await this.beliefRepository.getActiveBeliefs(page, limit);
    return result;
  }

  /**
   * 更新信仰公告
   */
  async updateBeliefNotice(dto: UpdateBeliefNoticePayloadDto): Promise<XResult<any>> {
    this.logger.log(`更新信仰公告: ${dto.characterId}`);

    // 获取角色当前信仰
    const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
    if (XResultUtils.isFailure(beliefResult)) {
      return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
    }

    const belief = beliefResult.data;
    if (!belief) {
      return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
    }

    // 检查是否为董事长
    const isChairman = belief.leader.some(leader => leader.uid === dto.characterId && leader.pos === 1);
    if (!isChairman) {
      return XResultUtils.error('只有董事长可以更新信仰公告', 'PERMISSION_DENIED');
    }

    // 更新公告
    belief.notice = dto.notice;
    belief.addNotify('信仰公告已更新', 'system');

    const saveResult = await this.beliefRepository.updateById(belief._id, belief);
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
    }

    return XResultUtils.ok({
      beliefId: belief.beliefId,
      notice: dto.notice,
      updateTime: Date.now()
    });
  }

  /**
   * 设置信仰领导者
   * 基于old项目: setBeliefLeader方法
   */
  async setBeliefLeader(dto: SetBeliefLeaderPayloadDto): Promise<XResult<any>> {
    this.logger.log(`设置信仰领导者: ${dto.characterId} -> ${dto.targetPlayerId}, 职位: ${dto.position}`);

    try {
      // 1. 获取操作者角色信息
      const operatorResult = await this.characterService.getCharacterInfo(dto.characterId, dto.serverId);
      if (XResultUtils.isFailure(operatorResult)) {
        return XResultUtils.error(`获取操作者信息失败: ${operatorResult.message}`, operatorResult.code);
      }

      // 2. 获取目标角色信息
      const targetResult = await this.characterService.getCharacterInfo(dto.targetPlayerId, dto.serverId);
      if (XResultUtils.isFailure(targetResult)) {
        return XResultUtils.error(`获取目标角色信息失败: ${targetResult.message}`, targetResult.code);
      }

      // 3. 获取信仰信息
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 4. 检查操作权限（只有董事长可以设置领导者）
      const operator = belief.playerList.find(p => p.playerId === dto.characterId);
      if (!operator || operator.position !== 1) {
        return XResultUtils.error('只有董事长可以设置领导者', 'PERMISSION_DENIED');
      }

      // 5. 检查目标是否在同一信仰
      const target = belief.playerList.find(p => p.playerId === dto.targetPlayerId);
      if (!target) {
        return XResultUtils.error('目标角色不在同一信仰中', 'TARGET_NOT_IN_BELIEF');
      }

      // 6. 检查职位有效性（1-董事长，2-副董事长，3-理事，4-成员）
      if (dto.position < 1 || dto.position > 4) {
        return XResultUtils.error('无效的职位', 'INVALID_POSITION');
      }

      // 7. 如果是任命董事长，需要特殊处理
      if (dto.position === 1) {
        // 原董事长降为副董事长
        operator.position = 2;
        operator.positionName = '副董事长';
      }

      // 8. 更新目标角色职位
      const oldPosition = target.position;
      target.position = dto.position;
      target.positionName = this.getPositionName(dto.position);
      target.appointTime = Date.now();

      // 9. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 10. 添加动态通知
      const actionText = dto.isAppoint ? '任命' : '调整';
      belief.addNotify(
        `${operatorResult.data.name}${actionText}${targetResult.data.name}为${target.positionName}`,
        'leadership'
      );
      await belief.save();

      return XResultUtils.ok({
        targetPlayerId: dto.targetPlayerId,
        targetPlayerName: targetResult.data.name,
        oldPosition,
        newPosition: dto.position,
        newPositionName: target.positionName,
        appointTime: target.appointTime
      });

    } catch (error) {
      this.logger.error(`设置信仰领导者失败: ${error.message}`, error.stack);
      return XResultUtils.error(`设置信仰领导者失败: ${error.message}`, 'SET_BELIEF_LEADER_ERROR');
    }
  }

  /**
   * 获取信仰成员列表
   * 基于old项目: getBeliefMembers方法
   */
  async getBeliefMembers(dto: GetBeliefMembersPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰成员列表: ${dto.characterId}, 信仰ID: ${dto.beliefId}`);

    try {
      // 1. 获取信仰信息
      let belief;
      if (dto.beliefId) {
        // 通过信仰ID获取
        const beliefResult = await this.beliefRepository.findByBeliefId(dto.beliefId);
        if (XResultUtils.isFailure(beliefResult)) {
          return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
        }
        belief = beliefResult.data;
      } else {
        // 通过角色ID获取其所在信仰
        const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
        if (XResultUtils.isFailure(beliefResult)) {
          return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
        }
        belief = beliefResult.data;
      }

      if (!belief) {
        return XResultUtils.error('信仰不存在', 'BELIEF_NOT_FOUND');
      }

      // 2. 分页处理
      const page = dto.page || 1;
      const limit = dto.limit || 20;
      const skip = (page - 1) * limit;

      // 3. 排序处理
      let sortedMembers = [...belief.playerList];
      const sortBy = dto.sortBy || 'position';
      const sortOrder = dto.sortOrder || 'asc';

      sortedMembers.sort((a, b) => {
        let aValue, bValue;

        switch (sortBy) {
          case 'position':
            aValue = a.position;
            bValue = b.position;
            break;
          case 'contribution':
            aValue = a.contribution || 0;
            bValue = b.contribution || 0;
            break;
          case 'joinTime':
            aValue = a.joinTime;
            bValue = b.joinTime;
            break;
          case 'lastActiveTime':
            aValue = a.lastActiveTime || 0;
            bValue = b.lastActiveTime || 0;
            break;
          default:
            aValue = a.position;
            bValue = b.position;
        }

        if (sortOrder === 'desc') {
          return bValue - aValue;
        } else {
          return aValue - bValue;
        }
      });

      // 4. 分页截取
      const totalCount = sortedMembers.length;
      const members = sortedMembers.slice(skip, skip + limit);

      // 5. 获取成员详细信息
      const memberDetails = await Promise.all(
        members.map(async (member) => {
          try {
            const characterResult = await this.characterService.getCharacterInfo(member.playerId, dto.serverId);
            const characterInfo = XResultUtils.isSuccess(characterResult) ? characterResult.data : null;

            return {
              playerId: member.playerId,
              playerName: characterInfo?.name || '未知',
              faceUrl: characterInfo?.faceUrl || '',
              level: characterInfo?.level || 1,
              position: member.position,
              positionName: member.positionName || this.getPositionName(member.position),
              contribution: member.contribution || 0,
              joinTime: member.joinTime,
              lastActiveTime: member.lastActiveTime || 0,
              isOnline: this.isPlayerOnline(member.playerId), // TODO: 实现在线状态检查
              teamValue: characterInfo?.teamValue || 0
            };
          } catch (error) {
            this.logger.warn(`获取成员信息失败: ${member.playerId}, ${error.message}`);
            return {
              playerId: member.playerId,
              playerName: '未知',
              faceUrl: '',
              level: 1,
              position: member.position,
              positionName: member.positionName || this.getPositionName(member.position),
              contribution: member.contribution || 0,
              joinTime: member.joinTime,
              lastActiveTime: member.lastActiveTime || 0,
              isOnline: false,
              teamValue: 0
            };
          }
        })
      );

      return XResultUtils.ok({
        beliefId: belief.beliefId,
        beliefName: belief.name,
        members: memberDetails,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        },
        statistics: {
          totalMembers: totalCount,
          onlineMembers: memberDetails.filter(m => m.isOnline).length,
          positionCounts: this.calculatePositionCounts(sortedMembers)
        }
      });

    } catch (error) {
      this.logger.error(`获取信仰成员列表失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰成员列表失败: ${error.message}`, 'GET_BELIEF_MEMBERS_ERROR');
    }
  }

  /**
   * 获取信仰排行
   */
  async getBeliefRank(dto: GetBeliefRankPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰排行: ${dto.characterId}, 类型: ${dto.rankType}`);

    try {
      const page = dto.page || 1;
      const limit = dto.limit || 50;
      const skip = (page - 1) * limit;

      let rankings: any[] = [];
      let totalCount = 0;

      switch (dto.rankType) {
        case 'level':
          // 信仰等级排行
          const levelRankResult = await this.beliefRepository.getRankingByLevel(skip, limit);
          if (XResultUtils.isSuccess(levelRankResult)) {
            rankings = levelRankResult.data.map((belief: any, index: number) => ({
              rank: skip + index + 1,
              beliefId: belief.beliefId,
              beliefName: belief.name,
              level: belief.level,
              experience: belief.beliefExp || 0,
              memberCount: belief.playerList?.length || 0,
              chairmanName: this.getChairmanName(belief.playerList),
              createdTime: belief.createdTime
            }));
            totalCount = levelRankResult.total || 0;
          }
          break;

        case 'activity':
          // 信仰活跃度排行
          const activityRankResult = await this.beliefRepository.getRankingByActivity(skip, limit);
          if (XResultUtils.isSuccess(activityRankResult)) {
            rankings = activityRankResult.data.map((belief: any, index: number) => ({
              rank: skip + index + 1,
              beliefId: belief.beliefId,
              beliefName: belief.name,
              level: belief.level,
              todayActivity: belief.todayActivity || 0,
              weekActivity: belief.weekActivity || 0,
              totalActivity: belief.totalActivity || 0,
              memberCount: belief.playerList?.length || 0,
              chairmanName: this.getChairmanName(belief.playerList)
            }));
            totalCount = activityRankResult.total || 0;
          }
          break;

        case 'contribution':
          // 个人贡献排行（需要指定信仰）
          const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
          if (XResultUtils.isFailure(beliefResult) || !beliefResult.data) {
            return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
          }

          const belief = beliefResult.data;
          const sortedMembers = [...belief.playerList].sort((a, b) => (b.contribution || 0) - (a.contribution || 0));

          const startIndex = skip;
          const endIndex = Math.min(skip + limit, sortedMembers.length);
          const memberRankings = sortedMembers.slice(startIndex, endIndex);

          // 获取成员详细信息
          rankings = await Promise.all(
            memberRankings.map(async (member: any, index: number) => {
              try {
                const characterResult = await this.characterService.getCharacterInfo(member.playerId, dto.serverId);
                const characterInfo = XResultUtils.isSuccess(characterResult) ? characterResult.data : null;

                return {
                  rank: startIndex + index + 1,
                  playerId: member.playerId,
                  playerName: characterInfo?.name || '未知',
                  faceUrl: characterInfo?.faceUrl || '',
                  level: characterInfo?.level || 1,
                  contribution: member.contribution || 0,
                  weekContribution: member.weekContribution || 0,
                  position: member.position,
                  positionName: member.positionName || this.getPositionName(member.position),
                  joinTime: member.joinTime,
                  lastActiveTime: member.lastActiveTime || 0
                };
              } catch (error) {
                return {
                  rank: startIndex + index + 1,
                  playerId: member.playerId,
                  playerName: '未知',
                  faceUrl: '',
                  level: 1,
                  contribution: member.contribution || 0,
                  weekContribution: member.weekContribution || 0,
                  position: member.position,
                  positionName: member.positionName || this.getPositionName(member.position),
                  joinTime: member.joinTime,
                  lastActiveTime: member.lastActiveTime || 0
                };
              }
            })
          );

          totalCount = sortedMembers.length;
          break;

        default:
          // 默认使用等级排行
          const defaultResult = await this.beliefRepository.getBeliefRanking(limit);
          if (XResultUtils.isSuccess(defaultResult)) {
            rankings = defaultResult.data;
            totalCount = defaultResult.total || 0;
          }
          break;
      }

      return XResultUtils.ok({
        rankType: dto.rankType || 'level',
        rankings,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        },
        updateTime: Date.now()
      });

    } catch (error) {
      this.logger.error(`获取信仰排行失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰排行失败: ${error.message}`, 'GET_BELIEF_RANK_ERROR');
    }
  }

  /**
   * 获取我的信仰信息
   */
  async getMyBelief(dto: GetMyBeliefPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取我的信仰信息: ${dto.characterId}`);

    const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
    if (XResultUtils.isFailure(beliefResult)) {
      return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
    }

    const belief = beliefResult.data;
    if (!belief) {
      return XResultUtils.ok(null); // 未加入信仰
    }

    // 获取我在信仰中的信息
    const myMember = belief.playerList.find(m => m.playerId === dto.characterId);
    const myLeader = belief.leader.find(l => l.uid === dto.characterId);

    return XResultUtils.ok({
      beliefId: belief.beliefId,
      name: belief.name,
      notice: belief.notice,
      level: belief.level,
      memberCount: belief.playerList.length,
      myContribution: myMember?.contribution || 0,
      myPosition: myLeader?.pos || 0,
      joinTime: myMember?.joinTime || 0
    });
  }

  /**
   * 搜索信仰
   */
  async searchBelief(dto: SearchBeliefPayloadDto): Promise<XResult<any>> {
    this.logger.log(`搜索信仰: ${dto.keyword}`);

    const result = await this.beliefRepository.searchByName(dto.keyword, 20);
    return result;
  }

  /**
   * 增加信仰活跃度
   * 基于old项目: addBeliefLiveness方法
   *
   * @param playerId 玩家ID
   * @param beliefId 信仰ID
   * @param playerName 玩家名称
   * @param faceUrl 头像URL
   * @param value 活跃度数值
   * @param type 类型：1-任务活跃度，2-捐球币活跃度
   * @param goldNum 球币数量（type=2时使用）
   */
  async addBeliefLiveness(
    playerId: string,
    beliefId: number,
    playerName: string,
    faceUrl: string,
    value: number,
    type: 1 | 2,
    goldNum?: number
  ): Promise<XResult<{ level: number; beliefExp: number; levelUp: boolean }>> {
    this.logger.log(`增加信仰活跃度: 玩家${playerId}, 信仰${beliefId}, 活跃度${value}, 类型${type}`);

    try {
      // 1. 获取信仰信息
      const beliefResult = await this.beliefRepository.findByBeliefId(beliefId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('信仰不存在', 'BELIEF_NOT_FOUND');
      }

      // 2. 增加信仰活跃度和经验
      const originalLevel = belief.level;
      const addExpResult = belief.addExperience(value);

      // 3. 球币捐献时增加今日捐献统计
      if (type === 2 && goldNum) {
        belief.todayGoldNum = (belief.todayGoldNum || 0) + goldNum;
      }

      // 4. 更新活跃度排行（TODO: 实现排行榜逻辑）
      // this.addActiveRankInfo(playerId, beliefId, playerName, faceUrl, value);

      // 5. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 6. 如果信仰升级，发送广播通知
      if (addExpResult.levelUp) {
        await this.broadcastBeliefLevelUp(beliefId, belief.name, addExpResult.newLevel);
      }

      return XResultUtils.ok({
        level: addExpResult.newLevel,
        beliefExp: belief.beliefExp,
        levelUp: addExpResult.levelUp
      });

    } catch (error) {
      this.logger.error(`增加信仰活跃度失败: ${error.message}`, error.stack);
      return XResultUtils.error(`增加信仰活跃度失败: ${error.message}`, 'ADD_BELIEF_LIVENESS_ERROR');
    }
  }

  /**
   * 邮件增加信仰活跃度
   * 基于old项目: mailAddBeliefLiveness方法
   */
  async mailAddBeliefLiveness(beliefId: number, value: number): Promise<XResult<{ level: number; beliefExp: number; levelUp: boolean }>> {
    this.logger.log(`邮件增加信仰活跃度: 信仰${beliefId}, 活跃度${value}`);

    try {
      // 1. 获取信仰信息
      const beliefResult = await this.beliefRepository.findByBeliefId(beliefId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('信仰不存在', 'BELIEF_NOT_FOUND');
      }

      // 2. 增加信仰经验
      const addExpResult = belief.addExperience(value);

      // 3. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 4. 如果信仰升级，发送广播通知
      if (addExpResult.levelUp) {
        await this.broadcastBeliefLevelUp(beliefId, belief.name, addExpResult.newLevel);
      }

      return XResultUtils.ok({
        level: addExpResult.newLevel,
        beliefExp: belief.beliefExp,
        levelUp: addExpResult.levelUp
      });

    } catch (error) {
      this.logger.error(`邮件增加信仰活跃度失败: ${error.message}`, error.stack);
      return XResultUtils.error(`邮件增加信仰活跃度失败: ${error.message}`, 'MAIL_ADD_BELIEF_LIVENESS_ERROR');
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 广播信仰升级消息
   * 基于old项目: 信仰升级广播机制
   */
  private async broadcastBeliefLevelUp(beliefId: number, beliefName: string, newLevel: number): Promise<void> {
    try {
      const message = `恭喜${beliefName}信仰将信仰等级提升到${newLevel}级`;

      // TODO: 实现广播消息发送
      // 这里需要调用聊天服务或消息服务来发送全服广播
      this.logger.log(`信仰升级广播: ${message}`);

      // 可以通过事件系统或微服务调用来实现
      // await this.eventEmitter.emit('belief.levelUp', { beliefId, beliefName, newLevel, message });

    } catch (error) {
      this.logger.error(`发送信仰升级广播失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取信仰统计信息
   */
  async getBeliefStats(dto: GetBeliefStatsPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰统计信息: ${dto.characterId}`);

    const result = await this.beliefRepository.getBeliefStats();
    return result;
  }

  /**
   * 踢出信仰成员
   */
  async kickBeliefMember(dto: KickBeliefMemberPayloadDto): Promise<XResult<any>> {
    this.logger.log(`踢出信仰成员: ${dto.characterId} -> ${dto.targetPlayerId}`);

    // TODO: 实现踢出成员逻辑
    return XResultUtils.error('功能暂未实现', 'NOT_IMPLEMENTED');
  }

  /**
   * 解散信仰
   */
  async disbandBelief(dto: DisbandBeliefPayloadDto): Promise<XResult<any>> {
    this.logger.log(`解散信仰: ${dto.characterId}`);

    // TODO: 实现解散信仰逻辑
    return XResultUtils.error('功能暂未实现', 'NOT_IMPLEMENTED');
  }

  /**
   * 转让董事长
   */
  async transferChairman(dto: TransferChairmanPayloadDto): Promise<XResult<any>> {
    this.logger.log(`转让董事长: ${dto.characterId} -> ${dto.targetPlayerId}`);

    // TODO: 实现转让董事长逻辑
    return XResultUtils.error('功能暂未实现', 'NOT_IMPLEMENTED');
  }

  /**
   * 获取信仰动态
   */
  async getBeliefNotifications(dto: GetBeliefNotificationsPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰动态: ${dto.characterId}`);

    // TODO: 实现获取信仰动态逻辑
    return XResultUtils.error('功能暂未实现', 'NOT_IMPLEMENTED');
  }
}
