import { Injectable, Logger, forwardRef, Inject } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { GameConfigFacade } from '@libs/game-config';
import { BeliefRepository } from './repositories/belief.repository';
import { CharacterService } from '../character/character.service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import {
  CreateBeliefPayloadDto,
  JoinBeliefPayloadDto,
  LeaveBeliefPayloadDto,
  DonateBeliefPayloadDto,
  UpdateBeliefNoticePayloadDto,
  GetBeliefInfoPayloadDto,
  GetBeliefListPayloadDto,
  SetBeliefLeaderPayloadDto,
  GetBeliefMembersPayloadDto,
  GetBeliefRankPayloadDto
} from '../../common/dto/belief-payload.dto';

/**
 * 信仰系统业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 严格基于old项目的信仰系统业务逻辑
 *
 * 🎯 核心功能：
 * - 信仰创建和管理
 * - 信仰成员管理（加入、退出）
 * - 信仰捐献系统（欧元、球币）
 * - 信仰等级和经验管理
 * - 信仰领导者管理
 * - 信仰排行和统计
 *
 * 🚀 架构优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的Result模式错误处理
 * - 与CharacterService紧密集成，保证数据一致性
 * - 事件驱动的跨服务通信
 *
 * 🔗 服务依赖：
 * - CharacterService：角色信息、资源检查（直接注入）
 * - BeliefRepository：信仰数据持久化（直接注入）
 * - GameConfigFacade：配置数据获取（直接注入）
 *
 * old项目核心逻辑：
 * - 信仰捐献：donateBeliefGold(type, num)
 * - 信仰升级：addBeliefLiveness(value)
 * - 成员管理：playerList数组操作
 * - 领导者管理：leader数组操作
 */
@Injectable()
export class BeliefService extends BaseService {
  constructor(
    private readonly beliefRepository: BeliefRepository,
    private readonly gameConfig: GameConfigFacade,
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
  ) {
    super('BeliefService');
  }

  /**
   * 创建信仰
   * 基于old项目: 创建新的信仰组织
   */
  async createBelief(dto: CreateBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`创建信仰: ${dto.characterId}, 名称: ${dto.name}`);

      // 1. 检查角色是否已有信仰
      const existingBeliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(existingBeliefResult)) {
        return XResultUtils.error(`检查现有信仰失败: ${existingBeliefResult.message}`, existingBeliefResult.code);
      }

      if (existingBeliefResult.data) {
        return XResultUtils.error('角色已加入信仰，无法创建新信仰', 'ALREADY_IN_BELIEF');
      }

      // 2. 检查信仰名称是否可用
      const nameAvailableResult = await this.beliefRepository.isNameAvailable(dto.name);
      if (XResultUtils.isFailure(nameAvailableResult)) {
        return XResultUtils.error(`检查名称可用性失败: ${nameAvailableResult.message}`, nameAvailableResult.code);
      }

      if (!nameAvailableResult.data) {
        return XResultUtils.error('信仰名称已被使用', 'BELIEF_NAME_TAKEN');
      }

      // 3. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 4. 检查创建费用（基于old项目逻辑）
      const createCost = await this.getBeliefCreateCost();
      if (character.cash < createCost) {
        return XResultUtils.error('现金不足，无法创建信仰', 'INSUFFICIENT_CASH');
      }

      // 5. 扣除创建费用
      const deductResult = await this.characterService.deductCurrency({
        characterId: dto.characterId,
        serverId: dto.serverId,
        currencyType: 'cash',
        amount: createCost,
        reason: 'create_belief'
      });

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除费用失败: ${deductResult.message}`, deductResult.code);
      }

      // 6. 获取下一个信仰ID
      const nextIdResult = await this.beliefRepository.getNextBeliefId();
      if (XResultUtils.isFailure(nextIdResult)) {
        return XResultUtils.error(`获取信仰ID失败: ${nextIdResult.message}`, nextIdResult.code);
      }

      const beliefId = nextIdResult.data;

      // 7. 创建信仰实体
      const beliefData = {
        beliefId,
        name: dto.name,
        notice: dto.notice || '',
        creatorId: dto.characterId,
        leader: [{
          uid: dto.characterId,
          gid: '', // 预留字段
          name: character.characterName,
          faceUrl: character.avatar || '',
          pos: 1, // 董事长
          appointTime: Date.now()
        }],
        playerList: [{
          playerId: dto.characterId,
          playerName: character.characterName,
          faceUrl: character.avatar || '',
          contribution: 0,
          weekContribution: 0,
          joinTime: Date.now(),
          lastActiveTime: Date.now(),
          isActive: true
        }],
        level: 1,
        beliefExp: 0,
        beliefGold: 0,
        status: 'active',
        createTime: Date.now()
      };

      const createResult = await this.beliefRepository.create(beliefData);
      if (XResultUtils.isFailure(createResult)) {
        // 创建失败，退还费用
        await this.characterService.addCurrency({
          characterId: dto.characterId,
          currencyType: 'cash',
          amount: createCost,
          reason: 'refund_belief_creation'
        });
        return XResultUtils.error(`创建信仰失败: ${createResult.message}`, createResult.code);
      }

      const belief = createResult.data;

      // 8. 更新角色的信仰信息
      await this.characterService.updateCharacter(dto.characterId, {
        beliefId: beliefId,
        beliefNum: 0 // 初始信仰值
      });

      // 9. 添加创建动态
      belief.addNotify(`信仰"${dto.name}"成立，${character.characterName}成为首任董事长`, 'system');
      await belief.save();

      // 10. 触发信仰创建事件（通知其他服务）
      await this.notifyBeliefCreated(beliefId, dto.characterId);

      return XResultUtils.ok({
        beliefId,
        name: dto.name,
        level: 1,
        memberCount: 1,
        leaderPosition: 1,
        createCost
      });
    }, { reason: 'create_belief', metadata: { characterId: dto.characterId, name: dto.name } });
  }

  /**
   * 加入信仰
   * 基于old项目: 玩家申请加入信仰
   */
  async joinBelief(dto: JoinBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`加入信仰: ${dto.characterId}, 信仰ID: ${dto.beliefId}`);

      // 1. 检查角色是否已有信仰
      const existingBeliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(existingBeliefResult)) {
        return XResultUtils.error(`检查现有信仰失败: ${existingBeliefResult.message}`, existingBeliefResult.code);
      }

      if (existingBeliefResult.data) {
        return XResultUtils.error('角色已加入其他信仰', 'ALREADY_IN_BELIEF');
      }

      // 2. 获取目标信仰
      const beliefResult = await this.beliefRepository.findByBeliefId(dto.beliefId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief || belief.status !== 'active') {
        return XResultUtils.error('信仰不存在或已解散', 'BELIEF_NOT_FOUND');
      }

      // 3. 检查信仰是否已满
      if (belief.playerList.length >= belief.maxMembers) {
        return XResultUtils.error('信仰成员已满', 'BELIEF_FULL');
      }

      // 4. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 5. 添加成员到信仰
      try {
        belief.addMember({
          playerId: dto.characterId,
          playerName: character.characterName,
          faceUrl: character.avatar || '',
          contribution: 0,
          weekContribution: 0
        });

        // 6. 添加加入动态
        belief.addNotify(`${character.characterName}加入了信仰`, 'player', dto.characterId);

        // 7. 保存信仰数据
        const saveResult = await this.beliefRepository.updateById(belief._id, belief);
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
        }

        // 8. 更新角色的信仰信息
        await this.characterService.updateCharacter(dto.characterId, {
          beliefId: dto.beliefId,
          beliefNum: 0
        });

        // 9. 触发加入信仰事件
        await this.notifyBeliefJoined(dto.beliefId, dto.characterId);

        return XResultUtils.ok({
          beliefId: dto.beliefId,
          beliefName: belief.name,
          memberCount: belief.playerList.length,
          joinTime: Date.now()
        });

      } catch (error) {
        return XResultUtils.error(`加入信仰失败: ${error.message}`, 'JOIN_BELIEF_ERROR');
      }
    }, { reason: 'join_belief', metadata: { characterId: dto.characterId, beliefId: dto.beliefId } });
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 获取信仰创建费用
   */
  private async getBeliefCreateCost(): Promise<number> {
    try {
      // 从SystemParam配置表获取创建费用
      const config = await this.gameConfig.systemParam.get(201); // BELIEF_CREATE_COST
      return config ? config.parameter : 10000; // 默认10000现金
    } catch (error) {
      this.logger.warn('获取信仰创建费用配置失败，使用默认值', error);
      return 10000;
    }
  }

  /**
   * 通知信仰创建事件
   */
  private async notifyBeliefCreated(beliefId: number, creatorId: string): Promise<void> {
    try {
      // TODO: 发送事件到其他服务
      // this.eventEmitter.emit('belief.created', { beliefId, creatorId });
      this.logger.log(`信仰创建事件: ${beliefId}, 创建者: ${creatorId}`);
    } catch (error) {
      this.logger.error('通知信仰创建事件失败', error);
    }
  }

  /**
   * 通知加入信仰事件
   */
  private async notifyBeliefJoined(beliefId: number, playerId: string): Promise<void> {
    try {
      // TODO: 发送事件到其他服务
      // this.eventEmitter.emit('belief.joined', { beliefId, playerId });
      this.logger.log(`加入信仰事件: ${beliefId}, 玩家: ${playerId}`);
    } catch (error) {
      this.logger.error('通知加入信仰事件失败', error);
    }
  }

  /**
   * 退出信仰
   * 基于old项目: 玩家主动退出信仰
   */
  async leaveBelief(dto: LeaveBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`退出信仰: ${dto.characterId}`);

      // 1. 获取角色当前信仰
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 检查是否为董事长
      const isChairman = belief.leader.some(leader => leader.uid === dto.characterId && leader.pos === 1);
      if (isChairman && belief.playerList.length > 1) {
        return XResultUtils.error('董事长不能直接退出，请先转让职位或解散信仰', 'CHAIRMAN_CANNOT_LEAVE');
      }

      // 3. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 4. 从信仰中移除成员
      try {
        belief.removeMember(dto.characterId);

        // 5. 移除领导职位（如果有）
        belief.leader = belief.leader.filter(leader => leader.uid !== dto.characterId);

        // 6. 添加退出动态
        belief.addNotify(`${character.characterName}退出了信仰`, 'player', dto.characterId);

        // 7. 如果信仰没有成员了，解散信仰
        if (belief.playerList.length === 0) {
          belief.status = 'disbanded';
          belief.addNotify('信仰因无成员而自动解散', 'system');
        }

        // 8. 保存信仰数据
        const saveResult = await this.beliefRepository.updateById(belief._id, belief);
        if (XResultUtils.isFailure(saveResult)) {
          return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
        }

        // 9. 清除角色的信仰信息
        await this.characterService.updateCharacter(dto.characterId, {
          beliefId: 0,
          beliefNum: 0
        });

        // 10. 触发退出信仰事件
        await this.notifyBeliefLeft(belief.beliefId, dto.characterId);

        return XResultUtils.ok({
          beliefId: belief.beliefId,
          beliefName: belief.name,
          leftTime: Date.now(),
          isDisbanded: belief.status === 'disbanded'
        });

      } catch (error) {
        return XResultUtils.error(`退出信仰失败: ${error.message}`, 'LEAVE_BELIEF_ERROR');
      }
    }, { reason: 'leave_belief', metadata: { characterId: dto.characterId } });
  }

  /**
   * 信仰捐献
   * 基于old项目: donateBeliefGold(type, num)
   */
  async donateBelief(dto: DonateBeliefPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`信仰捐献: ${dto.characterId}, 类型: ${dto.type}, 数量: ${dto.amount}`);

      // 1. 获取角色当前信仰
      const beliefResult = await this.beliefRepository.findByPlayerId(dto.characterId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief) {
        return XResultUtils.error('角色未加入任何信仰', 'NOT_IN_BELIEF');
      }

      // 2. 获取角色信息
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;

      // 3. 计算捐献获得的信仰资金和经验
      const donationResult = await this.calculateDonation(dto.type, dto.amount);
      if (XResultUtils.isFailure(donationResult)) {
        return XResultUtils.error(`计算捐献失败: ${donationResult.message}`, donationResult.code);
      }

      const { beliefGoldGain, expGain, costAmount, currencyType } = donationResult.data;

      // 4. 检查角色资源是否足够
      const currentAmount = character[currencyType] || 0;
      if (currentAmount < costAmount) {
        const errorMsg = currencyType === 'cash' ? '现金不足' : '金币不足';
        return XResultUtils.error(errorMsg, 'INSUFFICIENT_CURRENCY');
      }

      // 5. 扣除角色资源
      const deductResult = await this.characterService.subtractCurrency({
        characterId: dto.characterId,
        currencyType,
        amount: costAmount,
        reason: 'belief_donation'
      });

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除资源失败: ${deductResult.message}`, deductResult.code);
      }

      // 6. 更新信仰资金和经验
      belief.beliefGold += beliefGoldGain;
      belief.totalDonation += beliefGoldGain;

      // 7. 更新信仰经验和等级
      const levelUpResult = belief.addExperience(expGain);

      // 8. 更新成员贡献
      const member = belief.playerList.find(m => m.playerId === dto.characterId);
      if (member) {
        member.contribution += beliefGoldGain;
        member.weekContribution += beliefGoldGain;
        member.lastActiveTime = Date.now();
        member.isActive = true;
      }

      // 9. 更新当日捐献记录（球币捐献）
      if (dto.type === 2) {
        belief.todayGoldNum += dto.amount;
      }

      // 10. 添加捐献动态
      const currencyName = currencyType === 'cash' ? '现金' : '金币';
      belief.addNotify(
        `${character.characterName}捐献了${costAmount}${currencyName}，信仰获得${beliefGoldGain}资金`,
        'player',
        dto.characterId
      );

      // 11. 如果升级了，添加升级动态
      if (levelUpResult.levelUp) {
        belief.addNotify(
          `信仰升级到${levelUpResult.newLevel}级！`,
          'system'
        );
      }

      // 12. 保存信仰数据
      const saveResult = await this.beliefRepository.updateById(belief._id, belief);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存信仰数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 13. 触发捐献事件
      await this.notifyBeliefDonation(belief.beliefId, dto.characterId, beliefGoldGain);

      return XResultUtils.ok({
        beliefGoldGain,
        expGain,
        costAmount,
        currencyType,
        newBeliefGold: belief.beliefGold,
        newLevel: belief.level,
        levelUp: levelUpResult.levelUp,
        memberContribution: member?.contribution || 0
      });
    }, { reason: 'donate_belief', metadata: { characterId: dto.characterId, type: dto.type, amount: dto.amount } });
  }

  // ==================== 私有辅助方法（续） ====================

  /**
   * 计算捐献收益
   * 基于old项目逻辑
   */
  private async calculateDonation(type: number, amount: number): Promise<XResult<{
    beliefGoldGain: number;
    expGain: number;
    costAmount: number;
    currencyType: string;
  }>> {
    try {
      let beliefGoldGain = 0;
      let costAmount = 0;
      let currencyType = '';

      if (type === 1) {
        // 现金捐献
        currencyType = 'cash';
        costAmount = amount;

        // 从配置获取现金转信仰资金比例
        const config = await this.gameConfig.systemParam.get(202); // CASH_BELIEF_GOLD_RATIO
        const ratio = config ? config.parameter : 100;
        beliefGoldGain = amount * ratio;
      } else {
        // 球币捐献
        currencyType = 'gold';
        costAmount = amount;

        // 从配置获取球币转信仰资金比例
        const config = await this.gameConfig.systemParam.get(203); // GOLD_BELIEF_GOLD_RATIO
        const ratio = config ? config.parameter : 1000;
        beliefGoldGain = amount * ratio;
      }

      // 经验获得 = 信仰资金获得 / 10
      const expGain = Math.floor(beliefGoldGain / 10);

      return XResultUtils.ok({
        beliefGoldGain,
        expGain,
        costAmount,
        currencyType
      });
    } catch (error) {
      return XResultUtils.error(`计算捐献收益失败: ${error.message}`, 'CALCULATE_DONATION_ERROR');
    }
  }

  /**
   * 通知退出信仰事件
   */
  private async notifyBeliefLeft(beliefId: number, playerId: string): Promise<void> {
    try {
      this.logger.log(`退出信仰事件: ${beliefId}, 玩家: ${playerId}`);
    } catch (error) {
      this.logger.error('通知退出信仰事件失败', error);
    }
  }

  /**
   * 通知信仰捐献事件
   */
  private async notifyBeliefDonation(beliefId: number, playerId: string, amount: number): Promise<void> {
    try {
      this.logger.log(`信仰捐献事件: ${beliefId}, 玩家: ${playerId}, 金额: ${amount}`);
    } catch (error) {
      this.logger.error('通知信仰捐献事件失败', error);
    }
  }
}
