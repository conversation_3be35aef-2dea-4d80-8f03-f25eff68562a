import { Injectable } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { BeliefRepository } from '../../common/repositories/belief.repository';
import { XResult, XResultUtils } from '@libs/common/types';
import { Belief } from './schemas/belief.schema';
import { DonateBeliefPayloadDto, UpgradeBeliefSkillPayloadDto } from './dto/belief-payload.dto';
import { GameConfigFacade } from '@libs/game-config';

@Injectable()
export class BeliefService extends BaseService {
  constructor(
    private readonly beliefRepository: BeliefRepository,
    private readonly gameConfigFacade: GameConfigFacade,
  ) {
    super('BeliefService');
  }

  /**
   * @description 获取信仰信息
   * @param beliefId 信仰ID
   */
  async getBeliefInfo(beliefId: number): Promise<XResult<Belief | null>> {
    return this.beliefRepository.findOne({ beliefId });
  }

  /**
   * @description 信仰捐赠
   * @param dto 捐赠数据
   */
  async donateBeliefGold(dto: DonateBeliefPayloadDto): Promise<XResult<Belief>> {
    return this.executeBusinessOperation(async () => {
      const { beliefId, type, num } = dto;

      const cashMultiplierConfig = await this.gameConfigFacade.systemParam.get(1127);
      const goldMultiplierConfig = await this.gameConfigFacade.systemParam.get(1118);

      const cashMultiplier = cashMultiplierConfig?.parameter || 100;
      const goldMultiplier = goldMultiplierConfig?.parameter || 1000;

      let amountToAdd = 0;
      if (type === 1) {
        amountToAdd = cashMultiplier * num;
      } else if (type === 2) {
        amountToAdd = goldMultiplier * num;
      } else {
        return XResultUtils.error('无效的捐赠类型', 'INVALID_PARAMETER');
      }

      // TODO: 扣除玩家货币的逻辑需要调用Economy服务，此处暂时省略

      const updateResult = await this.beliefRepository.addBeliefGold(beliefId, amountToAdd);

      if (XResultUtils.isFailure(updateResult)) {
        return this.handleRepositoryResult(updateResult, '信仰捐赠失败');
      }

      if (!updateResult.data) {
        return XResultUtils.error('信仰不存在', 'BELIEF_NOT_FOUND');
      }

      return XResultUtils.ok(updateResult.data);
    });
  }

  /**
   * @description 升级信仰技能
   * @param dto 技能升级数据
   */
  async upgradeBeliefSkill(dto: UpgradeBeliefSkillPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // TODO: This logic needs to be fully implemented.
      // 1. Get Character data (including belief info and belief skills)
      // 2. Get Belief public data (for level to check if skill is unlocked)
      // 3. Get BeliefSkillExpend config for cost
      // 4. Check if character has enough beliefLiveness
      // 5. Deduct beliefLiveness from character
      // 6. Update skill level for the character
      // 7. Recalculate hero attributes

      this.logger.log(`Upgrading belief skill ${dto.skillId} for character ${dto.characterId}`);
      return XResultUtils.ok({ message: 'Skill upgrade logic not yet implemented.' });
    });
  }
}
