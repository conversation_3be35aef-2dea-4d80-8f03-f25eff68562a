import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseRepository, RepositoryResultWrapper } from '@libs/common/repository';
import { Belief, BeliefDocument } from '../schemas/belief.schema';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 信仰数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式支持
 * 严格基于old项目的信仰数据操作需求
 */
@Injectable()
export class BeliefRepository extends BaseRepository<BeliefDocument> {
  constructor(
    @InjectModel(Belief.name) private beliefModel: Model<BeliefDocument>,
  ) {
    super(beliefModel, 'BeliefRepository');
  }

  /**
   * 根据信仰ID查找信仰
   */
  async findByBeliefId(beliefId: number): Promise<XResult<BeliefDocument | null>> {
    return this.executeQuery(async () => {
      return await this.beliefModel.findOne({ beliefId }).exec();
    });
  }

  /**
   * 根据信仰名称查找信仰
   */
  async findByName(name: string): Promise<XResult<BeliefDocument | null>> {
    return this.executeQuery(async () => {
      return await this.beliefModel.findOne({ name }).exec();
    });
  }

  /**
   * 查找玩家所属的信仰
   */
  async findByPlayerId(playerId: string): Promise<XResult<BeliefDocument | null>> {
    return this.executeQuery(async () => {
      return await this.beliefModel.findOne({
        'playerList.playerId': playerId,
        status: 'active'
      }).exec();
    });
  }

  /**
   * 获取信仰排行榜
   */
  async getBeliefRanking(limit: number = 100): Promise<XResult<BeliefDocument[]>> {
    return this.executeQuery(async () => {
      return await this.beliefModel
        .find({ status: 'active' })
        .sort({ level: -1, beliefExp: -1, createTime: 1 })
        .limit(limit)
        .exec();
    });
  }

  /**
   * 获取活跃信仰列表
   */
  async getActiveBeliefs(page: number = 1, limit: number = 20): Promise<XResult<{
    beliefs: BeliefDocument[];
    total: number;
    page: number;
    totalPages: number;
  }>> {
    return this.executeQuery(async () => {
      const skip = (page - 1) * limit;
      
      const [beliefs, total] = await Promise.all([
        this.beliefModel
          .find({ status: 'active' })
          .sort({ level: -1, memberCount: -1, createTime: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.beliefModel.countDocuments({ status: 'active' })
      ]);

      return {
        beliefs,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    });
  }

  /**
   * 搜索信仰（按名称）
   */
  async searchByName(keyword: string, limit: number = 20): Promise<XResult<BeliefDocument[]>> {
    return this.executeQuery(async () => {
      return await this.beliefModel
        .find({
          name: { $regex: keyword, $options: 'i' },
          status: 'active'
        })
        .sort({ level: -1, memberCount: -1 })
        .limit(limit)
        .exec();
    });
  }

  /**
   * 获取下一个可用的信仰ID
   */
  async getNextBeliefId(): Promise<XResult<number>> {
    return this.executeQuery(async () => {
      const lastBelief = await this.beliefModel
        .findOne({}, { beliefId: 1 })
        .sort({ beliefId: -1 })
        .exec();
      
      return lastBelief ? lastBelief.beliefId + 1 : 1;
    });
  }

  /**
   * 更新信仰排名
   */
  async updateBeliefRanking(): Promise<XResult<void>> {
    return this.executeQuery(async () => {
      const beliefs = await this.beliefModel
        .find({ status: 'active' })
        .sort({ level: -1, beliefExp: -1, createTime: 1 })
        .exec();

      const updatePromises = beliefs.map((belief, index) => {
        belief.beliefRank = index + 1;
        return belief.save();
      });

      await Promise.all(updatePromises);
    });
  }

  /**
   * 批量更新信仰经验
   */
  async batchUpdateExperience(updates: Array<{ beliefId: number; exp: number }>): Promise<XResult<void>> {
    return this.executeQuery(async () => {
      const updatePromises = updates.map(async ({ beliefId, exp }) => {
        return await this.beliefModel.updateOne(
          { beliefId },
          { $inc: { beliefExp: exp, weekExp: exp } }
        ).exec();
      });

      await Promise.all(updatePromises);
    });
  }

  /**
   * 清理过期的信仰动态
   */
  async cleanExpiredNotifications(daysToKeep: number = 30): Promise<XResult<number>> {
    return this.executeQuery(async () => {
      const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
      
      const result = await this.beliefModel.updateMany(
        {},
        {
          $pull: {
            notify: { time: { $lt: cutoffTime } }
          }
        }
      ).exec();

      return result.modifiedCount;
    });
  }

  /**
   * 获取信仰统计信息
   */
  async getBeliefStats(): Promise<XResult<{
    totalBeliefs: number;
    activeBeliefs: number;
    totalMembers: number;
    averageLevel: number;
    topLevelBeliefs: BeliefDocument[];
  }>> {
    return this.executeQuery(async () => {
      const [
        totalBeliefs,
        activeBeliefs,
        memberStats,
        topLevelBeliefs
      ] = await Promise.all([
        this.beliefModel.countDocuments(),
        this.beliefModel.countDocuments({ status: 'active' }),
        this.beliefModel.aggregate([
          { $match: { status: 'active' } },
          {
            $group: {
              _id: null,
              totalMembers: { $sum: { $size: '$playerList' } },
              averageLevel: { $avg: '$level' }
            }
          }
        ]),
        this.beliefModel
          .find({ status: 'active' })
          .sort({ level: -1, beliefExp: -1 })
          .limit(10)
          .exec()
      ]);

      const stats = memberStats[0] || { totalMembers: 0, averageLevel: 0 };

      return {
        totalBeliefs,
        activeBeliefs,
        totalMembers: stats.totalMembers,
        averageLevel: Math.round(stats.averageLevel * 100) / 100,
        topLevelBeliefs
      };
    });
  }

  /**
   * 软删除信仰（解散）
   */
  async disbandBelief(beliefId: number): Promise<XResult<BeliefDocument | null>> {
    return this.executeQuery(async () => {
      return await this.beliefModel.findOneAndUpdate(
        { beliefId },
        { 
          status: 'disbanded',
          playerList: [],
          leader: []
        },
        { new: true }
      ).exec();
    });
  }

  /**
   * 检查信仰名称是否可用
   */
  async isNameAvailable(name: string, excludeBeliefId?: number): Promise<XResult<boolean>> {
    return this.executeQuery(async () => {
      const query: any = { name, status: 'active' };
      if (excludeBeliefId) {
        query.beliefId = { $ne: excludeBeliefId };
      }

      const existing = await this.beliefModel.findOne(query).exec();
      return !existing;
    });
  }
}
