import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseRepository, RepositoryResultWrapper } from '@libs/common/repository';
import { Belief, BeliefDocument } from '../schemas/belief.schema';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 信仰数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式支持
 * 严格基于old项目的信仰数据操作需求
 */
@Injectable()
export class BeliefRepository extends BaseRepository<BeliefDocument> {
  constructor(
    @InjectModel(Belief.name) private beliefModel: Model<BeliefDocument>,
  ) {
    super(beliefModel, 'BeliefRepository');
  }

  /**
   * 根据信仰ID查找信仰
   */
  async findByBeliefId(beliefId: number): Promise<XResult<BeliefDocument | null>> {
    return this.findOne({ beliefId });
  }

  /**
   * 根据信仰名称查找信仰
   */
  async findByName(name: string): Promise<XResult<BeliefDocument | null>> {
    return this.findOne({ name });
  }

  /**
   * 查找玩家所属的信仰
   */
  async findByPlayerId(playerId: string): Promise<XResult<BeliefDocument | null>> {
    return this.findOne({
      'playerList.playerId': playerId,
      status: 'active'
    });
  }

  /**
   * 获取信仰排行榜
   */
  async getBeliefRanking(limit: number = 100): Promise<XResult<BeliefDocument[]>> {
    return this.findMany(
      { status: 'active' },
      {
        sort: { level: -1, beliefExp: -1, createTime: 1 },
        limit
      }
    );
  }

  /**
   * 获取活跃信仰列表
   */
  async getActiveBeliefs(page: number = 1, limit: number = 20): Promise<XResult<{
    beliefs: BeliefDocument[];
    total: number;
    page: number;
    totalPages: number;
  }>> {
    return RepositoryResultWrapper.wrap(async () => {
      const skip = (page - 1) * limit;

      const [beliefs, total] = await Promise.all([
        this.beliefModel
          .find({ status: 'active' })
          .sort({ level: -1, memberCount: -1, createTime: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.beliefModel.countDocuments({ status: 'active' })
      ]);

      return {
        beliefs,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    });
  }

  /**
   * 搜索信仰（按名称）
   */
  async searchByName(keyword: string, limit: number = 20): Promise<XResult<BeliefDocument[]>> {
    return this.findMany(
      {
        name: { $regex: keyword, $options: 'i' },
        status: 'active'
      },
      {
        sort: { level: -1, memberCount: -1 },
        limit
      }
    );
  }

  /**
   * 获取下一个可用的信仰ID
   */
  async getNextBeliefId(): Promise<XResult<number>> {
    return RepositoryResultWrapper.wrap(async () => {
      const lastBeliefResult = await this.findOneLean(
        {},
        {
          select: 'beliefId',
          sort: { beliefId: -1 }
        }
      );

      if (XResultUtils.isFailure(lastBeliefResult)) {
        throw new Error(`查询最后信仰ID失败: ${lastBeliefResult.message}`);
      }

      const lastBelief = lastBeliefResult.data;
      return lastBelief ? lastBelief.beliefId + 1 : 1;
    });
  }

  /**
   * 更新信仰排名
   */
  async updateBeliefRanking(): Promise<XResult<void>> {
    return RepositoryResultWrapper.wrap(async () => {
      const beliefsResult = await this.findMany(
        { status: 'active' },
        {
          sort: { level: -1, beliefExp: -1, createTime: 1 },
          lean: false // 需要完整文档以便调用save()
        }
      );

      if (XResultUtils.isFailure(beliefsResult)) {
        throw new Error(`查询信仰列表失败: ${beliefsResult.message}`);
      }

      const beliefs = beliefsResult.data;
      const updatePromises = beliefs.map((belief, index) => {
        belief.beliefRank = index + 1;
        return belief.save();
      });

      await Promise.all(updatePromises);
    });
  }

  /**
   * 批量更新信仰经验
   */
  async batchUpdateExperience(updates: Array<{ beliefId: number; exp: number }>): Promise<XResult<void>> {
    return RepositoryResultWrapper.wrap(async () => {
      const updatePromises = updates.map(async ({ beliefId, exp }) => {
        const incrementResult = await this.increment(
          { beliefId },
          { beliefExp: exp, weekExp: exp }
        );

        if (XResultUtils.isFailure(incrementResult)) {
          throw new Error(`更新信仰${beliefId}经验失败: ${incrementResult.message}`);
        }

        return incrementResult.data;
      });

      await Promise.all(updatePromises);
    });
  }

  /**
   * 清理过期的信仰动态
   */
  async cleanExpiredNotifications(daysToKeep: number = 30): Promise<XResult<number>> {
    return RepositoryResultWrapper.wrap(async () => {
      const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);

      // 使用BaseRepository的pullFromArray方法
      const beliefsResult = await this.findManyLean({ status: 'active' }, { select: '_id' });
      if (XResultUtils.isFailure(beliefsResult)) {
        throw new Error(`查询信仰列表失败: ${beliefsResult.message}`);
      }

      let modifiedCount = 0;
      const beliefs = beliefsResult.data;

      for (const belief of beliefs) {
        const pullResult = await this.pullFromArray(
          { _id: belief._id },
          'notify',
          { time: { $lt: cutoffTime } }
        );

        if (XResultUtils.isSuccess(pullResult) && pullResult.data) {
          modifiedCount++;
        }
      }

      return modifiedCount;
    });
  }

  /**
   * 获取信仰统计信息
   */
  async getBeliefStats(): Promise<XResult<{
    totalBeliefs: number;
    activeBeliefs: number;
    totalMembers: number;
    averageLevel: number;
    topLevelBeliefs: BeliefDocument[];
  }>> {
    return RepositoryResultWrapper.wrap(async () => {
      const [
        totalBeliefsResult,
        activeBeliefsResult,
        topLevelBeliefsResult
      ] = await Promise.all([
        this.count({}),
        this.count({ status: 'active' }),
        this.findMany(
          { status: 'active' },
          {
            sort: { level: -1, beliefExp: -1 },
            limit: 10
          }
        )
      ]);

      // 检查查询结果
      if (XResultUtils.isFailure(totalBeliefsResult)) {
        throw new Error(`查询总信仰数失败: ${totalBeliefsResult.message}`);
      }
      if (XResultUtils.isFailure(activeBeliefsResult)) {
        throw new Error(`查询活跃信仰数失败: ${activeBeliefsResult.message}`);
      }
      if (XResultUtils.isFailure(topLevelBeliefsResult)) {
        throw new Error(`查询顶级信仰失败: ${topLevelBeliefsResult.message}`);
      }

      // 使用聚合查询计算成员统计
      const memberStats = await this.beliefModel.aggregate([
        { $match: { status: 'active' } },
        {
          $group: {
            _id: null,
            totalMembers: { $sum: { $size: '$playerList' } },
            averageLevel: { $avg: '$level' }
          }
        }
      ]);

      const stats = memberStats[0] || { totalMembers: 0, averageLevel: 0 };

      return {
        totalBeliefs: totalBeliefsResult.data,
        activeBeliefs: activeBeliefsResult.data,
        totalMembers: stats.totalMembers,
        averageLevel: Math.round(stats.averageLevel * 100) / 100,
        topLevelBeliefs: topLevelBeliefsResult.data
      };
    });
  }

  /**
   * 软删除信仰（解散）
   */
  async disbandBelief(beliefId: number): Promise<XResult<BeliefDocument | null>> {
    return this.updateOne(
      { beliefId },
      {
        status: 'disbanded',
        playerList: [],
        leader: []
      }
    );
  }

  /**
   * 检查信仰名称是否可用
   */
  async isNameAvailable(name: string, excludeBeliefId?: number): Promise<XResult<boolean>> {
    const query: any = { name, status: 'active' };
    if (excludeBeliefId) {
      query.beliefId = { $ne: excludeBeliefId };
    }

    const existsResult = await this.exists(query);
    if (XResultUtils.isFailure(existsResult)) {
      return XResultUtils.error(`检查名称可用性失败: ${existsResult.message}`, existsResult.code);
    }

    return XResultUtils.ok(!existsResult.data);
  }
}
