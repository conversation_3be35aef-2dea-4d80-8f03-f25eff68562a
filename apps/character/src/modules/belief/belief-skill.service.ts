import { Injectable, Logger, forwardRef, Inject } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { GameConfigFacade } from '@libs/game-config';
import { BeliefRepository } from './repositories/belief.repository';
import { CharacterService } from '../character/character.service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import {
  UpgradeBeliefSkillPayloadDto,
  GetBeliefSkillListPayloadDto,
  GetBeliefSkillDetailPayloadDto,
  CheckBeliefSkillUpgradeConditionPayloadDto,
  ResetBeliefSkillPayloadDto,
  GetBeliefSkillBonusPayloadDto,
  GetBeliefSkillUpgradeHistoryPayloadDto,
  BatchUpgradeBeliefSkillPayloadDto
} from '../../common/dto/belief-payload.dto';
import { CharacterRepository } from '@character/common/repositories/character.repository';

/**
 * 信仰技能系统业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 严格基于old项目的信仰技能系统业务逻辑
 *
 * 🎯 核心功能：
 * - 信仰技能学习和升级
 * - 信仰技能解锁条件检查
 * - 信仰活跃度消耗管理
 * - 技能属性加成计算
 * - 技能效果应用
 *
 * 🚀 架构优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 与CharacterService和BeliefService紧密集成
 * - 使用配置驱动的技能系统
 * - 支持技能效果的动态计算
 *
 * 🔗 服务依赖：
 * - CharacterService：角色信息、信仰活跃度管理（直接注入）
 * - BeliefRepository：信仰数据查询（直接注入）
 * - GameConfigFacade：技能配置和消耗配置（直接注入）
 *
 * old项目核心逻辑：
 * - 技能升级：upgradeBeliefSkill(skillId)
 * - 技能解锁：基于信仰等级检查
 * - 活跃度消耗：checkResourceIsEnough(beliefLiveness, cost)
 * - 属性加成：技能等级 * 技能系数
 */
@Injectable()
export class BeliefSkillService extends BaseService {
  constructor(
    private readonly beliefRepository: BeliefRepository,
    private readonly characterRepository: CharacterRepository,
    private readonly gameConfig: GameConfigFacade,
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
  ) {
    super('BeliefSkillService');
  }

  /**
   * 升级信仰技能
   * 基于old项目: upgradeBeliefSkill(skillId)
   */
  async upgradeBeliefSkill(dto: UpgradeBeliefSkillPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`升级信仰技能: ${dto.characterId}, 技能ID: ${dto.skillId}`);

      // 1. 获取角色信息（包含信仰技能数据）
      const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;
      if (!character.beliefId) {
        return XResultUtils.error('角色未加入信仰，无法升级信仰技能', 'NOT_IN_BELIEF');
      }

      // 2. 获取信仰信息
      const beliefResult = await this.beliefRepository.findByBeliefId(character.beliefId);
      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const belief = beliefResult.data;
      if (!belief || belief.status !== 'active') {
        return XResultUtils.error('信仰不存在或已解散', 'BELIEF_NOT_FOUND');
      }

      // 3. 检查技能是否已解锁
      const unlockCheckResult = await this.checkSkillUnlocked(dto.skillId, belief.level);
      if (XResultUtils.isFailure(unlockCheckResult)) {
        return XResultUtils.error(`技能解锁检查失败: ${unlockCheckResult.message}`, unlockCheckResult.code);
      }

      if (!unlockCheckResult.data) {
        return XResultUtils.error('技能尚未解锁，请提升信仰等级', 'SKILL_NOT_UNLOCKED');
      }

      // 4. 获取角色当前技能等级
      const currentLevel = this.getCharacterSkillLevel(character, dto.skillId);
      const nextLevel = currentLevel + 1;

      // 5. 获取升级消耗配置
      const costConfigResult = await this.getSkillUpgradeCost(nextLevel);
      if (XResultUtils.isFailure(costConfigResult)) {
        return XResultUtils.error(`获取升级消耗失败: ${costConfigResult.message}`, costConfigResult.code);
      }

      const { beliefLivenessCost } = costConfigResult.data;

      // 6. 检查信仰活跃度是否足够
      const currentLiveness = (character as any).beliefNum || 0; // 使用beliefNum字段（临时类型断言）
      if (currentLiveness < beliefLivenessCost) {
        return XResultUtils.error('信仰活跃度不足', 'INSUFFICIENT_BELIEF_LIVENESS');
      }

      // 7. 扣除信仰活跃度（使用通用货币扣除方法）
      const deductResult = await this.characterService.deductCurrency({
        characterId: dto.characterId,
        currencyType: 'beliefNum', // 使用beliefNum作为信仰活跃度
        amount: beliefLivenessCost,
        reason: 'upgrade_belief_skill'
      });

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除信仰活跃度失败: ${deductResult.message}`, deductResult.code);
      }

      // 8. 更新角色技能等级
      const updateResult = await this.updateCharacterSkillLevel(dto.characterId, dto.skillId, nextLevel);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新技能等级失败: ${updateResult.message}`, updateResult.code);
      }

      // 9. 重新计算角色属性（技能加成）
      await this.recalculateCharacterAttributes(dto.characterId);

      // 10. 获取技能配置信息
      const skillConfigResult = await this.getSkillConfig(dto.skillId);
      const skillConfig = XResultUtils.isSuccess(skillConfigResult) ? skillConfigResult.data : null;

      return XResultUtils.ok({
        skillId: dto.skillId,
        oldLevel: currentLevel,
        newLevel: nextLevel,
        cost: beliefLivenessCost,
        skillName: skillConfig?.name || `技能${dto.skillId}`,
        skillEffect: skillConfig?.effect || '',
        newBeliefLiveness: currentLiveness - beliefLivenessCost
      });
    }, { reason: 'upgrade_belief_skill', metadata: { characterId: dto.characterId, skillId: dto.skillId } });
  }

  /**
   * 获取信仰技能列表
   * 基于old项目: 获取角色的所有信仰技能信息
   */
  async getBeliefSkillList(dto: GetBeliefSkillListPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰技能列表: ${dto.characterId}`);

    // 1. 获取角色信息
    const characterResult = await this.characterService.getCharacterInfo(dto.characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
    }

    const character = characterResult.data;

    // 2. 获取信仰信息（如果有）
    let belief = null;
    if (character.beliefId) {
      const beliefResult = await this.beliefRepository.findByBeliefId(character.beliefId);
      if (XResultUtils.isSuccess(beliefResult)) {
        belief = beliefResult.data;
      }
    }

    // 3. 获取所有信仰技能配置
    const allSkillsResult = await this.getAllBeliefSkills();
    if (XResultUtils.isFailure(allSkillsResult)) {
      return XResultUtils.error(`获取技能配置失败: ${allSkillsResult.message}`, allSkillsResult.code);
    }

    const allSkills = allSkillsResult.data;

    // 4. 构建技能列表
    const skillList = allSkills.map(skillConfig => {
      const currentLevel = this.getCharacterSkillLevel(character, skillConfig.id);
      const isUnlocked = belief ? this.isSkillUnlockedByLevel(skillConfig.id, belief.level) : false;
      const nextLevelCost = this.getNextLevelCost(currentLevel + 1);

      return {
        skillId: skillConfig.id,
        name: skillConfig.name,
        description: skillConfig.description,
        currentLevel,
        maxLevel: skillConfig.maxLevel || 10,
        isUnlocked,
        nextLevelCost,
        effect: this.calculateSkillEffect(skillConfig, currentLevel),
        nextLevelEffect: this.calculateSkillEffect(skillConfig, currentLevel + 1),
        unlockRequirement: skillConfig.unlockLevel || 1
      };
    });

    return XResultUtils.ok({
      characterId: dto.characterId,
      beliefId: character.beliefId || 0,
      beliefLevel: belief?.level || 0,
      beliefLiveness: (character as any).beliefNum || 0, // 使用beliefNum字段（临时类型断言）
      skillList
    });
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 检查技能是否已解锁
   */
  private async checkSkillUnlocked(skillId: number, beliefLevel: number): Promise<XResult<boolean>> {
    try {
      const skillConfig = await this.gameConfig.beliefSkill.get(skillId);
      if (!skillConfig) {
        return XResultUtils.error('技能配置不存在', 'SKILL_CONFIG_NOT_FOUND');
      }

      const requiredLevel = skillConfig.level || 1; // 使用level字段
      return XResultUtils.ok(beliefLevel >= requiredLevel);
    } catch (error) {
      return XResultUtils.error(`检查技能解锁失败: ${error.message}`, 'CHECK_SKILL_UNLOCK_ERROR');
    }
  }

  /**
   * 获取技能升级消耗
   */
  private async getSkillUpgradeCost(level: number): Promise<XResult<{ beliefLivenessCost: number }>> {
    try {
      const costConfig = await this.gameConfig.beliefSkillExpend.get(level);
      if (!costConfig) {
        return XResultUtils.error('技能消耗配置不存在', 'SKILL_COST_CONFIG_NOT_FOUND');
      }

      return XResultUtils.ok({
        beliefLivenessCost: costConfig.contribution || 100
      });
    } catch (error) {
      return XResultUtils.error(`获取技能消耗失败: ${error.message}`, 'GET_SKILL_COST_ERROR');
    }
  }

  /**
   * 获取角色技能等级
   */
  private getCharacterSkillLevel(character: any, skillId: number): number {
    if (!character.beliefSkillList) {
      return 0;
    }

    const skill = character.beliefSkillList.find((s: any) => s.skillId === skillId);
    return skill ? skill.level : 0;
  }

  /**
   * 更新角色技能等级
   * 基于old项目: beliefSkill.upgradeBeliefSkill方法
   */
  private async updateCharacterSkillLevel(characterId: string, skillId: number, level: number): Promise<XResult<void>> {
    try {
      // 1. 获取角色信息
      const characterResult = await this.characterRepository.findById(characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;
      if (!character) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      // 2. 基于old项目的真实数据结构，角色的信仰技能存储在beliefSkill字段中
      if (!character.beliefSkill) {
        character.beliefSkill = {};
      }

      // 3. 更新技能等级（old项目中使用对象存储，key为skillId，value为level）
      character.beliefSkill[skillId] = level;

      // 4. 保存角色数据
      const saveResult = await this.characterRepository.updateById(characterId, character);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存角色数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`更新角色技能等级成功: ${characterId}, 技能${skillId} -> ${level}级`);
      return XResultUtils.ok(undefined);

    } catch (error) {
      return XResultUtils.error(`更新技能等级失败: ${error.message}`, 'UPDATE_SKILL_LEVEL_ERROR');
    }
  }

  /**
   * 重新计算角色属性
   * 基于old项目: player.heros.reCalcAllHeroAttr()方法
   */
  private async recalculateCharacterAttributes(characterId: string): Promise<void> {
    try {
      // 1. 调用角色服务重新计算属性
      const recalcResult = await this.characterService.recalculateAttributes(characterId);

      if (XResultUtils.isFailure(recalcResult)) {
        this.logger.warn(`重新计算角色属性失败: ${recalcResult.message}`);
        return;
      }

      // 2. 触发英雄属性重新计算（通过微服务调用）
      try {
        const heroRecalcResult = await this.characterService.recalculateHeroAttributes(characterId);
        if (XResultUtils.isFailure(heroRecalcResult)) {
          this.logger.warn(`重新计算英雄属性失败: ${heroRecalcResult.message}`);
        }
      } catch (error) {
        this.logger.warn(`调用英雄属性重新计算失败: ${error.message}`);
      }

      this.logger.log(`触发角色属性重新计算完成: ${characterId}`);
    } catch (error) {
      this.logger.error(`重新计算角色属性失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取技能配置
   */
  private async getSkillConfig(skillId: number): Promise<XResult<any>> {
    try {
      const config = await this.gameConfig.beliefSkill.get(skillId);
      return config ? XResultUtils.ok(config) : XResultUtils.error('技能配置不存在', 'SKILL_CONFIG_NOT_FOUND');
    } catch (error) {
      return XResultUtils.error(`获取技能配置失败: ${error.message}`, 'GET_SKILL_CONFIG_ERROR');
    }
  }

  /**
   * 获取所有信仰技能
   */
  private async getAllBeliefSkills(): Promise<XResult<any[]>> {
    try {
      const skills = await this.gameConfig.beliefSkill.getAll();
      return XResultUtils.ok(skills || []);
    } catch (error) {
      return XResultUtils.error(`获取所有技能失败: ${error.message}`, 'GET_ALL_SKILLS_ERROR');
    }
  }

  /**
   * 检查技能是否按等级解锁
   */
  private isSkillUnlockedByLevel(skillId: number, beliefLevel: number): boolean {
    // 简化的解锁逻辑，实际应该从配置获取
    const unlockLevels: { [key: number]: number } = {
      1: 1, 2: 3, 3: 5, 4: 7, 5: 10,
      6: 12, 7: 15, 8: 18, 9: 20, 10: 20
    };
    
    return beliefLevel >= (unlockLevels[skillId] || 1);
  }

  /**
   * 获取下一级消耗
   */
  private getNextLevelCost(level: number): number {
    // 简化的消耗计算，实际应该从配置获取
    return level * 100;
  }

  /**
   * 计算技能效果
   */
  private calculateSkillEffect(skillConfig: any, level: number): string {
    if (level === 0) {
      return '未学习';
    }

    // 简化的效果计算，实际应该根据技能类型计算
    const baseEffect = skillConfig.baseEffect || 10;
    const effectValue = baseEffect * level;
    
    return `${skillConfig.effectType || '属性'}+${effectValue}`;
  }

  /**
   * 获取信仰技能详情
   */
  async getBeliefSkillDetail(dto: GetBeliefSkillDetailPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰技能详情: ${dto.characterId}, 技能${dto.skillId}`);

    try {
      // 1. 获取角色信息
      const characterResult = await this.characterRepository.findById(dto.characterId || '');
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;
      if (!character) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      // 2. 获取技能配置
      const skillConfigResult = await this.getSkillConfig(dto.skillId);
      if (XResultUtils.isFailure(skillConfigResult)) {
        return skillConfigResult;
      }

      const skillConfig = skillConfigResult.data;
      const currentLevel = this.getCharacterSkillLevel(character, dto.skillId);

      return XResultUtils.ok({
        skillId: dto.skillId,
        skillName: skillConfig.name || `技能${dto.skillId}`,
        skillDescription: skillConfig.description || '技能描述',
        currentLevel,
        maxLevel: skillConfig.maxLevel || 10,
        effect: this.calculateSkillEffect(skillConfig, currentLevel),
        nextLevelCost: this.getNextLevelCost(currentLevel + 1),
        canUpgrade: currentLevel < (skillConfig.maxLevel || 10)
      });

    } catch (error) {
      this.logger.error(`获取信仰技能详情失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰技能详情失败: ${error.message}`, 'GET_SKILL_DETAIL_ERROR');
    }
  }

  /**
   * 检查信仰技能升级条件
   */
  async checkBeliefSkillUpgradeCondition(dto: CheckBeliefSkillUpgradeConditionPayloadDto): Promise<XResult<any>> {
    this.logger.log(`检查信仰技能升级条件: ${dto.characterId}, 技能${dto.skillId}`);

    try {
      const characterResult = await this.characterRepository.findById(dto.characterId || '');
      if (XResultUtils.isFailure(characterResult)) {
        return characterResult;
      }

      const character = characterResult.data;
      if (!character) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      const currentLevel = this.getCharacterSkillLevel(character, dto.skillId);
      const cost = this.getNextLevelCost(currentLevel + 1);
      const hasEnoughResource = (character.beliefNum || 0) >= cost;

      return XResultUtils.ok({
        canUpgrade: hasEnoughResource && currentLevel < 10,
        currentLevel,
        cost,
        hasEnoughResource,
        currentBeliefNum: character.beliefNum || 0
      });

    } catch (error) {
      this.logger.error(`检查升级条件失败: ${error.message}`, error.stack);
      return XResultUtils.error(`检查升级条件失败: ${error.message}`, 'CHECK_UPGRADE_CONDITION_ERROR');
    }
  }

  /**
   * 重置信仰技能
   */
  async resetBeliefSkill(dto: ResetBeliefSkillPayloadDto): Promise<XResult<any>> {
    this.logger.log(`重置信仰技能: ${dto.characterId}, 技能${dto.skillId}`);

    try {
      // 1. 获取角色信息
      const characterResult = await this.characterRepository.findById(dto.characterId || '');
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;
      if (!character) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      // 2. 检查信仰技能列表
      if (!character.beliefSkillList || character.beliefSkillList.length === 0) {
        return XResultUtils.error('没有可重置的信仰技能', 'NO_SKILLS_TO_RESET');
      }

      let resetSkills: number[] = [];
      let totalRefund = 0;

      if (dto.resetType === 'all') {
        // 重置所有技能
        for (const skill of character.beliefSkillList) {
          if (skill.level > 1) {
            // 计算退还的信仰活跃度（基于old项目的重置逻辑）
            const refundAmount = this.calculateSkillResetRefund(skill.level);
            totalRefund += refundAmount;

            // 重置技能等级
            skill.level = 1;
            skill.lastUpgradeTime = Date.now();
            resetSkills.push(skill.skillId);
          }
        }
      } else {
        // 重置单个技能
        const skillRecord = character.beliefSkillList.find((skill: any) => skill.skillId === dto.skillId);
        if (!skillRecord) {
          return XResultUtils.error('技能不存在', 'SKILL_NOT_FOUND');
        }

        if (skillRecord.level <= 1) {
          return XResultUtils.error('技能等级已是最低级，无需重置', 'SKILL_ALREADY_MIN_LEVEL');
        }

        // 计算退还金额
        totalRefund = this.calculateSkillResetRefund(skillRecord.level);

        // 重置技能
        skillRecord.level = 1;
        skillRecord.lastUpgradeTime = Date.now();
        resetSkills.push(skillRecord.skillId);
      }

      // 3. 退还信仰活跃度
      if (totalRefund > 0) {
        const refundResult = await this.characterService.addResource(
          dto.characterId || '',
          dto.serverId || '',
          'beliefNum',
          totalRefund,
          'belief_skill_reset'
        );

        if (XResultUtils.isFailure(refundResult)) {
          return XResultUtils.error(`退还信仰活跃度失败: ${refundResult.message}`, refundResult.code);
        }
      }

      // 4. 保存角色数据
      const saveResult = await this.characterRepository.updateById(character._id, character);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存角色数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 5. 重新计算角色属性
      await this.recalculateCharacterAttributes(dto.characterId || '');

      return XResultUtils.ok({
        resetSkills,
        refundAmount: totalRefund,
        resetType: dto.resetType || 'single',
        message: `成功重置${resetSkills.length}个技能，退还${totalRefund}点信仰活跃度`
      });

    } catch (error) {
      this.logger.error(`重置技能失败: ${error.message}`, error.stack);
      return XResultUtils.error(`重置技能失败: ${error.message}`, 'RESET_SKILL_ERROR');
    }
  }

  /**
   * 获取信仰技能加成统计
   */
  async getBeliefSkillBonus(dto: GetBeliefSkillBonusPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取信仰技能加成: ${dto.characterId}`);

    try {
      const characterResult = await this.characterRepository.findById(dto.characterId || '');
      if (XResultUtils.isFailure(characterResult)) {
        return characterResult;
      }

      const character = characterResult.data;
      if (!character) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      // 2. 计算技能加成
      if (!character.beliefSkillList || character.beliefSkillList.length === 0) {
        return XResultUtils.ok({
          totalBonus: {
            attack: 0,
            defense: 0,
            speed: 0,
            technique: 0,
            physical: 0
          },
          skillDetails: [],
          totalSkillLevel: 0
        });
      }

      let totalBonus = {
        attack: 0,
        defense: 0,
        speed: 0,
        technique: 0,
        physical: 0
      };

      let skillDetails: any[] = [];
      let totalSkillLevel = 0;

      // 3. 遍历所有技能计算加成
      for (const skill of character.beliefSkillList) {
        if (skill.status !== 1 || skill.level <= 0) {
          continue; // 跳过未解锁或无效技能
        }

        // 获取技能配置
        const skillConfigResult = await this.getSkillConfig(skill.skillId);
        if (XResultUtils.isFailure(skillConfigResult)) {
          continue;
        }

        const skillConfig = skillConfigResult.data;

        // 计算技能加成（基于old项目的技能效果计算）
        const skillBonus = this.calculateSkillBonus(skillConfig, skill.level);

        // 累加到总加成
        totalBonus.attack += skillBonus.attack || 0;
        totalBonus.defense += skillBonus.defense || 0;
        totalBonus.speed += skillBonus.speed || 0;
        totalBonus.technique += skillBonus.technique || 0;
        totalBonus.physical += skillBonus.physical || 0;

        // 记录技能详情
        skillDetails.push({
          skillId: skill.skillId,
          skillName: skillConfig.name || `技能${skill.skillId}`,
          level: skill.level,
          bonus: skillBonus,
          description: skillConfig.description || '',
          isActive: skill.status === 1
        });

        totalSkillLevel += skill.level;
      }

      return XResultUtils.ok({
        totalBonus,
        skillDetails,
        totalSkillLevel,
        activeSkillCount: skillDetails.length
      });

    } catch (error) {
      this.logger.error(`获取技能加成失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取技能加成失败: ${error.message}`, 'GET_SKILL_BONUS_ERROR');
    }
  }

  /**
   * 批量升级信仰技能
   */
  async batchUpgradeBeliefSkill(dto: BatchUpgradeBeliefSkillPayloadDto): Promise<XResult<any>> {
    this.logger.log(`批量升级技能: ${dto.characterId}`);

    try {
      // 1. 获取角色信息
      const characterResult = await this.characterRepository.findById(dto.characterId || '');
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;
      if (!character) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      // 2. 计算总升级费用
      let totalCost = 0;
      let upgradeDetails: any[] = [];

      for (const upgrade of dto.skillUpgrades) {
        const currentLevel = this.getCharacterSkillLevel(character, upgrade.skillId);

        if (upgrade.targetLevel <= currentLevel) {
          continue; // 跳过无需升级的技能
        }

        // 计算升级费用
        let skillCost = 0;
        for (let level = currentLevel + 1; level <= upgrade.targetLevel; level++) {
          const costResult = await this.getSkillUpgradeCost(level);
          if (XResultUtils.isSuccess(costResult)) {
            skillCost += costResult.data.beliefLivenessCost;
          }
        }

        totalCost += skillCost;
        upgradeDetails.push({
          skillId: upgrade.skillId,
          fromLevel: currentLevel,
          toLevel: upgrade.targetLevel,
          cost: skillCost
        });
      }

      // 3. 检查费用限制
      if (dto.maxCost && totalCost > dto.maxCost) {
        return XResultUtils.error(`升级费用${totalCost}超过限制${dto.maxCost}`, 'COST_LIMIT_EXCEEDED');
      }

      // 4. 检查信仰活跃度是否足够
      const currentLiveness = (character as any).beliefNum || 0;
      if (currentLiveness < totalCost) {
        return XResultUtils.error('信仰活跃度不足', 'INSUFFICIENT_BELIEF_LIVENESS');
      }

      // 5. 扣除信仰活跃度
      const deductResult = await this.characterService.deductResource(
        dto.characterId || '',
        dto.serverId || '',
        'beliefNum',
        totalCost,
        'belief_skill_batch_upgrade'
      );

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除信仰活跃度失败: ${deductResult.message}`, deductResult.code);
      }

      // 6. 执行批量升级
      let successCount = 0;
      for (const upgrade of dto.skillUpgrades) {
        const updateResult = await this.updateCharacterSkillLevel(
          dto.characterId || '',
          upgrade.skillId,
          upgrade.targetLevel
        );

        if (XResultUtils.isSuccess(updateResult)) {
          successCount++;
        }
      }

      // 7. 保存角色数据
      const saveResult = await this.characterRepository.updateById(character._id, character);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存角色数据失败: ${saveResult.message}`, saveResult.code);
      }

      // 8. 重新计算角色属性
      await this.recalculateCharacterAttributes(dto.characterId || '');

      return XResultUtils.ok({
        upgradeCount: successCount,
        totalCost,
        upgradeDetails,
        newBeliefLiveness: currentLiveness - totalCost,
        message: `成功升级${successCount}个技能，消耗${totalCost}点信仰活跃度`
      });

    } catch (error) {
      this.logger.error(`批量升级失败: ${error.message}`, error.stack);
      return XResultUtils.error(`批量升级失败: ${error.message}`, 'BATCH_UPGRADE_ERROR');
    }
  }

  /**
   * 获取信仰技能升级历史
   */
  async getBeliefSkillUpgradeHistory(dto: GetBeliefSkillUpgradeHistoryPayloadDto): Promise<XResult<any>> {
    this.logger.log(`获取升级历史: ${dto.characterId}`);

    try {
      // 1. 获取角色信息
      const characterResult = await this.characterRepository.findById(dto.characterId || '');
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;
      if (!character) {
        return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
      }

      // 2. 获取升级历史记录（从beliefSkillList中获取）
      const page = dto.page || 1;
      const limit = dto.limit || 20;
      const skip = (page - 1) * limit;

      let records: any[] = [];

      if (character.beliefSkillList && character.beliefSkillList.length > 0) {
        // 构建历史记录（基于技能的最后升级时间）
        const skillRecords = await Promise.all(
          character.beliefSkillList.map(async (skill: any) => {
            const skillConfigResult = await this.getSkillConfig(skill.skillId);
            const skillConfig = XResultUtils.isSuccess(skillConfigResult) ? skillConfigResult.data : null;

            return {
              skillId: skill.skillId,
              skillName: skillConfig?.name || `技能${skill.skillId}`,
              currentLevel: skill.level,
              upgradeTime: skill.lastUpgradeTime || 0,
              totalUpgradeCost: skill.totalUpgradeCost || 0,
              status: skill.status === 1 ? '已解锁' : '未解锁'
            };
          })
        );

        // 按升级时间排序
        skillRecords.sort((a, b) => b.upgradeTime - a.upgradeTime);

        // 时间过滤
        let filteredRecords = skillRecords;
        if (dto.startTime || dto.endTime) {
          filteredRecords = skillRecords.filter(record => {
            if (dto.startTime && record.upgradeTime < dto.startTime) return false;
            if (dto.endTime && record.upgradeTime > dto.endTime) return false;
            return true;
          });
        }

        // 分页处理
        const totalCount = filteredRecords.length;
        records = filteredRecords.slice(skip, skip + limit);

        return XResultUtils.ok({
          records,
          total: totalCount,
          page,
          limit,
          totalPages: Math.ceil(totalCount / limit),
          hasMore: skip + limit < totalCount
        });
      }

      return XResultUtils.ok({
        records: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
        hasMore: false
      });

    } catch (error) {
      this.logger.error(`获取升级历史失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取升级历史失败: ${error.message}`, 'GET_UPGRADE_HISTORY_ERROR');
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 计算技能重置退还金额
   * 基于old项目的重置逻辑
   */
  private calculateSkillResetRefund(skillLevel: number): number {
    // 退还80%的升级费用
    let totalCost = 0;
    for (let level = 2; level <= skillLevel; level++) {
      totalCost += level * 100; // 简化的费用计算
    }
    return Math.floor(totalCost * 0.8);
  }

  /**
   * 计算技能加成
   * 基于old项目的技能效果计算
   */
  private calculateSkillBonus(skillConfig: any, skillLevel: number): any {
    const baseBonus = {
      attack: 0,
      defense: 0,
      speed: 0,
      technique: 0,
      physical: 0
    };

    if (!skillConfig || !skillConfig.effects) {
      return baseBonus;
    }

    // 根据技能配置和等级计算加成
    for (const effect of skillConfig.effects) {
      const bonusValue = (effect.baseValue || 0) + (effect.levelMultiplier || 0) * (skillLevel - 1);

      switch (effect.type) {
        case 'attack':
          baseBonus.attack += bonusValue;
          break;
        case 'defense':
          baseBonus.defense += bonusValue;
          break;
        case 'speed':
          baseBonus.speed += bonusValue;
          break;
        case 'technique':
          baseBonus.technique += bonusValue;
          break;
        case 'physical':
          baseBonus.physical += bonusValue;
          break;
      }
    }

    return baseBonus;
  }
}
