import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 领导者信息子文档
@Schema({ _id: false })
export class BeliefLeader {
  @Prop({ required: true })
  uid: string;

  @Prop({ required: true })
  gid: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  faceUrl: string;

  // 职位: 1-董事长, 2-副董事长, 3-总经理, 4-总监
  @Prop({ required: true })
  pos: number;
}

// 动态信息子文档
@Schema({ _id: false })
export class BeliefNotify {
  @Prop({ required: true })
  time: number;

  @Prop({ required: true })
  msg: string;
}

@Schema({ collection: 'beliefs', timestamps: true })
export class Belief extends Document {
  @Prop({ required: true, unique: true, index: true })
  beliefId: number; // 信仰ID

  @Prop({ default: '' })
  notice: string; // 信仰公告

  @Prop({ default: 0 })
  beliefGold: number; // 信仰资金

  @Prop({ type: [BeliefLeader], default: [] })
  leader: BeliefLeader[]; // 领导者列表

  @Prop({ type: [BeliefNotify], default: [] })
  notify: BeliefNotify[]; // 信仰动态

  @Prop({ default: 1 })
  level: number; // 信仰等级

  @Prop({ default: 0 })
  beliefExp: number; // 信仰经验

  @Prop({ default: 0 })
  todayGoldNum: number; // 当日捐献球币数量

  @Prop({ default: 0 })
  weekExp: number; // 周贡献

  @Prop({ default: () => Date.now() })
  clearTime: number; // 清空当日球币捐赠的时间戳
}

export const BeliefSchema = SchemaFactory.createForClass(Belief);
