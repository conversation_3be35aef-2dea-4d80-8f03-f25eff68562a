import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@libs/redis';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { BeliefDocument } from './schemas/belief.schema';

/**
 * 信仰缓存服务
 * 基于old项目的信仰缓存机制，提供高性能的信仰数据缓存
 * 
 * 🎯 核心功能：
 * - 信仰基础信息缓存
 * - 信仰成员列表缓存
 * - 信仰排行榜缓存
 * - 玩家信仰关系缓存
 * - 缓存失效和更新机制
 * 
 * 🔧 缓存策略：
 * - 信仰基础信息：30分钟过期
 * - 成员列表：15分钟过期
 * - 排行榜：1小时过期
 * - 玩家关系：1小时过期
 * 
 * old项目对应：
 * - beliefService.js中的缓存逻辑
 * - Redis前缀架构v3.0
 */
@Injectable()
export class BeliefCacheService {
  private readonly logger = new Logger(BeliefCacheService.name);
  
  // 缓存键前缀
  private readonly CACHE_PREFIX = 'belief';
  
  // 缓存过期时间（秒）
  private readonly CACHE_TTL = {
    BELIEF_INFO: 30 * 60,        // 信仰基础信息：30分钟
    MEMBER_LIST: 15 * 60,        // 成员列表：15分钟
    RANKING: 60 * 60,            // 排行榜：1小时
    PLAYER_BELIEF: 60 * 60,      // 玩家信仰关系：1小时
    STATS: 30 * 60,              // 统计信息：30分钟
  };

  constructor(
    private readonly redisService: RedisService,
  ) {}

  // ==================== 信仰基础信息缓存 ====================

  /**
   * 获取信仰基础信息缓存键
   */
  private getBeliefInfoKey(beliefId: number, serverId: string): string {
    return `${this.CACHE_PREFIX}:info:${serverId}:${beliefId}`;
  }

  /**
   * 缓存信仰基础信息
   */
  async cacheBeliefInfo(beliefId: number, serverId: string, beliefData: any): Promise<XResult<void>> {
    try {
      const key = this.getBeliefInfoKey(beliefId, serverId);
      await this.redisService.setex(key, this.CACHE_TTL.BELIEF_INFO, JSON.stringify(beliefData));
      
      this.logger.debug(`缓存信仰基础信息: ${key}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`缓存信仰基础信息失败: ${error.message}`, error.stack);
      return XResultUtils.error(`缓存信仰基础信息失败: ${error.message}`, 'CACHE_ERROR');
    }
  }

  /**
   * 获取信仰基础信息缓存
   */
  async getBeliefInfo(beliefId: number, serverId: string): Promise<XResult<any | null>> {
    try {
      const key = this.getBeliefInfoKey(beliefId, serverId);
      const cached = await this.redisService.get(key);
      
      if (cached) {
        this.logger.debug(`命中信仰基础信息缓存: ${key}`);
        return XResultUtils.ok(JSON.parse(cached));
      }
      
      return XResultUtils.ok(null);
    } catch (error) {
      this.logger.error(`获取信仰基础信息缓存失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰基础信息缓存失败: ${error.message}`, 'CACHE_ERROR');
    }
  }

  // ==================== 信仰成员列表缓存 ====================

  /**
   * 获取信仰成员列表缓存键
   */
  private getBeliefMembersKey(beliefId: number, serverId: string): string {
    return `${this.CACHE_PREFIX}:members:${serverId}:${beliefId}`;
  }

  /**
   * 缓存信仰成员列表
   */
  async cacheBeliefMembers(beliefId: number, serverId: string, members: any[]): Promise<XResult<void>> {
    try {
      const key = this.getBeliefMembersKey(beliefId, serverId);
      await this.redisService.setex(key, this.CACHE_TTL.MEMBER_LIST, JSON.stringify(members));
      
      this.logger.debug(`缓存信仰成员列表: ${key}, 成员数: ${members.length}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`缓存信仰成员列表失败: ${error.message}`, error.stack);
      return XResultUtils.error(`缓存信仰成员列表失败: ${error.message}`, 'CACHE_ERROR');
    }
  }

  /**
   * 获取信仰成员列表缓存
   */
  async getBeliefMembers(beliefId: number, serverId: string): Promise<XResult<any[] | null>> {
    try {
      const key = this.getBeliefMembersKey(beliefId, serverId);
      const cached = await this.redisService.get(key);
      
      if (cached) {
        this.logger.debug(`命中信仰成员列表缓存: ${key}`);
        return XResultUtils.ok(JSON.parse(cached));
      }
      
      return XResultUtils.ok(null);
    } catch (error) {
      this.logger.error(`获取信仰成员列表缓存失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰成员列表缓存失败: ${error.message}`, 'CACHE_ERROR');
    }
  }

  // ==================== 信仰排行榜缓存 ====================

  /**
   * 获取信仰排行榜缓存键
   */
  private getBeliefRankingKey(serverId: string, rankType: string = 'level'): string {
    return `${this.CACHE_PREFIX}:ranking:${serverId}:${rankType}`;
  }

  /**
   * 缓存信仰排行榜
   */
  async cacheBeliefRanking(serverId: string, rankType: string, ranking: any[]): Promise<XResult<void>> {
    try {
      const key = this.getBeliefRankingKey(serverId, rankType);
      await this.redisService.setex(key, this.CACHE_TTL.RANKING, JSON.stringify(ranking));
      
      this.logger.debug(`缓存信仰排行榜: ${key}, 排行数: ${ranking.length}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`缓存信仰排行榜失败: ${error.message}`, error.stack);
      return XResultUtils.error(`缓存信仰排行榜失败: ${error.message}`, 'CACHE_ERROR');
    }
  }

  /**
   * 获取信仰排行榜缓存
   */
  async getBeliefRanking(serverId: string, rankType: string = 'level'): Promise<XResult<any[] | null>> {
    try {
      const key = this.getBeliefRankingKey(serverId, rankType);
      const cached = await this.redisService.get(key);
      
      if (cached) {
        this.logger.debug(`命中信仰排行榜缓存: ${key}`);
        return XResultUtils.ok(JSON.parse(cached));
      }
      
      return XResultUtils.ok(null);
    } catch (error) {
      this.logger.error(`获取信仰排行榜缓存失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰排行榜缓存失败: ${error.message}`, 'CACHE_ERROR');
    }
  }

  // ==================== 玩家信仰关系缓存 ====================

  /**
   * 获取玩家信仰关系缓存键
   */
  private getPlayerBeliefKey(characterId: string, serverId: string): string {
    return `${this.CACHE_PREFIX}:player:${serverId}:${characterId}`;
  }

  /**
   * 缓存玩家信仰关系
   */
  async cachePlayerBelief(characterId: string, serverId: string, beliefData: any): Promise<XResult<void>> {
    try {
      const key = this.getPlayerBeliefKey(characterId, serverId);
      await this.redisService.setex(key, this.CACHE_TTL.PLAYER_BELIEF, JSON.stringify(beliefData));
      
      this.logger.debug(`缓存玩家信仰关系: ${key}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`缓存玩家信仰关系失败: ${error.message}`, error.stack);
      return XResultUtils.error(`缓存玩家信仰关系失败: ${error.message}`, 'CACHE_ERROR');
    }
  }

  /**
   * 获取玩家信仰关系缓存
   */
  async getPlayerBelief(characterId: string, serverId: string): Promise<XResult<any | null>> {
    try {
      const key = this.getPlayerBeliefKey(characterId, serverId);
      const cached = await this.redisService.get(key);
      
      if (cached) {
        this.logger.debug(`命中玩家信仰关系缓存: ${key}`);
        return XResultUtils.ok(JSON.parse(cached));
      }
      
      return XResultUtils.ok(null);
    } catch (error) {
      this.logger.error(`获取玩家信仰关系缓存失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取玩家信仰关系缓存失败: ${error.message}`, 'CACHE_ERROR');
    }
  }

  // ==================== 缓存失效管理 ====================

  /**
   * 清除信仰相关缓存
   */
  async invalidateBeliefCache(beliefId: number, serverId: string): Promise<XResult<void>> {
    try {
      const keys = [
        this.getBeliefInfoKey(beliefId, serverId),
        this.getBeliefMembersKey(beliefId, serverId),
      ];
      
      await Promise.all(keys.map(key => this.redisService.del(key)));
      
      this.logger.debug(`清除信仰缓存: 信仰${beliefId}, 服务器${serverId}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`清除信仰缓存失败: ${error.message}`, error.stack);
      return XResultUtils.error(`清除信仰缓存失败: ${error.message}`, 'CACHE_ERROR');
    }
  }

  /**
   * 清除玩家信仰缓存
   */
  async invalidatePlayerBeliefCache(characterId: string, serverId: string): Promise<XResult<void>> {
    try {
      const key = this.getPlayerBeliefKey(characterId, serverId);
      await this.redisService.del(key);
      
      this.logger.debug(`清除玩家信仰缓存: 玩家${characterId}, 服务器${serverId}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`清除玩家信仰缓存失败: ${error.message}`, error.stack);
      return XResultUtils.error(`清除玩家信仰缓存失败: ${error.message}`, 'CACHE_ERROR');
    }
  }

  /**
   * 清除排行榜缓存
   */
  async invalidateRankingCache(serverId: string): Promise<XResult<void>> {
    try {
      const pattern = `${this.CACHE_PREFIX}:ranking:${serverId}:*`;
      const keys = await this.redisService.keys(pattern);
      
      if (keys.length > 0) {
        await this.redisService.del(...keys);
      }
      
      this.logger.debug(`清除排行榜缓存: 服务器${serverId}, 清除${keys.length}个键`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`清除排行榜缓存失败: ${error.message}`, error.stack);
      return XResultUtils.error(`清除排行榜缓存失败: ${error.message}`, 'CACHE_ERROR');
    }
  }
}
