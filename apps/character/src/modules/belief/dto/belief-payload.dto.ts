import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, <PERSON>, <PERSON> } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

/**
 * 获取信仰信息Payload DTO
 * @MessagePattern('belief.getInfo')
 */
export class GetBeliefInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '信仰ID', example: 1 })
  @Expose()
  @IsInt({ message: '信仰ID必须是整数' })
  @IsNotEmpty({ message: '信仰ID不能为空' })
  beliefId: number;
}

/**
 * 信仰捐赠Payload DTO
 * @MessagePattern('belief.donate')
 */
export class DonateBeliefPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '信仰ID', example: 1 })
  @Expose()
  @IsInt({ message: '信仰ID必须是整数' })
  @IsNotEmpty({ message: '信仰ID不能为空' })
  beliefId: number;

  @ApiProperty({ description: '捐赠次数', example: 1 })
  @Expose()
  @IsInt({ message: '捐赠次数必须是整数' })
  @Min(1, { message: '捐赠次数不能小于1' })
  @IsNotEmpty({ message: '捐赠次数不能为空' })
  num: number;

  @ApiProperty({ description: '捐赠类型: 1-欧元, 2-球币', example: 1 })
  @Expose()
  @IsInt({ message: '捐赠类型必须是整数' })
  @Min(1)
  @Max(2)
  @IsNotEmpty({ message: '捐赠类型不能为空' })
  type: number;
}

/**
 * 升级信仰技能Payload DTO
 * @MessagePattern('belief.upgradeSkill')
 */
export class UpgradeBeliefSkillPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '技能ID', example: 1001 })
  @Expose()
  @IsInt({ message: '技能ID必须是整数' })
  @IsNotEmpty({ message: '技能ID不能为空' })
  skillId: number;
}
