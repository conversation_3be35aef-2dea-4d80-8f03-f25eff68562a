import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BeliefSkillService } from './belief-skill.service';
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import { Cacheable, CacheEvict } from '@libs/redis';
import {
  UpgradeBeliefSkillPayloadDto,
  GetBeliefSkillListPayloadDto,
  GetBeliefSkillDetailPayloadDto,
  CheckBeliefSkillUpgradeConditionPayloadDto,
  ResetBeliefSkillPayloadDto,
  GetBeliefSkillBonusPayloadDto,
  BatchUpgradeBeliefSkillPayloadDto,
  GetBeliefSkillUpgradeHistoryPayloadDto
} from '../../common/dto/belief-payload.dto';

/**
 * 信仰技能控制器
 * 基于old项目的完整信仰技能系统实现
 * 
 * 🎯 核心功能：
 * - 信仰技能升级
 * - 信仰技能列表查询
 * - 信仰技能解锁条件检查
 * - 信仰活跃度消耗管理
 * 
 * 🚀 架构特性：
 * - 继承BaseController的标准化处理
 * - WebSocket微服务接口(@MessagePattern)
 * - Redis缓存装饰器(@Cacheable/@CacheEvict)
 * - 统一的XResponse响应格式
 * - 完整的日志记录和性能监控
 * 
 * old项目对应：
 * - beliefService.js中的技能相关方法
 * - BeliefSkill.json配置表
 * - 信仰技能升级和管理逻辑
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class BeliefSkillController extends BaseController {
  constructor(
    private readonly beliefSkillService: BeliefSkillService,
  ) {
    super('BeliefSkillController');
  }

  /**
   * 升级信仰技能
   * 基于old项目: upgradeBeliefSkill接口
   * 
   * 🔧 功能说明：
   * - 消耗信仰活跃度升级技能
   * - 检查信仰等级和解锁条件
   * - 更新角色技能等级
   * - 记录升级历史
   * 
   * 对应old项目: beliefService.upgradeBeliefSkill
   */
  @MessagePattern('beliefSkill.upgrade')
  @CacheEvict({
    key: 'beliefSkill:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async upgradeBeliefSkill(payload: UpgradeBeliefSkillPayloadDto) {
    const result = await this.beliefSkillService.upgradeBeliefSkill(payload);
    return this.fromResult(result, '信仰技能升级成功');
  }

  /**
   * 获取信仰技能列表
   * 基于old项目: getBeliefSkillList接口
   * 
   * 🔧 功能说明：
   * - 获取角色的信仰技能列表
   * - 包含技能等级和解锁状态
   * - 显示升级所需资源
   * - 计算技能加成效果
   * 
   * 对应old项目: beliefService.getBeliefSkillList
   */
  @MessagePattern('beliefSkill.getList')
  @Cacheable({
    key: 'beliefSkill:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 30 * 60 // 30分钟缓存
  })
  async getBeliefSkillList(payload: GetBeliefSkillListPayloadDto) {
    const result = await this.beliefSkillService.getBeliefSkillList(payload);
    return this.fromResult(result, '获取信仰技能列表成功');
  }

  /**
   * 获取信仰技能详情
   * 基于old项目: getBeliefSkillDetail接口
   * 
   * 🔧 功能说明：
   * - 获取指定技能的详细信息
   * - 包含技能描述和效果
   * - 显示升级路径和消耗
   * - 计算下一级加成
   * 
   * 对应old项目: beliefService.getBeliefSkillDetail
   */
  @MessagePattern('beliefSkill.getDetail')
  @Cacheable({
    key: 'beliefSkill:detail:#{payload.characterId}:#{payload.skillId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60 * 60 // 1小时缓存
  })
  async getBeliefSkillDetail(payload: GetBeliefSkillDetailPayloadDto) {
    const result = await this.beliefSkillService.getBeliefSkillDetail(payload);
    return this.fromResult(result, '获取信仰技能详情成功');
  }

  /**
   * 检查信仰技能升级条件
   * 基于old项目: checkBeliefSkillUpgradeCondition接口
   * 
   * 🔧 功能说明：
   * - 检查技能是否可以升级
   * - 验证信仰等级要求
   * - 检查资源是否充足
   * - 返回升级预览信息
   * 
   * 对应old项目: beliefService.checkBeliefSkillUpgradeCondition
   */
  @MessagePattern('beliefSkill.checkUpgradeCondition')
  async checkBeliefSkillUpgradeCondition(payload: CheckBeliefSkillUpgradeConditionPayloadDto) {
    const result = await this.beliefSkillService.checkBeliefSkillUpgradeCondition(payload);
    return this.fromResult(result, '检查升级条件成功');
  }

  /**
   * 重置信仰技能
   * 基于old项目: resetBeliefSkill接口
   * 
   * 🔧 功能说明：
   * - 重置指定技能等级
   * - 返还部分消耗资源
   * - 更新技能状态
   * - 记录重置历史
   * 
   * 对应old项目: beliefService.resetBeliefSkill
   */
  @MessagePattern('beliefSkill.reset')
  @CacheEvict({
    key: 'beliefSkill:*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async resetBeliefSkill(payload: ResetBeliefSkillPayloadDto) {
    const result = await this.beliefSkillService.resetBeliefSkill(payload);
    return this.fromResult(result, '重置信仰技能成功');
  }

  /**
   * 获取信仰技能加成统计
   * 基于old项目: getBeliefSkillBonus接口
   * 
   * 🔧 功能说明：
   * - 计算所有技能的总加成
   * - 按属性类型分类统计
   * - 显示加成来源详情
   * - 用于战力计算参考
   * 
   * 对应old项目: beliefService.getBeliefSkillBonus
   */
  @MessagePattern('beliefSkill.getBonus')
  @Cacheable({
    key: 'beliefSkill:bonus:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 15 * 60 // 15分钟缓存
  })
  async getBeliefSkillBonus(payload: GetBeliefSkillBonusPayloadDto) {
    const result = await this.beliefSkillService.getBeliefSkillBonus(payload);
    return this.fromResult(result, '获取信仰技能加成成功');
  }

  /**
   * 批量升级信仰技能
   * 基于old项目: batchUpgradeBeliefSkill接口
   * 
   * 🔧 功能说明：
   * - 一次性升级多个技能
   * - 优化资源消耗计算
   * - 支持升级优先级设置
   * - 提供批量操作反馈
   * 
   * 对应old项目: beliefService.batchUpgradeBeliefSkill
   */
  @MessagePattern('beliefSkill.batchUpgrade')
  @CacheEvict({
    key: 'beliefSkill:*:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchUpgradeBeliefSkill(payload: BatchUpgradeBeliefSkillPayloadDto) {
    const result = await this.beliefSkillService.batchUpgradeBeliefSkill(payload);
    return this.fromResult(result, '批量升级信仰技能成功');
  }

  /**
   * 获取信仰技能升级历史
   * 基于old项目: getBeliefSkillUpgradeHistory接口
   * 
   * 🔧 功能说明：
   * - 查询技能升级记录
   * - 支持时间范围筛选
   * - 显示资源消耗统计
   * - 用于数据分析和回溯
   * 
   * 对应old项目: beliefService.getBeliefSkillUpgradeHistory
   */
  @MessagePattern('beliefSkill.getUpgradeHistory')
  @Cacheable({
    key: 'beliefSkill:history:#{payload.characterId}:#{payload.page || 1}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60 * 60 // 1小时缓存
  })
  async getBeliefSkillUpgradeHistory(payload: GetBeliefSkillUpgradeHistoryPayloadDto) {
    const result = await this.beliefSkillService.getBeliefSkillUpgradeHistory(payload);
    return this.fromResult(result, '获取升级历史成功');
  }
}
