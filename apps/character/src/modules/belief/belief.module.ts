import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BeliefController } from './belief.controller';
import { BeliefSkillController } from './belief-skill.controller';
import { BeliefService } from './belief.service';
import { BeliefSkillService } from './belief-skill.service';
import { Belief, BeliefSchema } from './schemas/belief.schema';
import { BeliefRepository } from './repositories/belief.repository';

// 导入共享模块
import { GameConfigModule } from '@libs/game-config';
import { ServiceMeshModule } from '@libs/service-mesh';
import { RedisModule } from '@libs/redis';

// 导入Character模块（避免循环依赖）
import { CharacterModule } from '../character/character.module';

/**
 * 信仰系统模块
 * 基于old项目的完整信仰系统实现
 * 
 * 🎯 核心功能：
 * - 信仰创建和管理
 * - 信仰成员管理
 * - 信仰捐献系统
 * - 信仰技能系统
 * - 信仰等级管理
 * - 信仰领导者管理
 * 
 * 🔗 依赖关系：
 * - Character模块：角色数据和资源管理
 * - GameConfig模块：配置数据
 * - ServiceMesh模块：跨服务通信
 * - Redis模块：缓存和排行榜
 */
@Module({
  imports: [
    // MongoDB Schema注册
    MongooseModule.forFeature([
      { name: Belief.name, schema: BeliefSchema },
    ]),
    
    // 共享模块
    GameConfigModule,
    ServiceMeshModule,
    RedisModule,
    
    // Character模块（使用forwardRef避免循环依赖）
    forwardRef(() => CharacterModule),
  ],
  controllers: [
    BeliefController,
    BeliefSkillController,
  ],
  providers: [
    BeliefService,
    BeliefSkillService,
    BeliefRepository,
  ],
  exports: [
    BeliefService,
    BeliefSkillService,
    BeliefRepository,
  ],
})
export class BeliefModule {}
