import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { <PERSON>iefController } from './belief.controller';
import { BeliefService } from './belief.service';
import { Belief, BeliefSchema } from './schemas/belief.schema';
import { BeliefRepository } from '../../common/repositories/belief.repository';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Belief.name, schema: BeliefSchema }]),
  ],
  controllers: [BeliefController],
  providers: [BeliefService, BeliefRepository],
  exports: [BeliefService],
})
export class BeliefModule {}
