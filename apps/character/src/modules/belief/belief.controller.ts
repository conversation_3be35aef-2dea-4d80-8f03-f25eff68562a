import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BaseController } from '@libs/common/controller';
import { BeliefService } from './belief.service';
import { GetBeliefInfoPayloadDto, DonateBeliefPayloadDto, UpgradeBeliefSkillPayloadDto } from './dto/belief-payload.dto';

@Controller()
export class BeliefController extends BaseController {
  constructor(private readonly beliefService: BeliefService) {
    super('BeliefController');
  }

  @MessagePattern('belief.getInfo')
  async getBeliefInfo(@Payload() payload: GetBeliefInfoPayloadDto) {
    const validationResult = await this.validatePayloadWithDto(payload, GetBeliefInfoPayloadDto);
    if (validationResult.success === false) {
      return this.fromResult(validationResult);
    }
    const result = await this.beliefService.getBeliefInfo(validationResult.data.beliefId);
    return this.fromResult(result);
  }

  @MessagePattern('belief.donate')
  async donateBeliefGold(@Payload() payload: DonateBeliefPayloadDto) {
    const validationResult = await this.validatePayloadWithDto(payload, DonateBeliefPayloadDto);
    if (validationResult.success === false) {
      return this.fromResult(validationResult);
    }

    const result = await this.beliefService.donateBeliefGold(validationResult.data);
    return this.fromResult(result);
  }

  @MessagePattern('belief.upgradeSkill')
  async upgradeBeliefSkill(@Payload() payload: UpgradeBeliefSkillPayloadDto) {
    const validationResult = await this.validatePayloadWithDto(payload, UpgradeBeliefSkillPayloadDto);
    if (validationResult.success === false) {
      return this.fromResult(validationResult);
    }

    const result = await this.beliefService.upgradeBeliefSkill(validationResult.data);
    return this.fromResult(result);
  }
}
