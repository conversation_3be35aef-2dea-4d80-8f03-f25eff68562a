import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BeliefService } from './belief.service';
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import {
  CreateBeliefPayloadDto,
  JoinBeliefPayloadDto,
  LeaveBeliefPayloadDto,
  DonateBeliefPayloadDto,
  UpdateBeliefNoticePayloadDto,
  GetBeliefInfoPayloadDto,
  GetBeliefListPayloadDto,
  SetBeliefLeaderPayloadDto,
  GetBeliefMembersPayloadDto,
  GetBeliefRankPayloadDto
} from '../../common/dto/belief-payload.dto';

/**
 * 信仰系统控制器
 * 严格基于old项目的信仰系统接口设计
 * 
 * 核心接口：
 * - belief.create: 创建信仰
 * - belief.join: 加入信仰
 * - belief.leave: 退出信仰
 * - belief.donate: 信仰捐献
 * - belief.getInfo: 获取信仰信息
 * - belief.getList: 获取信仰列表
 * - belief.updateNotice: 更新信仰公告
 * - belief.setLeader: 设置领导者
 * - belief.getMembers: 获取成员列表
 * - belief.getRank: 获取信仰排行
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class BeliefController extends BaseController {
  constructor(private readonly beliefService: BeliefService) {
    super('BeliefController');
  }

  /**
   * 创建信仰
   * 基于old项目: 玩家创建新的信仰组织
   */
  @MessagePattern('belief.create')
  async createBelief(payload: CreateBeliefPayloadDto) {
    return this.handleRequest(async () => {
      return await this.beliefService.createBelief(payload);
    }, payload);
  }

  /**
   * 加入信仰
   * 基于old项目: 玩家申请加入指定信仰
   */
  @MessagePattern('belief.join')
  async joinBelief(payload: JoinBeliefPayloadDto & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.joinBelief(dto);
    });
  }

  /**
   * 退出信仰
   * 基于old项目: 玩家主动退出当前信仰
   */
  @MessagePattern('belief.leave')
  async leaveBelief(payload: LeaveBeliefPayloadDto & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.leaveBelief(dto);
    });
  }

  /**
   * 信仰捐献
   * 基于old项目: donateBeliefGold接口
   */
  @MessagePattern('belief.donate')
  async donateBelief(payload: DonateBeliefPayloadDto & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.donateBelief(dto);
    });
  }

  /**
   * 获取信仰信息
   * 基于old项目: 获取指定信仰的详细信息
   */
  @MessagePattern('belief.getInfo')
  async getBeliefInfo(payload: GetBeliefInfoPayloadDto & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.getBeliefInfo(dto);
    });
  }

  /**
   * 获取信仰列表
   * 基于old项目: 获取信仰列表（支持分页和搜索）
   */
  @MessagePattern('belief.getList')
  async getBeliefList(payload: GetBeliefListPayloadDto & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.getBeliefList(dto);
    });
  }

  /**
   * 更新信仰公告
   * 基于old项目: 董事长更新信仰公告
   */
  @MessagePattern('belief.updateNotice')
  async updateBeliefNotice(payload: UpdateBeliefNoticePayloadDto & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.updateBeliefNotice(dto);
    });
  }

  /**
   * 设置信仰领导者
   * 基于old项目: 董事长任命或撤销领导职位
   */
  @MessagePattern('belief.setLeader')
  async setBeliefLeader(payload: SetBeliefLeaderPayloadDto & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.setBeliefLeader(dto);
    });
  }

  /**
   * 获取信仰成员列表
   * 基于old项目: 获取信仰的所有成员信息
   */
  @MessagePattern('belief.getMembers')
  async getBeliefMembers(payload: GetBeliefMembersPayloadDto & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.getBeliefMembers(dto);
    });
  }

  /**
   * 获取信仰排行榜
   * 基于old项目: 获取信仰等级和活跃度排行
   */
  @MessagePattern('belief.getRank')
  async getBeliefRank(payload: GetBeliefRankPayloadDto & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.getBeliefRank(dto);
    });
  }

  /**
   * 获取我的信仰信息
   * 基于old项目: 获取角色当前加入的信仰信息
   */
  @MessagePattern('belief.getMyBelief')
  async getMyBelief(payload: { characterId: string; serverId: string } & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.getMyBelief(dto);
    });
  }

  /**
   * 搜索信仰
   * 基于old项目: 按名称搜索信仰
   */
  @MessagePattern('belief.search')
  async searchBelief(payload: { characterId: string; serverId: string; keyword: string } & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.searchBelief(dto);
    });
  }

  /**
   * 获取信仰统计信息
   * 基于old项目: 获取信仰的各种统计数据
   */
  @MessagePattern('belief.getStats')
  async getBeliefStats(payload: { characterId: string; serverId: string; beliefId?: number } & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.getBeliefStats(dto);
    });
  }

  /**
   * 踢出信仰成员
   * 基于old项目: 董事长踢出信仰成员
   */
  @MessagePattern('belief.kickMember')
  async kickBeliefMember(payload: {
    characterId: string;
    serverId: string;
    targetPlayerId: string;
    reason?: string
  } & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.kickBeliefMember(dto);
    });
  }

  /**
   * 解散信仰
   * 基于old项目: 董事长解散信仰
   */
  @MessagePattern('belief.disband')
  async disbandBelief(payload: {
    characterId: string;
    serverId: string;
    reason?: string
  } & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.disbandBelief(dto);
    });
  }

  /**
   * 转让董事长职位
   * 基于old项目: 董事长转让职位给其他成员
   */
  @MessagePattern('belief.transferChairman')
  async transferChairman(payload: {
    characterId: string;
    serverId: string;
    targetPlayerId: string
  } & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.transferChairman(dto);
    });
  }

  /**
   * 获取信仰动态
   * 基于old项目: 获取信仰的最新动态消息
   */
  @MessagePattern('belief.getNotifications')
  async getBeliefNotifications(payload: {
    characterId: string;
    serverId: string;
    beliefId?: number;
    page?: number;
    limit?: number;
  } & BasePayload) {
    return this.handleRequest(payload, async (dto) => {
      return await this.beliefService.getBeliefNotifications(dto);
    });
  }
}
