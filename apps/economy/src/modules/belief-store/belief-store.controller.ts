import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BeliefStoreService } from './belief-store.service';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import { Cacheable, CacheEvict } from '@libs/redis';
import {
  GetBeliefStoreInfoPayloadDto,
  SetBeliefStorePricePayloadDto,
  BuyBeliefStoreItemPayloadDto,
  GetBeliefStoreItemListPayloadDto,
  GetBeliefStoreBuyHistoryPayloadDto,
  GetBeliefStoreStatsPayloadDto,
  RefreshBeliefStorePayloadDto,
  SwitchBeliefStorePhasePayloadDto,
  PreviewBeliefStoreBuyPayloadDto
} from '../../common/dto/belief-store-payload.dto';

/**
 * 信仰商店控制器
 * 基于old项目的完整信仰商店系统实现
 * 
 * 🎯 核心功能：
 * - 信仰商店定价系统（董事长定价）
 * - 信仰商店购买系统
 * - 商品刷新和限购管理
 * - 定价阶段和开售阶段切换
 * - 信仰商品折扣机制
 * 
 * 🚀 架构特性：
 * - 继承BaseController的标准化处理
 * - WebSocket微服务接口(@MessagePattern)
 * - Redis缓存装饰器(@Cacheable/@CacheEvict)
 * - 统一的XResponse响应格式
 * - 完整的日志记录和性能监控
 * 
 * old项目对应：
 * - beliefService.js中的商店相关方法
 * - FaithStore.json配置表
 * - 定价和购买的完整业务流程
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class BeliefStoreController extends BaseController {
  constructor(
    private readonly beliefStoreService: BeliefStoreService,
  ) {
    super('BeliefStoreController');
  }

  /**
   * 获取信仰商店状态
   * 基于old项目: getFaithStoreInfo接口
   * 
   * 🔧 功能说明：
   * - 获取当前商店阶段（定价/开售）
   * - 显示商品列表和价格
   * - 显示购买统计信息
   * - 显示阶段剩余时间
   * 
   * 对应old项目: beliefService.getFaithStoreInfo
   */
  @MessagePattern('beliefStore.getInfo')
  @Cacheable({
    key: 'beliefStore:info:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 5 * 60 // 5分钟缓存
  })
  async getBeliefStoreInfo(payload: GetBeliefStoreInfoPayloadDto) {
    const result = await this.beliefStoreService.getBeliefStoreInfo(payload);
    return this.fromResult(result, '获取信仰商店信息成功');
  }

  /**
   * 董事长设置商品定价
   * 基于old项目: setFaithStorePrice接口
   * 
   * 🔧 功能说明：
   * - 董事长为3个不同信仰设置价格档位
   * - 价格档位：50%、200%、300%
   * - 不能设置重复信仰和重复价格
   * - 绑定商品一起设置价格
   * 
   * 对应old项目: beliefService.setFaithStorePrice
   */
  @MessagePattern('beliefStore.setPrice')
  @CacheEvict({
    key: 'beliefStore:*:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async setBeliefStorePrice(payload: SetBeliefStorePricePayloadDto) {
    const result = await this.beliefStoreService.setBeliefStorePrice(payload);
    return this.fromResult(result, '设置商品定价成功');
  }

  /**
   * 购买信仰商店商品
   * 基于old项目: buyFaithStoreItem接口
   * 
   * 🔧 功能说明：
   * - 使用信仰积分购买商品
   * - 本信仰商品半价优惠（除非董事长特别设置）
   * - 检查个人限购和全服限购
   * - 检查信仰等级限制
   * 
   * 对应old项目: beliefService.buyFaithStoreItem
   */
  @MessagePattern('beliefStore.buyItem')
  @CacheEvict({
    key: 'beliefStore:*:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyBeliefStoreItem(payload: BuyBeliefStoreItemPayloadDto) {
    const result = await this.beliefStoreService.buyBeliefStoreItem(payload);
    return this.fromResult(result, '购买商品成功');
  }

  /**
   * 获取信仰商店商品列表
   * 基于old项目: getFaithStoreItemList接口
   * 
   * 🔧 功能说明：
   * - 获取当前期数的商品列表
   * - 显示商品价格和库存
   * - 显示个人购买记录
   * - 显示限购信息
   * 
   * 对应old项目: beliefService.getFaithStoreItemList
   */
  @MessagePattern('beliefStore.getItemList')
  @Cacheable({
    key: 'beliefStore:items:#{payload.serverId}:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 10 * 60 // 10分钟缓存
  })
  async getBeliefStoreItemList(payload: GetBeliefStoreItemListPayloadDto) {
    const result = await this.beliefStoreService.getBeliefStoreItemList(payload);
    return this.fromResult(result, '获取商品列表成功');
  }

  /**
   * 获取信仰商店购买历史
   * 基于old项目: getFaithStoreBuyHistory接口
   * 
   * 🔧 功能说明：
   * - 查询玩家购买记录
   * - 支持时间范围筛选
   * - 显示消耗统计
   * - 用于数据分析
   * 
   * 对应old项目: beliefService.getFaithStoreBuyHistory
   */
  @MessagePattern('beliefStore.getBuyHistory')
  @Cacheable({
    key: 'beliefStore:history:#{payload.characterId}:#{payload.page || 1}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 30 * 60 // 30分钟缓存
  })
  async getBeliefStoreBuyHistory(payload: GetBeliefStoreBuyHistoryPayloadDto) {
    const result = await this.beliefStoreService.getBeliefStoreBuyHistory(payload);
    return this.fromResult(result, '获取购买历史成功');
  }

  /**
   * 获取信仰商店统计信息
   * 基于old项目: getFaithStoreStats接口
   * 
   * 🔧 功能说明：
   * - 获取商店销售统计
   * - 显示热门商品排行
   * - 显示收益分配情况
   * - 用于运营分析
   * 
   * 对应old项目: beliefService.getFaithStoreStats
   */
  @MessagePattern('beliefStore.getStats')
  @Cacheable({
    key: 'beliefStore:stats:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60 * 60 // 1小时缓存
  })
  async getBeliefStoreStats(payload: GetBeliefStoreStatsPayloadDto) {
    const result = await this.beliefStoreService.getBeliefStoreStats(payload);
    return this.fromResult(result, '获取商店统计成功');
  }

  /**
   * 刷新信仰商店商品
   * 基于old项目: refreshFaithStore接口
   * 
   * 🔧 功能说明：
   * - 每日、每周、每月刷新
   * - 重置限购数量
   * - 更新商品可用性
   * - 切换商店阶段
   * 
   * 对应old项目: beliefService.refreshFaithStore
   */
  @MessagePattern('beliefStore.refresh')
  @CacheEvict({
    key: 'beliefStore:*:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async refreshBeliefStore(payload: RefreshBeliefStorePayloadDto) {
    const result = await this.beliefStoreService.refreshBeliefStore(payload);
    return this.fromResult(result, '刷新商店成功');
  }

  /**
   * 切换商店阶段
   * 基于old项目: switchFaithStorePhase接口
   * 
   * 🔧 功能说明：
   * - 在定价阶段和开售阶段之间切换
   * - 自动或手动触发
   * - 更新阶段时间戳
   * - 清理过期数据
   * 
   * 对应old项目: beliefService.switchFaithStorePhase
   */
  @MessagePattern('beliefStore.switchPhase')
  @CacheEvict({
    key: 'beliefStore:*:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async switchBeliefStorePhase(payload: SwitchBeliefStorePhasePayloadDto) {
    const result = await this.beliefStoreService.switchBeliefStorePhase(payload);
    return this.fromResult(result, '切换商店阶段成功');
  }

  /**
   * 预览商品购买
   * 基于old项目: previewFaithStoreBuy接口
   * 
   * 🔧 功能说明：
   * - 预览购买商品的消耗和收益
   * - 检查购买条件和限制
   * - 计算折扣和优惠
   * - 不实际执行购买
   * 
   * 对应old项目: beliefService.previewFaithStoreBuy
   */
  @MessagePattern('beliefStore.previewBuy')
  async previewBeliefStoreBuy(payload: PreviewBeliefStoreBuyPayloadDto) {
    const result = await this.beliefStoreService.previewBeliefStoreBuy(payload);
    return this.fromResult(result, '预览购买成功');
  }
}
