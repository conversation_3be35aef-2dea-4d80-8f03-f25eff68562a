import { Injectable, Logger } from '@nestjs/common';
import { BaseService } from '@libs/common/service/base-service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { BeliefStoreRepository } from './repositories/belief-store.repository';
import { GameConfigFacade } from '@libs/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';

/**
 * 信仰商店业务逻辑层
 * 基于old项目的完整信仰商店系统实现
 * 
 * 🎯 核心功能：
 * - 信仰商店定价系统（董事长定价）
 * - 信仰商店购买系统
 * - 商品刷新和限购管理
 * - 定价阶段和开售阶段切换
 * - 信仰商品折扣机制
 * 
 * 🔧 业务规则：
 * - 董事长可为3个不同信仰设置价格档位（50%、200%、300%）
 * - 本信仰商品享受半价优惠（除非董事长特别设置）
 * - 支持个人限购和全服限购
 * - 信仰等级限制商品购买
 * - 双阶段循环：定价→开售→定价...
 * 
 * old项目对应：
 * - beliefService.js中的商店相关方法
 * - FaithStore.json配置表
 * - 定价和购买的完整业务流程
 */
@Injectable()
export class BeliefStoreService extends BaseService {
  constructor(
    private readonly beliefStoreRepository: BeliefStoreRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient: MicroserviceClientService,
  ) {
    super('BeliefStoreService');
  }

  /**
   * 获取信仰商店信息
   */
  async getBeliefStoreInfo(dto: { characterId: string; serverId: string }): Promise<XResult<any>> {
    this.logger.log(`获取信仰商店信息: ${dto.characterId}`);

    try {
      // 1. 获取商店状态
      const storeResult = await this.beliefStoreRepository.getCurrentStore();
      if (XResultUtils.isFailure(storeResult)) {
        return XResultUtils.error(`获取商店状态失败: ${storeResult.message}`, storeResult.code);
      }

      const store = storeResult.data;
      if (!store) {
        // 初始化商店
        const initResult = await this.initializeBeliefStore();
        if (XResultUtils.isFailure(initResult)) {
          return XResultUtils.error(`初始化商店失败: ${initResult.message}`, initResult.code);
        }
        return await this.getBeliefStoreInfo(dto);
      }

      // 2. 获取玩家信仰信息
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取玩家信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const playerBelief = beliefResult.data;

      // 3. 计算商品价格（考虑玩家信仰折扣）
      const itemsWithPrices = await this.calculateItemPrices(store.itemList, playerBelief?.beliefId);

      // 4. 计算阶段剩余时间
      const phaseTimeLeft = this.calculatePhaseTimeLeft(store);

      return XResultUtils.ok({
        phase: store.phase,
        shopPeriod: store.shopPeriod,
        phaseTimeLeft,
        itemList: itemsWithPrices,
        buyNum: store.buyNum,
        playerBelief: playerBelief ? {
          beliefId: playerBelief.beliefId,
          beliefName: playerBelief.name,
          beliefNum: playerBelief.myContribution || 0
        } : null
      });

    } catch (error) {
      this.logger.error(`获取信仰商店信息失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰商店信息失败: ${error.message}`, 'GET_STORE_INFO_ERROR');
    }
  }

  /**
   * 董事长设置商品定价
   */
  async setBeliefStorePrice(dto: {
    characterId: string;
    serverId: string;
    itemId: number;
    priceList: Array<{ beliefId: number; price: 50 | 200 | 300 }>;
  }): Promise<XResult<any>> {
    this.logger.log(`设置商品定价: ${dto.characterId}, 商品${dto.itemId}`);

    try {
      // 1. 验证玩家是否为董事长
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取玩家信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const playerBelief = beliefResult.data;
      if (!playerBelief || playerBelief.myPosition !== 1) {
        return XResultUtils.error('只有董事长可以设置商品定价', 'PERMISSION_DENIED');
      }

      // 2. 验证商店是否处于定价阶段
      const storeResult = await this.beliefStoreRepository.getCurrentStore();
      if (XResultUtils.isFailure(storeResult)) {
        return XResultUtils.error(`获取商店状态失败: ${storeResult.message}`, storeResult.code);
      }

      const store = storeResult.data;
      if (!store || store.shopPeriod !== 1) {
        return XResultUtils.error('当前不是定价阶段，无法设置价格', 'INVALID_PHASE');
      }

      // 3. 验证定价规则
      const validationResult = this.validatePricing(dto.priceList);
      if (XResultUtils.isFailure(validationResult)) {
        return validationResult;
      }

      // 4. 转换定价数据格式
      const pricingList = dto.priceList.map(p => ({
        selfId: playerBelief.beliefId,
        beliefId: p.beliefId,
        price: p.price
      }));

      // 5. 更新商品定价
      const updateResult = await this.beliefStoreRepository.updateItemPricing(dto.itemId, pricingList);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新商品定价失败: ${updateResult.message}`, updateResult.code);
      }

      // 6. 记录定价历史
      await this.beliefStoreRepository.addPricingRecord(dto.itemId, playerBelief.beliefId, pricingList);

      return XResultUtils.ok({
        itemId: dto.itemId,
        priceList: dto.priceList,
        setTime: Date.now()
      });

    } catch (error) {
      this.logger.error(`设置商品定价失败: ${error.message}`, error.stack);
      return XResultUtils.error(`设置商品定价失败: ${error.message}`, 'SET_PRICE_ERROR');
    }
  }

  /**
   * 购买信仰商店商品
   */
  async buyBeliefStoreItem(dto: {
    characterId: string;
    serverId: string;
    itemId: number;
    quantity: number;
    beliefId: number;
  }): Promise<XResult<any>> {
    this.logger.log(`购买信仰商店商品: ${dto.characterId}, 商品${dto.itemId}, 数量${dto.quantity}`);

    try {
      // 1. 验证商店是否处于开售阶段
      const storeResult = await this.beliefStoreRepository.getCurrentStore();
      if (XResultUtils.isFailure(storeResult)) {
        return XResultUtils.error(`获取商店状态失败: ${storeResult.message}`, storeResult.code);
      }

      const store = storeResult.data;
      if (!store || store.shopPeriod !== 2) {
        return XResultUtils.error('当前不是开售阶段，无法购买商品', 'INVALID_PHASE');
      }

      // 2. 获取玩家信仰信息
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取玩家信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const playerBelief = beliefResult.data;
      if (!playerBelief) {
        return XResultUtils.error('玩家未加入任何信仰，无法购买商品', 'NO_BELIEF');
      }

      // 3. 验证购买条件
      const validationResult = await this.validatePurchase(dto, store, playerBelief);
      if (XResultUtils.isFailure(validationResult)) {
        return validationResult;
      }

      // 4. 计算购买价格
      const priceResult = await this.calculatePurchasePrice(dto, store, playerBelief);
      if (XResultUtils.isFailure(priceResult)) {
        return priceResult;
      }

      const { totalCost, discount } = priceResult.data;

      // 5. 扣除信仰积分
      const deductResult = await this.microserviceClient.call('character', 'character.deductCurrency', {
        characterId: dto.characterId,
        serverId: dto.serverId,
        currencyType: 'beliefNum',
        amount: totalCost,
        reason: 'belief_store_purchase'
      });

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除信仰积分失败: ${deductResult.message}`, deductResult.code);
      }

      // 6. 发放商品
      const itemResult = await this.microserviceClient.call('character', 'inventory.addItem', {
        characterId: dto.characterId,
        serverId: dto.serverId,
        itemId: dto.itemId,
        quantity: dto.quantity,
        reason: 'belief_store_purchase'
      });

      if (XResultUtils.isFailure(itemResult)) {
        // 购买失败，退还信仰积分
        await this.microserviceClient.call('character', 'character.addCurrency', {
          characterId: dto.characterId,
          serverId: dto.serverId,
          currencyType: 'beliefNum',
          amount: totalCost,
          reason: 'belief_store_refund'
        });
        return XResultUtils.error(`发放商品失败: ${itemResult.message}`, itemResult.code);
      }

      // 7. 更新购买记录
      await this.beliefStoreRepository.addPurchaseRecord(
        dto.characterId,
        playerBelief.beliefId,
        dto.itemId,
        dto.quantity,
        totalCost
      );

      // 8. 更新商品购买统计
      await this.beliefStoreRepository.updateItemPurchaseCount(dto.itemId, dto.quantity);

      return XResultUtils.ok({
        itemId: dto.itemId,
        quantity: dto.quantity,
        totalCost,
        discount,
        beliefNumLeft: playerBelief.myContribution - totalCost
      });

    } catch (error) {
      this.logger.error(`购买信仰商店商品失败: ${error.message}`, error.stack);
      return XResultUtils.error(`购买信仰商店商品失败: ${error.message}`, 'BUY_ITEM_ERROR');
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 初始化信仰商店
   */
  private async initializeBeliefStore(): Promise<XResult<any>> {
    try {
      const storeData = {
        shopPeriod: 1, // 从定价阶段开始
        phase: 1,
        itemList: [],
        pricingInfo: [],
        buyInfo: [],
        buyUidMap: new Map(),
        buyNum: 0,
        lastUpdateTime: Date.now(),
        phaseStartTime: Date.now(),
        phaseDuration: 24 * 60 * 60 * 1000 // 24小时
      };

      return await this.beliefStoreRepository.createOne(storeData);
    } catch (error) {
      return XResultUtils.error(`初始化信仰商店失败: ${error.message}`, 'INIT_STORE_ERROR');
    }
  }

  /**
   * 计算商品价格（考虑信仰折扣）
   * 基于old项目: BeliefShopPurchase中的价格计算逻辑
   */
  private async calculateItemPrices(itemList: any[], playerBeliefId?: number): Promise<any[]> {
    const faithStoreConfig = await this.gameConfig.faithStore.getAll();

    return itemList.map(item => {
      const config = faithStoreConfig.find(c => c.parameters === item.itemId);
      if (!config) {
        return { ...item, finalPrice: 0, canPurchase: false };
      }

      let finalPrice = config.price;
      let discount = 0;
      let isSet = false;

      // 1. 检查董事长定价
      const pricing = item.itemList.find((p: any) => p.beliefId === playerBeliefId);
      if (pricing) {
        finalPrice = Math.floor(config.price * pricing.price / 100);
        discount = 100 - pricing.price;
        isSet = true;
      }

      // 2. 本信仰商品半价优惠（如果没有董事长特别设置）
      if (this.isBeliefItem(item.itemId, playerBeliefId) && !isSet) {
        finalPrice = Math.floor(finalPrice / 2);
        discount = 50;
      }

      return {
        ...item,
        basePrice: config.price,
        finalPrice,
        discount,
        canPurchase: true,
        beliefLevel: config.lv || 0,
        customer: config.customer || 0,
        purchase: config.purchase || 0
      };
    });
  }

  /**
   * 计算单个商品的最终价格
   */
  private calculateItemFinalPrice(item: any, playerBeliefId?: number): number {
    // TODO: 实现具体的价格计算逻辑
    // 考虑信仰折扣、董事长定价等因素
    return item.basePrice || 100;
  }

  /**
   * 计算阶段剩余时间
   */
  private calculatePhaseTimeLeft(store: any): number {
    const elapsed = Date.now() - store.phaseStartTime;
    const remaining = store.phaseDuration - elapsed;
    return Math.max(0, remaining);
  }

  /**
   * 验证定价规则
   */
  private validatePricing(priceList: Array<{ beliefId: number; price: 50 | 200 | 300 }>): XResult<void> {
    // 1. 检查是否设置了3个不同的信仰
    if (priceList.length !== 3) {
      return XResultUtils.error('必须为3个不同的信仰设置价格', 'INVALID_PRICE_COUNT');
    }

    // 2. 检查信仰ID是否重复
    const beliefIds = priceList.map(p => p.beliefId);
    const uniqueBeliefIds = new Set(beliefIds);
    if (uniqueBeliefIds.size !== 3) {
      return XResultUtils.error('不能为同一个信仰设置多个价格', 'DUPLICATE_BELIEF');
    }

    // 3. 检查价格是否重复
    const prices = priceList.map(p => p.price);
    const uniquePrices = new Set(prices);
    if (uniquePrices.size !== 3) {
      return XResultUtils.error('不能设置重复的价格档位', 'DUPLICATE_PRICE');
    }

    // 4. 检查价格档位是否有效
    const validPrices = [50, 200, 300];
    for (const price of prices) {
      if (!validPrices.includes(price)) {
        return XResultUtils.error('价格档位只能是50%、200%、300%', 'INVALID_PRICE_TIER');
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证购买条件
   */
  private async validatePurchase(dto: any, store: any, playerBelief: any): Promise<XResult<void>> {
    // TODO: 实现购买条件验证
    // 1. 检查商品是否存在
    // 2. 检查库存是否充足
    // 3. 检查个人限购
    // 4. 检查全服限购
    // 5. 检查信仰等级限制
    return XResultUtils.ok(undefined);
  }

  /**
   * 计算购买价格
   */
  private async calculatePurchasePrice(dto: any, store: any, playerBelief: any): Promise<XResult<{ totalCost: number; discount: number }>> {
    // TODO: 实现价格计算逻辑
    // 1. 获取基础价格
    // 2. 应用信仰折扣
    // 3. 应用董事长定价
    // 4. 计算最终价格
    return XResultUtils.ok({
      totalCost: 100,
      discount: 0
    });
  }

  /**
   * 获取信仰商店商品列表
   */
  async getBeliefStoreItemList(dto: { characterId: string; serverId: string }): Promise<XResult<any>> {
    // TODO: 实现商品列表获取逻辑
    return XResultUtils.ok([]);
  }

  /**
   * 获取信仰商店购买历史
   */
  async getBeliefStoreBuyHistory(dto: {
    characterId: string;
    serverId: string;
    page?: number;
    limit?: number;
    startTime?: number;
    endTime?: number;
  }): Promise<XResult<any>> {
    // TODO: 实现购买历史获取逻辑
    return XResultUtils.ok({ records: [], total: 0, page: 1, totalPages: 0 });
  }

  /**
   * 获取信仰商店统计信息
   */
  async getBeliefStoreStats(dto: { characterId: string; serverId: string }): Promise<XResult<any>> {
    // TODO: 实现统计信息获取逻辑
    return XResultUtils.ok({});
  }

  /**
   * 刷新信仰商店商品
   */
  async refreshBeliefStore(dto: {
    serverId: string;
    refreshType: 'daily' | 'weekly' | 'monthly' | 'phase';
  }): Promise<XResult<any>> {
    // TODO: 实现商店刷新逻辑
    return XResultUtils.ok({});
  }

  /**
   * 切换商店阶段
   */
  async switchBeliefStorePhase(dto: {
    serverId: string;
    targetPhase?: 1 | 2;
    duration?: number;
  }): Promise<XResult<any>> {
    // TODO: 实现阶段切换逻辑
    return XResultUtils.ok({});
  }

  /**
   * 预览商品购买
   */
  async previewBeliefStoreBuy(dto: {
    characterId: string;
    serverId: string;
    itemId: number;
    quantity: number;
    beliefId: number;
  }): Promise<XResult<any>> {
    // TODO: 实现购买预览逻辑
    return XResultUtils.ok({});
  }
}
