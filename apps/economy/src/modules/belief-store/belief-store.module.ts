import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BeliefStoreController } from './belief-store.controller';
import { BeliefStoreService } from './belief-store.service';
import { BeliefStore, BeliefStoreSchema } from './schemas/belief-store.schema';
import { BeliefStoreRepository } from './repositories/belief-store.repository';

// 导入共享模块
import { GameConfigModule } from '@libs/game-config';
import { ServiceMeshModule } from '@libs/service-mesh';
import { RedisModule } from '@libs/redis';

/**
 * 信仰商店系统模块
 * 基于old项目的完整信仰商店实现
 * 
 * 🎯 核心功能：
 * - 信仰商店定价系统（董事长定价）
 * - 信仰商店购买系统
 * - 商品刷新和限购管理
 * - 定价阶段和开售阶段切换
 * - 信仰商品折扣机制
 * 
 * 🔗 依赖关系：
 * - Character服务：信仰信息和玩家资源
 * - GameConfig模块：商店配置数据
 * - ServiceMesh模块：跨服务通信
 * - Redis模块：缓存和状态管理
 * 
 * old项目对应：
 * - beliefService.js中的商店相关方法
 * - FaithStore.json配置表
 * - 定价和购买的完整业务流程
 */
@Module({
  imports: [
    // MongoDB Schema注册
    MongooseModule.forFeature([
      { name: BeliefStore.name, schema: BeliefStoreSchema },
    ]),
    
    // 共享模块
    GameConfigModule,
    RedisModule,
  ],
  controllers: [
    BeliefStoreController,
  ],
  providers: [
    BeliefStoreService,
    BeliefStoreRepository,
  ],
  exports: [
    BeliefStoreService,
    BeliefStoreRepository,
  ],
})
export class BeliefStoreModule {}
