/*
 * @Author: colorinstance <EMAIL>
 * @Date: 2025-08-19 22:37:25
 * @LastEditors: colorinstance <EMAIL>
 * @LastEditTime: 2025-09-06 20:26:48
 * @FilePath: \server-new\apps\economy\src\modules\payment\payment.controller.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { PaymentService } from './payment.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  ProcessPaymentPayloadDto, VerifyPaymentPayloadDto
} from "@economy/common/dto/payment-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class PaymentController extends BaseController {
  constructor(private readonly paymentService: PaymentService) {
    super('PaymentController');
  }

  /**
   * 处理支付
   */
  @MessagePattern('payment.process')
  async processPayment(@Payload() payload: ProcessPaymentPayloadDto): Promise<XResponse<any>> {
    this.logger.log('处理支付请求');
    const result = await this.paymentService.processPayment(payload);
    return this.fromResult(result);
  }

  /**
   * 验证支付
   */
  @MessagePattern('payment.verify')
  async verifyPayment(@Payload() payload: VerifyPaymentPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`验证支付: ${payload.transactionId}`);
    const result = await this.paymentService.verifyPayment(payload.transactionId);
    return this.fromResult(result);
  }
}
