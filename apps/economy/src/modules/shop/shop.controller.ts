import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ShopService } from './shop.service';

import { ShopType, RefreshCycle } from '@economy/common/schemas/shop.schema';
import { Cacheable, CacheEvict, CachePut } from '@libs/redis';


import { XResponse } from '@libs/common/types/result.type';

import {
  BatchRefreshShopsPayloadDto,
  BuyMonthCardPayloadDto, ClaimMonthCardPayloadDto, GetLimitShopPayloadDto, GetPurchaseHistoryPayloadDto,
  GetShopInfoPayloadDto,
  GetShopListPayloadDto, GetShopStatsPayloadDto, GetVipShopPayloadDto,
  PurchaseGoodsPayloadDto, RefreshShopPayloadDto
} from "@economy/common/dto/shop-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class ShopController extends BaseController {
  constructor(private readonly shopService: ShopService) {
    super('ShopController');
  }

  // ==================== 商店信息管理 ====================

  /**
   * 获取商店信息
   */
  @MessagePattern('shop.getInfo')
  @Cacheable({ 
    key: 'shop:info:#{payload.characterId}:#{payload.shopType}',
    dataType: 'server',
    ttl: 300
  })
  async getShopInfo(@Payload() payload: GetShopInfoPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取商店信息: ${payload.characterId}, 类型: ${payload.shopType}`);
    const result = await this.shopService.getShopInfo(payload.characterId, payload.shopType);
    return this.fromResult(result);
  }

  /**
   * 获取商店列表
   */
  @MessagePattern('shop.getList')
  @Cacheable({ 
    key: 'shop:list:#{payload.characterId}',
    dataType: 'server',
    ttl: 300
  })
  async getShopList(@Payload() payload: GetShopListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取商店列表: ${payload.characterId}`);
    const result = await this.shopService.getShopList(payload);
    return this.fromResult(result);
  }

  // ==================== 商品购买 ====================

  /**
   * 购买商品
   */
  @MessagePattern('shop.purchase')
  @CacheEvict({ 
    key: 'shop:info:#{payload.characterId}:#{payload.purchaseDto.shopType}',
    dataType: 'server'
  })
  async purchaseGoods(@Payload() payload: PurchaseGoodsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`购买商品: ${payload.characterId}, 商品: ${payload.goodsId}`);
    const result = await this.shopService.purchaseGoods(
      payload
    );
    return this.fromResult(result);
  }

  /**
   * 刷新商店
   */
  @MessagePattern('shop.refresh')
  @CacheEvict({ 
    key: 'shop:info:#{payload.characterId}:#{payload.refreshDto.shopType}',
    dataType: 'server'
  })
  async refreshShop(@Payload() payload: RefreshShopPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`刷新商店: ${payload.characterId}, 类型: ${payload.shopType}`);
    const result = await this.shopService.refreshShop(payload);
    return this.fromResult(result);
  }

  // ==================== 月卡系统 ====================

  /**
   * 购买月卡
   */
  @MessagePattern('shop.buyMonthCard')
  @CacheEvict({ 
    key: 'shop:info:#{payload.characterId}:#{ShopType.NORMAL}',
    dataType: 'server'
  })
  async buyMonthCard(@Payload() payload: BuyMonthCardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`购买月卡: ${payload.characterId}, 类型: ${payload.cardType}`);
    const result = await this.shopService.buyMonthCard(payload);
    return this.fromResult(result);
  }

  /**
   * 领取月卡奖励
   */
  @MessagePattern('shop.claimMonthCard')
  @CacheEvict({ 
    key: 'shop:info:#{payload.claimDto.characterId}:#{ShopType.NORMAL}',
    dataType: 'server'
  })
  async claimMonthCardReward(@Payload() payload: ClaimMonthCardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`领取月卡奖励: ${payload.characterId}`);
    const result = await this.shopService.claimMonthCardReward(payload);
    return this.fromResult(result);
  }

  // ==================== VIP商店 ====================

  /**
   * 获取VIP商店信息
   */
  @MessagePattern('shop.getVipShop')
  @Cacheable({ 
    key: 'shop:vip:#{payload.characterId}:#{payload.vipLevel}',
    dataType: 'server',
    ttl: 600
  })
  async getVipShop(@Payload() payload: GetVipShopPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取VIP商店: ${payload.characterId}, VIP等级: ${payload.vipLevel}`);
    const result = await this.shopService.getShopInfo(payload.characterId, ShopType.VIP);
    return this.fromResult(result);
  }

  // ==================== 限时商店 ====================

  /**
   * 获取限时商店信息
   */
  @MessagePattern('shop.getLimitShop')
  @Cacheable({ 
    key: 'shop:limit:#{payload.characterId}:#{payload.activityId}',
    dataType: 'server',
    ttl: 180
  })
  async getLimitShop(@Payload() payload: GetLimitShopPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取限时商店: ${payload.characterId}, 活动: ${payload.activityId}`);
    const result = await this.shopService.getShopInfo(payload.characterId, ShopType.LIMIT);
    return this.fromResult(result);
  }

  // ==================== 购买历史和统计 ====================

  /**
   * 获取购买历史
   */
  @MessagePattern('shop.getPurchaseHistory')
  async getPurchaseHistory(@Payload() payload: GetPurchaseHistoryPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取购买历史: ${payload.characterId}`);
    const result = await this.shopService.getPurchaseHistory(payload);
    return this.fromResult(result);
  }

  /**
   * 获取商店统计
   */
  @MessagePattern('shop.getStats')
  @Cacheable({ 
    key: 'shop:stats:#{payload.characterId}:#{payload.days}',
    dataType: 'server',
    ttl: 1800
  })
  async getShopStats(@Payload() payload: GetShopStatsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取商店统计: ${payload.characterId}`);
    const result = await this.shopService.getShopStats(payload);
    return this.fromResult(result);
  }

  // ==================== 管理接口 ====================

  /**
   * 批量刷新商店（定时任务用）
   */
  @MessagePattern('shop.batchRefresh')
  async batchRefreshShops(@Payload() payload: BatchRefreshShopsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量刷新商店: 周期 ${payload.cycle}`);
    const result = await this.shopService.batchRefreshShops(payload.cycle);
    return this.fromResult(result);
  }
}
