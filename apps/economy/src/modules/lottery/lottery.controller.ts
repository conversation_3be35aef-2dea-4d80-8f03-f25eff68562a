import { Controller, Logger, UsePipes } from '@nestjs/common';
import {MessagePattern, Payload} from '@nestjs/microservices';
import { LotteryService } from './lottery.service';


import { XResponse } from '@libs/common/types/result.type';

import {
  GetLotteryConfigPayloadDto,
  GetLotteryHistoryPayloadDto,
  LotteryHeroPayloadDto
} from "@economy/common/dto/lottery-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

/**
 * 传统抽奖系统控制器
 * 基于old项目Player.prototype.lotteryHero的功能实现
 * 
 * 核心功能：
 * - 金币抽奖（单抽/十连抽）
 * - 代币抽奖（单抽/十连抽）
 * - 权重随机算法
 * - 抽奖历史记录
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class LotteryController extends BaseController {
  constructor(private readonly lotteryService: LotteryService) {
    super('LotteryController');
  }

  /**
   * 球员抽奖
   * 基于old项目: Player.prototype.lotteryHero
   *
   */
  @MessagePattern('lottery.lotteryHero')
  async lotteryHero(@Payload() payload: LotteryHeroPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球员抽奖请求: ${JSON.stringify(payload)}`);
    const result = await this.lotteryService.lotteryHero(
      payload.characterId,
      payload.type,
      payload.times
    );
    
    return this.fromResult(result);
  }

  /**
   * 获取抽奖配置信息
   *
   * @returns 抽奖配置
   * @param payload
   */
  @MessagePattern('lottery.getLotteryConfig')
  async getLotteryConfig(@Payload() payload: GetLotteryConfigPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取抽奖配置: ${payload}`);

    const result = await this.lotteryService.getLotteryConfig();

    return this.fromResult(result);
  }

  /**
   * 获取抽奖历史记录
   * 
   * @param payload 请求参数
   * @returns 抽奖历史
   */
  @MessagePattern('lottery.getLotteryHistory')
  async getLotteryHistory(@Payload() payload: GetLotteryHistoryPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取抽奖历史: ${payload.characterId}`);
    const result = await this.lotteryService.getLotteryHistory(
      payload.characterId,
      payload.page || 1,
      payload.limit || 10
    );

    return this.fromResult(result);
  }
}
