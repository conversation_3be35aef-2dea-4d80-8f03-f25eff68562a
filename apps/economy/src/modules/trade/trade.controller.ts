import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { TradeService } from './trade.service';


import {ConfirmTradePayloadDto, CreateTradePayloadDto} from "@economy/common/dto/trade-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

import { XResponse } from '@libs/common/types/result.type';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class TradeController extends BaseController {
  constructor(private readonly tradeService: TradeService) {
    super('TradeController');
  }

  /**
   * 创建交易
   */
  @MessagePattern('trade.create')
  async createTrade(@Payload() payload: CreateTradePayloadDto): Promise<XResponse<any>> {
    this.logger.log('创建交易');
    const result = await this.tradeService.createTrade(payload);
    return this.fromResult(result);
  }

  /**
   * 确认交易
   */
  @MessagePattern('trade.confirm')
  async confirmTrade(@Payload() payload: ConfirmTradePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`确认交易: ${payload.tradeId}`);
    const result = await this.tradeService.confirmTrade(payload.tradeId);
    return this.fromResult(result);
  }
}
