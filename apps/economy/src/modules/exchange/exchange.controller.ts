/**
 * 兑换大厅控制器
 * 基于old项目exchangeHall.js接口迁移
 */

import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { Cacheable, CacheEvict } from '@libs/redis';
import { ExchangeService } from './exchange.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  CompoundItemPayloadDto,
  DecomposeItemPayloadDto,
  ExchangeItemPayloadDto,
  GetExchangeInfoPayloadDto,
  GetExchangeStatsPayloadDto,
  RefreshExchangeHallPayloadDto
} from "@economy/common/dto/exchange-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class ExchangeController extends BaseController {
  constructor(private readonly exchangeService: ExchangeService) {
    super('ExchangeController');
  }

  /**
   * 获取兑换大厅信息
   * 对应old项目中的getExchangeHallInfo方法
   */
  @MessagePattern('exchange.getInfo')
  @Cacheable({
    key: 'exchange:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getExchangeHallInfo(@Payload() payload: GetExchangeInfoPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取兑换大厅信息: ${payload.uid}`);
    const result = await this.exchangeService.getExchangeHallInfo(payload.uid, payload.serverId);

    return this.fromResult(result);
  }

  /**
   * 合成物品
   * 对应old项目中的compoundItem方法
   */
  @MessagePattern('exchange.compound')
  @CacheEvict({
    key: 'exchange:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async compoundItem(@Payload() payload: CompoundItemPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`合成物品: ${payload.uid}, 物品: ${payload.resId}, 数量: ${payload.num}`);
    const result = await this.exchangeService.compoundItem(
      payload.uid, 
      payload.serverId, 
      payload.resId,
      payload.num
    );

    return this.fromResult(result);
  }

  /**
   * 分解物品
   * 对应old项目中的decomposeItem方法
   */
  @MessagePattern('exchange.decompose')
  @CacheEvict({
    key: 'exchange:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async decomposeItem(@Payload() payload: DecomposeItemPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`分解物品: ${payload.uid}, 物品: ${payload.resId}, 数量: ${payload.num}`);
    const result = await this.exchangeService.decomposeItem(
      payload.uid, 
      payload.serverId, 
      payload.resId,
      payload.num
    );

    return this.fromResult(result);
  }

  /**
   * 刷新兑换大厅
   * 对应old项目中的flushExchangeHall方法
   */
  @MessagePattern('exchange.refresh')
  @CacheEvict({
    key: 'exchange:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async flushExchangeHall(@Payload() payload: RefreshExchangeHallPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`刷新兑换大厅: ${payload.uid}, 类型: ${payload.type}`);
    const result = await this.exchangeService.flushExchangeHall(
      payload.uid, 
      payload.serverId, 
      payload.type,
      payload.teamId
    );

    return this.fromResult(result);
  }

  /**
   * 兑换物品
   * 对应old项目中的exchangeItem方法
   */
  @MessagePattern('exchange.exchangeItem')
  @CacheEvict({
    key: 'exchange:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async exchangeItem(@Payload() payload:ExchangeItemPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`兑换物品: ${payload.uid}, 兑换ID: ${payload.id}`);
    const result = await this.exchangeService.exchangeItem(
      payload.uid, 
      payload.serverId, 
      payload.id
    );

    return this.fromResult(result);
  }

  /**
   * 获取兑换统计信息（管理接口）
   */
  @MessagePattern('exchange.getStats')
  async getExchangeStats(@Payload() payload: GetExchangeStatsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取兑换统计信息');
    // TODO: 验证管理员权限
    // const stats = await this.exchangeService.getExchangeStats();
    return this.toSuccessResponse({
      totalPlayers: 0,
      totalCompounds: 0,
      totalDecomposes: 0,
      totalExchanges: 0,
    });
  }
}
