import { IsString, <PERSON><PERSON><PERSON>ber, Is<PERSON><PERSON>al, IsArray, ValidateNested, Min, Max, IsIn } from 'class-validator';
import { Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto';

/**
 * 获取信仰商店信息载荷DTO
 */
export class GetBeliefStoreInfoPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId
}

/**
 * 商品定价项DTO
 */
export class BeliefStorePricingItemDto {
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId: number;

  @IsNumber()
  @IsIn([50, 200, 300], { message: '价格档位只能是50、200、300' })
  price: 50 | 200 | 300;
}

/**
 * 设置信仰商店价格载荷DTO
 */
export class SetBeliefStorePricePayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '商品ID必须大于0' })
  itemId: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BeliefStorePricingItemDto)
  priceList: BeliefStorePricingItemDto[];
}

/**
 * 购买信仰商店商品载荷DTO
 */
export class BuyBeliefStoreItemPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '商品ID必须大于0' })
  itemId: number;

  @IsNumber()
  @Min(1, { message: '购买数量必须大于0' })
  @Max(999, { message: '购买数量不能超过999' })
  quantity: number;

  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId: number;
}

/**
 * 获取信仰商店商品列表载荷DTO
 */
export class GetBeliefStoreItemListPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId
}

/**
 * 获取信仰商店购买历史载荷DTO
 */
export class GetBeliefStoreBuyHistoryPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number;

  @IsOptional()
  @IsNumber()
  startTime?: number;

  @IsOptional()
  @IsNumber()
  endTime?: number;
}

/**
 * 获取信仰商店统计载荷DTO
 */
export class GetBeliefStoreStatsPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId
}

/**
 * 刷新信仰商店载荷DTO
 */
export class RefreshBeliefStorePayloadDto extends BasePayloadDto {
  @IsString()
  @IsIn(['daily', 'weekly', 'monthly', 'phase'], { message: '刷新类型无效' })
  refreshType: 'daily' | 'weekly' | 'monthly' | 'phase';
}

/**
 * 切换商店阶段载荷DTO
 */
export class SwitchBeliefStorePhasePayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @IsIn([1, 2], { message: '目标阶段只能是1或2' })
  targetPhase?: 1 | 2;

  @IsOptional()
  @IsNumber()
  @Min(1000, { message: '持续时间至少1秒' })
  duration?: number;
}

/**
 * 预览商品购买载荷DTO
 */
export class PreviewBeliefStoreBuyPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '商品ID必须大于0' })
  itemId: number;

  @IsNumber()
  @Min(1, { message: '购买数量必须大于0' })
  @Max(999, { message: '购买数量不能超过999' })
  quantity: number;

  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId: number;
}
