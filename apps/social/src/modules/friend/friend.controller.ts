import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { FriendService } from './friend.service';
import { 
  AddFriendDto, 
  HandleFriendApplyDto, 
  RemoveFriendDto,
  BlockCharacterDto,
  SearchFriendDto,
  NearbyCharactersDto,
  UpdateLocationDto
} from '@social/common/dto/friend.dto';
import { Cacheable, CacheEvict, CachePut } from '@libs/redis';
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

import { XResponse } from '@libs/common/types/result.type';

import {
  AddFriendPayloadDto,
  BlockCharacterPayloadDto,
  CleanExpiredAppliesPayloadDto,
  GetAppliesPayloadDto,
  GetBlacklistPayloadDto, GetFriendDistancesPayloadDto,
  GetFriendListPayloadDto,
  GetFriendStatsPayloadDto,
  GetNearbyPlayersPayloadDto, GetRecommendationsPayloadDto,
  HandleApplyPayloadDto,
  RemoveFriendPayloadDto,
  SearchFriendPayloadDto, UpdateLocationPayloadDto, UpdateOnlineStatusPayloadDto
} from "@social/common/dto/friend-payload.dto";

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class FriendController extends BaseController {
  constructor(private readonly friendService: FriendService) {
    super('FriendController');
  }

  // ==================== 好友列表管理 ====================

  /**
   * 获取好友列表
   */
  @MessagePattern('friend.getList')
  @Cacheable({
    key: 'friend:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getFriendList(@Payload() payload: GetFriendListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取好友列表: ${payload.characterId}`);
    const friendList = await this.friendService.getFriendList(payload.characterId);
    return this.fromResult(friendList);
  }

  /**
   * 添加好友申请
   */
  @MessagePattern('friend.add')
  @CacheEvict({
    key: 'friend:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addFriend(@Payload() payload: AddFriendPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`添加好友申请: ${payload.characterId} -> ${payload.targetCharacterId}`);
    const result = await this.friendService.addFriend(payload.characterId, payload);
    return this.fromResult(result);
  }

  /**
   * 处理好友申请
   */
  @MessagePattern('friend.handleApply')
  @CacheEvict({ 
    key: 'friend:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async handleFriendApply(@Payload() payload: HandleApplyPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`处理好友申请: ${payload.characterId}, 申请者: ${payload.applicantCharacterId}, 接受: ${payload.accept}`);
    const result = await this.friendService.handleFriendApply(payload.characterId, payload.serverId, payload);
    return this.fromResult(result);
  }

  /**
   * 删除好友
   */
  @MessagePattern('friend.remove')
  @CacheEvict({ 
    key: 'friend:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async removeFriend(@Payload() payload: RemoveFriendPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`删除好友: ${payload.characterId} -> ${payload.friendCharacterId}`);
    const result = await this.friendService.removeFriend(payload.characterId, payload.serverId, payload);
    return this.fromResult(result);
  }

  /**
   * 屏蔽玩家
   */
  @MessagePattern('friend.block')
  @CacheEvict({ 
    key: 'friend:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async blockCharacter(@Payload() payload: BlockCharacterPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`屏蔽玩家: ${payload.characterId} -> ${payload.targetCharacterId}`);
    const result = await this.friendService.blockCharacter(payload.characterId, payload.serverId, payload);
    return this.fromResult(result);
  }

  // ==================== 好友申请管理 ====================

  /**
   * 获取好友申请列表
   */
  @MessagePattern('friend.getApplies')
  @Cacheable({ 
    key: 'friend:applies:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 180
  })
  async getFriendApplies(@Payload() payload: GetAppliesPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取好友申请列表: ${payload.characterId}`);
    const applies = await this.friendService.getFriendApplies(payload.characterId);
    return this.fromResult(applies);
  }

  // ==================== 好友搜索 ====================

  /**
   * 搜索好友
   */
  @MessagePattern('friend.search')
  async searchFriends(@Payload() payload: SearchFriendPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`搜索好友: ${payload.characterId}, 关键词: ${payload.keyword}`);
    const result = await this.friendService.searchFriends(payload.characterId, payload);
    return this.fromResult(result);
  }

  /**
   * 查找附近玩家
   */
  @MessagePattern('friend.findNearby')
  @Cacheable({
    key: 'friend:nearby:#{payload.characterId}:#{payload.longitude}:#{payload.latitude}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600
  })
  async findNearbyCharacters(@Payload() payload: GetNearbyPlayersPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`查找附近玩家: ${payload.characterId}, 位置: ${payload}, ${payload.latitude}`);
    const result = await this.friendService.findNearbyCharacters(payload.characterId, payload.serverId, payload);
    return this.fromResult(result);
  }

  /**
   * 更新玩家位置
   */
  @MessagePattern('friend.updateLocation')
  async updateCharacterLocation(@Payload() payload: UpdateLocationPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新玩家位置: ${payload.characterId}, 位置: ${payload.longitude}, ${payload.latitude}`);
    const result = await this.friendService.updateCharacterLocation(payload.characterId, payload.serverId, payload);
    return this.fromResult(result);
  }

  /**
   * 推荐好友
   */
  @MessagePattern('friend.recommend')
  @Cacheable({
    key: 'friend:recommend:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async recommendFriends(@Payload() payload: GetRecommendationsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`推荐好友: ${payload.characterId}, 限制: ${payload.limit || 10}个`);
    const result = await this.friendService.recommendFriends(payload.characterId, payload.limit);
    return this.fromResult(result);
  }

  /**
   * 获取好友距离信息
   */
  @MessagePattern('friend.distances')
  @Cacheable({
    key: 'friend:distances:#{payload.characterId}:#{payload.longitude}:#{payload.latitude}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getFriendDistances(@Payload() payload: GetFriendDistancesPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取好友距离: ${payload.characterId}, 位置: ${payload.longitude}, ${payload.latitude}`);
    const result = await this.friendService.getFriendDistances(payload.characterId, payload.longitude, payload.latitude);
    return this.fromResult(result);
  }

  /**
   * 获取黑名单
   */
  @MessagePattern('friend.blacklist')
  @Cacheable({
    key: 'friend:blacklist:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600
  })
  async getBlackList(@Payload() payload: GetBlacklistPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取黑名单: ${payload.characterId}`);
    const result = await this.friendService.getBlackList(payload.characterId);
    return this.fromResult(result);
  }

  // ==================== 好友统计 ====================

  /**
   * 获取好友统计
   */
  @MessagePattern('friend.getStats')
  @Cacheable({ 
    key: 'friend:stats:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getFriendStats(@Payload() payload: GetFriendStatsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取好友统计: ${payload.characterId}`);
    const stats = await this.friendService.getFriendStats(payload.characterId);
    return this.fromResult(stats);
  }

  // ==================== 状态管理 ====================

  /**
   * 更新玩家在线状态
   */
  @MessagePattern('friend.updateOnlineStatus')
  @CacheEvict({ 
    key: 'friend:list:*',
    dataType: 'server'
  })
  async updateCharacterOnlineStatus(@Payload() payload: UpdateOnlineStatusPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新玩家在线状态: ${payload.characterId}, 在线: ${payload.isOnline}`);
    const result = await this.friendService.updateCharacterOnlineStatus(payload.characterId, payload.isOnline);
    return this.fromResult(result);
  }



  // ==================== 管理接口 ====================

  /**
   * 清理过期申请
   */
  @MessagePattern('friend.cleanExpiredApplies')
  async cleanExpiredApplies(@Payload() payload: CleanExpiredAppliesPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`清理过期申请: ${payload.expireDays || 7}天`);
    // TODO: 实现过期申请清理
    // const result = await this.friendService.cleanExpiredApplies(payload.expireDays);
    // return this.fromResult(result);

    return this.toSuccessResponse(undefined);
  }
}