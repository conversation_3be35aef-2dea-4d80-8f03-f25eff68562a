import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { FriendDocument, FriendInfo, FriendApply } from '@social/common/schemas/friend.schema';
import { FriendRepository } from '@social/common/repositories/friend.repository';
import {
  AddFriendDto,
  HandleFriendApplyDto,
  RemoveFriendDto,
  BlockCharacterDto,
  SearchFriendDto,
  NearbyCharactersDto,
  UpdateLocationDto,
  FriendListDto,
  FriendApplyListDto,
  FriendStatsDto
} from '@social/common/dto/friend.dto';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';

// Result模式相关导入
import { BaseService } from '@libs/common/service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { MicroserviceClientService } from '@libs/service-mesh';
import { RepositoryResultWrapper } from '@libs/common/repository';

/**
 * 好友系统服务 - 已适配Result模式
 *
 * 核心功能：
 * - 好友列表管理
 * - 好友申请处理
 * - 好友搜索和推荐
 * - 黑名单管理
 * - 附近玩家查找
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的错误处理
 */
@Injectable()
export class FriendService extends BaseService {

  constructor(
    private readonly friendRepository: FriendRepository,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('FriendService', microserviceClient);
  }

  /**
   * 获取好友列表
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getFriendList(characterId: string): Promise<XResult<FriendListDto>> {
    return this.executeBusinessOperation(async () => {
      const friendResult = await this.friendRepository.getOrCreateFriend(characterId);
      if (XResultUtils.isFailure(friendResult)) {
        return XResultUtils.error(`获取好友数据失败: ${friendResult.message}`, friendResult.code);
      }

      const friend = friendResult.data;

      const responseData: FriendListDto = {
        friends: friend.friends.map(f => ({
          characterId: f.characterId,
          name: f.name,
          level: f.level,
          honor: f.honor,
          faceIcon: f.faceIcon,
          faceUrl: f.faceUrl,
          vip: f.vip,
          trophy: f.trophy,
          addTime: f.addTime,
          lastOnlineTime: f.lastOnlineTime,
          isOnline: f.isOnline,
        })),
        totalFriends: friend.totalFriends,
        onlineFriends: friend.onlineFriends,
        pendingApplies: friend.pendingApplies,
        canAddMoreFriends: friend.canAddMoreFriends,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_friend_list',
      metadata: { characterId }
    });
  }

  /**
   * 添加好友申请 (使用characterId)
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async addFriend(characterId: string, addDto: AddFriendDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      if (characterId === addDto.targetCharacterId) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CANNOT_ADD_SELF], ErrorCode.CANNOT_ADD_SELF);
      }

      const [myFriendResult, targetFriendResult] = await Promise.all([
        this.friendRepository.getOrCreateFriend(characterId),
        this.friendRepository.getOrCreateFriend(addDto.targetCharacterId)
      ]);

      if (XResultUtils.isFailure(myFriendResult)) {
        return XResultUtils.error(`获取我的好友数据失败: ${myFriendResult.message}`, myFriendResult.code);
      }

      if (XResultUtils.isFailure(targetFriendResult)) {
        return XResultUtils.error(`获取目标好友数据失败: ${targetFriendResult.message}`, targetFriendResult.code);
      }

      const myFriend = myFriendResult.data;
      const targetFriend = targetFriendResult.data;

      // 检查是否已经是好友
      const existingFriend = myFriend.friends.find(f => f.characterId === addDto.targetCharacterId);
      if (existingFriend) {
        return XResultUtils.error(ErrorMessages[ErrorCode.FRIEND_ALREADY_EXISTS], ErrorCode.FRIEND_ALREADY_EXISTS);
      }

      // 检查是否在黑名单中
      if (targetFriend.blackList.includes(characterId)) {
        return XResultUtils.error(ErrorMessages[ErrorCode.FRIEND_NOT_FOUND], ErrorCode.FRIEND_NOT_FOUND);
      }

      // 检查是否已经发送过申请
      const existingApply = targetFriend.applyList.find(a => a.characterId === characterId);
      if (existingApply) {
        return XResultUtils.error(ErrorMessages[ErrorCode.FRIEND_REQUEST_ALREADY_SENT], ErrorCode.FRIEND_REQUEST_ALREADY_SENT);
      }

      // 从Character服务获取玩家信息
      const characterInfoResult = await this.getCharacterInfo(characterId);
      if (XResultUtils.isFailure(characterInfoResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterInfoResult.message}`, characterInfoResult.code);
      }

      const characterInfo = characterInfoResult.data;

      const applyInfo: FriendApply = {
        characterId,
        name: characterInfo.name,
        level: characterInfo.level,
        honor: characterInfo.honor || 0,
        faceIcon: characterInfo.faceIcon || '',
        faceUrl: characterInfo.faceUrl || '',
        vip: characterInfo.vip || 0,
        trophy: characterInfo.trophy || 0,
        applyTime: Date.now(),
        message: addDto.message || ''
      };

      // 添加到目标玩家的申请列表
      targetFriend.addApply(applyInfo);
      const saveTargetResult = await RepositoryResultWrapper.wrap(async () => {
        return await targetFriend.save();
      });
      if (XResultUtils.isFailure(saveTargetResult)) {
        return XResultUtils.error(`保存目标好友数据失败: ${saveTargetResult.message}`, saveTargetResult.code);
      }

      // 添加到自己的发出申请列表
      myFriend.relationApply.push(applyInfo);
      const saveMyResult = await RepositoryResultWrapper.wrap(async () => {
        return await myFriend.save();
      });
      if (XResultUtils.isFailure(saveMyResult)) {
        return XResultUtils.error(`保存我的好友数据失败: ${saveMyResult.message}`, saveMyResult.code);
      }

      this.logger.log(`好友申请发送成功: ${characterId} -> ${addDto.targetCharacterId}`);

      const responseData = {
        success: true,
        message: '好友申请已发送',
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'add_friend',
      metadata: {
        characterId,
        targetCharacterId: addDto.targetCharacterId
      }
    });
  }

  /**
   * 处理好友申请
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async handleFriendApply(characterId: string, handleDto: HandleFriendApplyDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const [myFriendResult, applicantFriendResult] = await Promise.all([
        this.friendRepository.getOrCreateFriend(characterId),
        this.friendRepository.getOrCreateFriend(handleDto.applicantCharacterId)
      ]);

      if (XResultUtils.isFailure(myFriendResult)) {
        return XResultUtils.error(`获取我的好友数据失败: ${myFriendResult.message}`, myFriendResult.code);
      }

      if (XResultUtils.isFailure(applicantFriendResult)) {
        return XResultUtils.error(`获取申请者好友数据失败: ${applicantFriendResult.message}`, applicantFriendResult.code);
      }

      const myFriend = myFriendResult.data;
      const applicantFriend = applicantFriendResult.data;

      const apply = myFriend.applyList.find(a => a.characterId === handleDto.applicantCharacterId);
      if (!apply) {
        return XResultUtils.error(ErrorMessages[ErrorCode.FRIEND_NOT_FOUND], ErrorCode.FRIEND_NOT_FOUND);
      }

      if (handleDto.accept) {
        // 接受申请
        const success = myFriend.acceptApply(handleDto.applicantCharacterId);
        if (!success) {
          return XResultUtils.error(ErrorMessages[ErrorCode.FRIEND_LIMIT_REACHED], ErrorCode.FRIEND_LIMIT_REACHED);
        }

        // 添加到申请者的好友列表
        const myInfoResult = await this.getCharacterInfo(characterId);
        if (XResultUtils.isFailure(myInfoResult)) {
          return XResultUtils.error(`获取我的角色信息失败: ${myInfoResult.message}`, myInfoResult.code);
        }

        const myInfo = myInfoResult.data;
        const friendInfo: FriendInfo = {
          characterId,
          name: myInfo.name,
          level: myInfo.level,
          honor: myInfo.honor || 0,
          longitude: 0,
          latitude: 0,
          faceIcon: myInfo.faceIcon || '',
          faceUrl: myInfo.faceUrl || '',
          vip: myInfo.vip || 0,
          trophy: myInfo.trophy || 0,
          addTime: Date.now(),
          lastOnlineTime: Date.now(),
          isOnline: true,
        };

        applicantFriend.addFriend(friendInfo);

        // 移除申请者的发出申请记录
        const applyIndex = applicantFriend.relationApply.findIndex(a => a.characterId === characterId);
        if (applyIndex !== -1) {
          applicantFriend.relationApply.splice(applyIndex, 1);
        }

        const [saveMyResult, saveApplicantResult] = await Promise.all([
          RepositoryResultWrapper.wrap(async () => await myFriend.save()),
          RepositoryResultWrapper.wrap(async () => await applicantFriend.save())
        ]);

        if (XResultUtils.isFailure(saveMyResult)) {
          return XResultUtils.error(`保存我的好友数据失败: ${saveMyResult.message}`, saveMyResult.code);
        }

        if (XResultUtils.isFailure(saveApplicantResult)) {
          return XResultUtils.error(`保存申请者好友数据失败: ${saveApplicantResult.message}`, saveApplicantResult.code);
        }

        this.logger.log(`好友申请接受成功: ${characterId} <-> ${handleDto.applicantCharacterId}`);

        const responseData = {
          success: true,
          message: '好友申请已接受',
          newFriend: friendInfo,
        };

        return XResultUtils.ok(responseData);
      } else {
        // 拒绝申请
        myFriend.rejectApply(handleDto.applicantCharacterId);

        // 移除申请者的发出申请记录
        const applyIndex = applicantFriend.relationApply.findIndex(a => a.characterId === characterId);
        if (applyIndex !== -1) {
          applicantFriend.relationApply.splice(applyIndex, 1);
        }

        const [saveMyResult, saveApplicantResult] = await Promise.all([
          RepositoryResultWrapper.wrap(async () => await myFriend.save()),
          RepositoryResultWrapper.wrap(async () => await applicantFriend.save())
        ]);

        if (XResultUtils.isFailure(saveMyResult)) {
          return XResultUtils.error(`保存我的好友数据失败: ${saveMyResult.message}`, saveMyResult.code);
        }

        if (XResultUtils.isFailure(saveApplicantResult)) {
          return XResultUtils.error(`保存申请者好友数据失败: ${saveApplicantResult.message}`, saveApplicantResult.code);
        }

        this.logger.log(`好友申请拒绝: ${characterId} -> ${handleDto.applicantCharacterId}`);

        const responseData = {
          success: true,
          message: '好友申请已拒绝',
        };

        return XResultUtils.ok(responseData);
      }
    }, {
      reason: 'handle_friend_apply',
      metadata: {
        characterId,
        applicantCharacterId: handleDto.applicantCharacterId,
        accept: handleDto.accept,
      }
    });
  }

  /**
   * 删除好友
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async removeFriend(characterId: string, removeDto: RemoveFriendDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const [myFriendResult, targetFriendResult] = await Promise.all([
        this.friendRepository.getOrCreateFriend(characterId),
        this.friendRepository.getOrCreateFriend(removeDto.friendCharacterId)
      ]);

      if (XResultUtils.isFailure(myFriendResult)) {
        return XResultUtils.error(`获取我的好友数据失败: ${myFriendResult.message}`, myFriendResult.code);
      }

      if (XResultUtils.isFailure(targetFriendResult)) {
        return XResultUtils.error(`获取目标好友数据失败: ${targetFriendResult.message}`, targetFriendResult.code);
      }

      const myFriend = myFriendResult.data;
      const targetFriend = targetFriendResult.data;

      // 从双方好友列表中移除
      const mySuccess = myFriend.removeFriend(removeDto.friendCharacterId);
      const targetSuccess = targetFriend.removeFriend(characterId);

      if (!mySuccess && !targetSuccess) {
        return XResultUtils.error(ErrorMessages[ErrorCode.FRIEND_NOT_FOUND], ErrorCode.FRIEND_NOT_FOUND);
      }

      const [saveMyResult, saveTargetResult] = await Promise.all([
        RepositoryResultWrapper.wrap(async () => await myFriend.save()),
        RepositoryResultWrapper.wrap(async () => await targetFriend.save())
      ]);

      if (XResultUtils.isFailure(saveMyResult)) {
        return XResultUtils.error(`保存我的好友数据失败: ${saveMyResult.message}`, saveMyResult.code);
      }

      if (XResultUtils.isFailure(saveTargetResult)) {
        return XResultUtils.error(`保存目标好友数据失败: ${saveTargetResult.message}`, saveTargetResult.code);
      }

      this.logger.log(`好友删除成功: ${characterId} <-> ${removeDto.friendCharacterId}`);

      const responseData = {
        success: true,
        message: '好友已删除',
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'remove_friend',
      metadata: {
        characterId,
        friendCharacterId: removeDto.friendCharacterId,
      }
    });
  }

  /**
   * 屏蔽玩家
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async blockCharacter(characterId: string, blockDto: BlockCharacterDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const myFriendResult = await this.friendRepository.getOrCreateFriend(characterId);
      if (XResultUtils.isFailure(myFriendResult)) {
        return XResultUtils.error(`获取好友数据失败: ${myFriendResult.message}`, myFriendResult.code);
      }

      const myFriend = myFriendResult.data;

      const success = myFriend.blockCharacter(blockDto.targetCharacterId);
      if (!success) {
        return XResultUtils.error(ErrorMessages[ErrorCode.FRIEND_NOT_FOUND], ErrorCode.FRIEND_NOT_FOUND);
      }

      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await myFriend.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存好友数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`玩家屏蔽成功: ${characterId} -> ${blockDto.targetCharacterId}`);

      const responseData = {
        success: true,
        message: '玩家已屏蔽',
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'block_character',
      metadata: {
        characterId,
        targetCharacterId: blockDto.targetCharacterId,
      }
    });
  }

  /**
   * 获取好友申请列表
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getFriendApplies(characterId: string): Promise<XResult<FriendApplyListDto>> {
    return this.executeBusinessOperation(async () => {
      const friendResult = await this.friendRepository.getOrCreateFriend(characterId);
      if (XResultUtils.isFailure(friendResult)) {
        return XResultUtils.error(`获取好友数据失败: ${friendResult.message}`, friendResult.code);
      }

      const friend = friendResult.data;

      const responseData: FriendApplyListDto = {
        receivedApplies: friend.applyList.map(a => ({
          characterId: a.characterId,
          name: a.name,
          level: a.level,
          honor: a.honor,
          faceIcon: a.faceIcon,
          faceUrl: a.faceUrl,
          vip: a.vip,
          trophy: a.trophy,
          applyTime: a.applyTime,
          message: a.message,
        })),
        sentApplies: friend.relationApply.map(a => ({
          characterId: a.characterId,
          name: a.name,
          level: a.level,
          honor: a.honor,
          faceIcon: a.faceIcon,
          faceUrl: a.faceUrl,
          vip: a.vip,
          trophy: a.trophy,
          applyTime: a.applyTime,
          message: a.message,
        })),
        pendingCount: friend.pendingApplies,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_friend_applies',
      metadata: { characterId }
    });
  }

  /**
   * 搜索好友
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async searchFriends(characterId: string, searchDto: SearchFriendDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const friendsResult = await this.friendRepository.searchFriends(characterId, searchDto);
      if (XResultUtils.isFailure(friendsResult)) {
        return XResultUtils.error(`搜索好友失败: ${friendsResult.message}`, friendsResult.code);
      }

      const friends = friendsResult.data;
      const responseData = {
        friends,
        total: friends.length,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'search_friends',
      metadata: {
        characterId,
        searchKeyword: searchDto.keyword
      }
    });
  }

  /**
   * 获取好友统计
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getFriendStats(characterId: string): Promise<XResult<FriendStatsDto>> {
    return this.executeBusinessOperation(async () => {
      const statsResult = await this.friendRepository.getFriendStats(characterId);
      if (XResultUtils.isFailure(statsResult)) {
        return XResultUtils.error(`获取好友统计失败: ${statsResult.message}`, statsResult.code);
      }

      return XResultUtils.ok(statsResult.data);
    }, {
      reason: 'get_friend_stats',
      metadata: { characterId }
    });
  }

  /**
   * 更新玩家在线状态
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async updateCharacterOnlineStatus(characterId: string, isOnline: boolean): Promise<XResult<void>> {
    return this.executeBusinessOperation(async () => {
      const updateResult = await this.friendRepository.batchUpdateFriendStatus([characterId], isOnline);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新在线状态失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`玩家在线状态更新: ${characterId}, 在线: ${isOnline}`);

      return XResultUtils.ok(undefined);
    }, {
      reason: 'update_character_online_status',
      metadata: {
        characterId,
        isOnline
      }
    });
  }

  // ==================== 私有方法 ====================

  /**
   * 获取玩家信息（从Character服务）
   * 已适配Result模式：返回XResult类型，使用标准化微服务调用，移除try-catch
   */
  private async getCharacterInfo(characterId: string): Promise<XResult<any>> {
    // TODO: 调用Character服务获取玩家信息（使用BaseService的标准化微服务调用）
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
    //   'character.getInfo',
    //   { characterId }
    // );

    // if (XResultUtils.isSuccess(result)) {
    //   return XResultUtils.ok(result.data);
    // } else {
    //   return XResultUtils.error(`获取角色信息失败: ${result.message}`, result.code);
    // }

    // 暂时返回模拟数据
    const mockData = {
      characterId,
      name: `玩家${characterId.slice(-4)}`,
      level: Math.floor(Math.random() * 50) + 1,
      honor: Math.floor(Math.random() * 1000),
      faceIcon: 'default_icon',
      faceUrl: '',
      vip: Math.floor(Math.random() * 10),
      trophy: Math.floor(Math.random() * 5000),
      lastOnlineTime: Date.now() - Math.floor(Math.random() * 86400000), // 24小时内
    };

    return XResultUtils.ok(mockData);
  }

  // ==================== 缺失功能补充 ====================

  /**
   * 更新玩家位置信息
   * 对应old项目中的地理位置功能
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async updateCharacterLocation(characterId: string, locationDto: UpdateLocationDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const friendResult = await this.friendRepository.getOrCreateFriend(characterId);
      if (XResultUtils.isFailure(friendResult)) {
        return XResultUtils.error(`获取好友数据失败: ${friendResult.message}`, friendResult.code);
      }

      // 更新自己在所有好友列表中的位置信息
      const updateLocationResult = await this.friendRepository.updateCharacterLocationInFriendLists(
        characterId,
        locationDto.longitude,
        locationDto.latitude
      );

      if (XResultUtils.isFailure(updateLocationResult)) {
        return XResultUtils.error(`更新位置信息失败: ${updateLocationResult.message}`, updateLocationResult.code);
      }

      this.logger.log(`玩家位置更新成功: ${characterId}, 位置: ${locationDto.longitude}, ${locationDto.latitude}`);

      const responseData = {
        success: true,
        message: '位置更新成功',
        longitude: locationDto.longitude,
        latitude: locationDto.latitude,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'update_character_location',
      metadata: {
        characterId,
        longitude: locationDto.longitude,
        latitude: locationDto.latitude
      }
    });
  }

  /**
   * 查找附近玩家
   * 对应old项目中的地理位置查找功能
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async findNearbyCharacters(characterId: string, nearbyDto: NearbyCharactersDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // TODO: 需要与Character服务通信获取附近玩家（使用BaseService的标准化微服务调用）
      // 这里先返回模拟数据，实际实现需要：
      // 1. 调用Character服务获取指定范围内的玩家
      // 2. 过滤已经是好友的玩家
      // 3. 计算距离并排序

      const radius = nearbyDto.radius || 5000; // 默认5公里
      const limit = nearbyDto.limit || 20;

      this.logger.log(`查找附近玩家: ${characterId}, 半径: ${radius}米, 限制: ${limit}个`);

      const responseData = {
        characters: [], // TODO: 实现真实的附近玩家查询
        total: 0,
        radius,
        center: {
          longitude: nearbyDto.longitude,
          latitude: nearbyDto.latitude,
        },
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'find_nearby_characters',
      metadata: {
        characterId,
        radius: nearbyDto.radius,
        limit: nearbyDto.limit
      }
    });
  }

  /**
   * 推荐好友
   * 基于等级、活跃度等推荐合适的好友
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async recommendFriends(characterId: string, limit: number = 10): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const friendResult = await this.friendRepository.getOrCreateFriend(characterId);
      if (XResultUtils.isFailure(friendResult)) {
        return XResultUtils.error(`获取好友数据失败: ${friendResult.message}`, friendResult.code);
      }

      const friend = friendResult.data;

      // 基于old项目: 好友推荐算法实现
      // 1. 获取玩家信息（等级、活跃度等）
      const myInfoResult = await this.getCharacterInfo(characterId);
      if (XResultUtils.isFailure(myInfoResult)) {
        return XResultUtils.error(`获取角色信息失败: ${myInfoResult.message}`, myInfoResult.code);
      }

      const myInfo = myInfoResult.data;

      // 2. 查找相似等级的玩家
      const candidatesResult = await this.findCandidateFriends(myInfo);
      if (XResultUtils.isFailure(candidatesResult)) {
        return XResultUtils.error(`查找候选好友失败: ${candidatesResult.message}`, candidatesResult.code);
      }

      const candidates = candidatesResult.data;

      // 3. 过滤已经是好友或在黑名单中的玩家
      const filteredCandidates = this.filterCandidates(candidates, friend);

      // 4. 按推荐度排序
      const recommendations = this.sortByRecommendationScore(filteredCandidates, myInfo).slice(0, limit);

      this.logger.log(`推荐好友: ${characterId}, 找到${recommendations.length}个推荐`);

      const responseData = {
        recommendations,
        total: recommendations.length,
        criteria: {
          levelRange: `±${this.getLevelRange(myInfo.level)}`,
          activityScore: 'high',
          commonInterests: [],
        },
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'recommend_friends',
      metadata: {
        characterId,
        limit
      }
    });
  }

  /**
   * 批量更新好友在线状态
   * 对应old项目中的在线状态管理
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async batchUpdateOnlineStatus(characterIds: string[], isOnline: boolean): Promise<XResult<void>> {
    return this.executeBusinessOperation(async () => {
      const updateResult = await this.friendRepository.batchUpdateFriendStatus(characterIds, isOnline);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`批量更新在线状态失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`批量更新在线状态: ${characterIds.length}个玩家, 在线: ${isOnline}`);

      return XResultUtils.ok(undefined);
    }, {
      reason: 'batch_update_online_status',
      metadata: {
        characterCount: characterIds.length,
        isOnline
      }
    });
  }

  /**
   * 获取好友距离信息
   * 对应old项目中的距离计算功能
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getFriendDistances(characterId: string, longitude: number, latitude: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const friendResult = await this.friendRepository.getOrCreateFriend(characterId);
      if (XResultUtils.isFailure(friendResult)) {
        return XResultUtils.error(`获取好友数据失败: ${friendResult.message}`, friendResult.code);
      }

      const friend = friendResult.data;

      const friendsWithDistance = friend.friends.map(f => {
        const distance = this.calculateDistance(longitude, latitude, f.longitude, f.latitude);
        return {
          characterId: f.characterId,
          name: f.name,
          level: f.level,
          isOnline: f.isOnline,
          distance: distance,
          distanceText: this.formatDistance(distance),
        };
      });

      // 按距离排序
      friendsWithDistance.sort((a, b) => a.distance - b.distance);

      const responseData = {
        friends: friendsWithDistance,
        total: friendsWithDistance.length,
        center: { longitude, latitude },
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_friend_distances',
      metadata: {
        characterId,
        longitude,
        latitude
      }
    });
  }

  /**
   * 计算两点间距离（米）
   * 对应old项目中的距离计算算法
   */
  private calculateDistance(lon1: number, lat1: number, lon2: number, lat2: number): number {
    if (!lon2 || !lat2) return -1;

    const R = 6371393; // 地球半径（米）
    const lat1Rad = lat1 * Math.PI / 180;
    const lat2Rad = lat2 * Math.PI / 180;
    const deltaLat = (lat2 - lat1) * Math.PI / 180;
    const deltaLng = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLng/2) * Math.sin(deltaLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    const distance = R * c;

    // 对应old项目中的距离处理逻辑
    if (distance !== 0 && distance < 100) {
      return 50;
    }
    if (distance !== 0 && distance > 1000) {
      return 5000;
    }

    return Math.ceil(distance);
  }

  /**
   * 格式化距离显示
   */
  private formatDistance(distance: number): string {
    if (distance < 0) return '未知';
    if (distance < 1000) return `${distance}米`;
    return `${(distance / 1000).toFixed(1)}公里`;
  }

  /**
   * 获取黑名单
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getBlackList(characterId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const friendResult = await this.friendRepository.getOrCreateFriend(characterId);
      if (XResultUtils.isFailure(friendResult)) {
        return XResultUtils.error(`获取好友数据失败: ${friendResult.message}`, friendResult.code);
      }

      const friend = friendResult.data;
      const responseData = {
        blackList: friend.blackList,
        total: friend.blackList.length,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_black_list',
      metadata: {
        characterId,
      }
    });
  }

  /**
   * 查找候选好友
   * 基于old项目: 根据等级范围查找玩家
   * 已适配Result模式：返回XResult类型，使用标准化微服务调用，移除try-catch
   */
  private async findCandidateFriends(myInfo: any): Promise<XResult<any[]>> {
    // TODO: 调用Character服务查找相似等级的玩家（使用BaseService的标准化微服务调用）
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
    //   'character.findByLevelRange',
    //   {
    //     minLevel: Math.max(1, myInfo.level - 10),
    //     maxLevel: myInfo.level + 10,
    //     serverId,
    //     limit: 50
    //   }
    // );
    //
    // if (XResultUtils.isSuccess(result)) {
    //   return XResultUtils.ok(result.data.characters || []);
    // } else {
    //   return XResultUtils.error(`查找候选好友失败: ${result.message}`, result.code);
    // }

    // 暂时返回模拟数据
    const candidates = [];
    for (let i = 0; i < 20; i++) {
      candidates.push({
        characterId: `candidate_${i}`,
        name: `玩家${i}`,
        level: myInfo.level + Math.floor(Math.random() * 21) - 10, // ±10级
        honor: Math.floor(Math.random() * 1000),
        trophy: Math.floor(Math.random() * 5000),
        vip: Math.floor(Math.random() * 10),
        lastOnlineTime: Date.now() - Math.floor(Math.random() * 86400000), // 24小时内
      });
    }

    return XResultUtils.ok(candidates);
  }

  /**
   * 过滤候选好友
   * 基于old项目: 过滤已有好友和黑名单
   */
  private filterCandidates(candidates: any[], friend: any): any[] {
    return candidates.filter(candidate => {
      // 过滤已经是好友的
      const isAlreadyFriend = friend.friends.some(f => f.characterId === candidate.characterId);
      if (isAlreadyFriend) return false;

      // 过滤黑名单中的
      const isBlocked = friend.blackList.includes(candidate.characterId);
      if (isBlocked) return false;

      // 过滤已发送申请的
      const hasApplied = friend.relationApply.some(a => a.characterId === candidate.characterId);
      if (hasApplied) return false;

      return true;
    });
  }

  /**
   * 按推荐度排序
   * 基于old项目: 推荐算法评分
   */
  private sortByRecommendationScore(candidates: any[], myInfo: any): any[] {
    return candidates
      .map(candidate => ({
        ...candidate,
        score: this.calculateRecommendationScore(candidate, myInfo),
      }))
      .sort((a, b) => b.score - a.score);
  }

  /**
   * 计算推荐度评分
   * 基于old项目: 等级差异、活跃度、VIP等级等因素
   */
  private calculateRecommendationScore(candidate: any, myInfo: any): number {
    let score = 100; // 基础分

    // 等级差异评分（差异越小分数越高）
    const levelDiff = Math.abs(candidate.level - myInfo.level);
    score -= levelDiff * 2;

    // 活跃度评分（最近在线时间）
    const hoursOffline = (Date.now() - candidate.lastOnlineTime) / (1000 * 60 * 60);
    if (hoursOffline < 1) score += 20; // 1小时内在线
    else if (hoursOffline < 24) score += 10; // 24小时内在线
    else score -= hoursOffline; // 离线时间越长扣分越多

    // VIP等级评分
    score += candidate.vip * 5;

    // 奖杯数评分
    score += Math.min(candidate.trophy / 100, 50); // 最多加50分

    return Math.max(0, score);
  }

  /**
   * 获取等级范围
   * 基于old项目: 根据玩家等级确定推荐范围
   */
  private getLevelRange(level: number): number {
    if (level <= 10) return 5;
    if (level <= 30) return 10;
    if (level <= 50) return 15;
    return 20;
  }
}
