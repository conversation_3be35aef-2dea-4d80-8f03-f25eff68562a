import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, ClientSession } from 'mongoose';
import { Friend, FriendDocument, FriendInfo, FriendApply } from '../schemas/friend.schema';
import { SearchFriendDto, NearbyCharactersDto } from '../dto/friend.dto';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 好友数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 好友关系CRUD操作
 * - 好友申请管理
 * - 好友搜索和推荐
 * - 附近玩家查询
 * - 好友统计分析
 * - 黑名单管理
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 */
@Injectable()
export class FriendRepository extends BaseRepository<FriendDocument> {
  constructor(
    @InjectModel(Friend.name) friendModel: Model<FriendDocument>
  ) {
    super(friendModel, 'FriendRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 创建好友记录
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async createFriend(friendData: Partial<Friend>): Promise<XResult<FriendDocument>> {
    const friendWithTime = {
      ...friendData,
      lastUpdateTime: Date.now(),
    };
    return this.createOne(friendWithTime);
  }

  /**
   * 根据玩家ID查找好友记录
   * 使用BaseRepository的findOne方法优化性能
   */
  async findFriendBycharacterId(characterId: string): Promise<XResult<FriendDocument | null>> {
    return this.findOne({ characterId });
  }

  /**
   * 根据玩家ID查找好友记录（Lean查询优化版本）
   */
  async findFriendBycharacterIdLean(characterId: string): Promise<XResult<any | null>> {
    return this.findOneLean({ characterId });
  }

  /**
   * 获取或创建好友记录
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async getOrCreateFriend(characterId: string): Promise<XResult<FriendDocument>> {
    const friendResult = await this.findFriendBycharacterId(characterId);

    if (XResultUtils.isSuccess(friendResult) && friendResult.data) {
      return friendResult;
    }

    // 创建新好友记录
    const friendId = this.generateFriendId(characterId);
    const createResult = await this.createFriend({
      friendId,
      characterId,
    });

    if (XResultUtils.isSuccess(createResult)) {
      this.logger.log(`创建新好友记录: ${friendId}`);
    }

    return createResult;
  }

  /**
   * 更新好友记录
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateFriend(
    characterId: string,
    updateData: UpdateQuery<FriendDocument>,
    session?: ClientSession
  ): Promise<XResult<FriendDocument | null>> {
    return this.updateOne(
      { characterId },
      { ...updateData, lastUpdateTime: Date.now() },
      session
    );
  }

  /**
   * 搜索好友
   * 使用BaseRepository的findOne方法优化性能
   */
  async searchFriends(characterId: string, searchDto: SearchFriendDto): Promise<XResult<FriendInfo[]>> {
    const friendResult = await this.findFriendBycharacterIdLean(characterId);
    if (XResultUtils.isFailure(friendResult) || !friendResult.data) {
      return XResultUtils.ok([]);
    }

    const friend = friendResult.data;
    let friends = friend.friends || [];

    // 关键词搜索
    if (searchDto.keyword) {
      const keyword = searchDto.keyword.toLowerCase();
      friends = friends.filter(f =>
        f.name.toLowerCase().includes(keyword) ||
        f.characterId.includes(keyword)
      );
    }

    // 等级范围过滤
    if (searchDto.minLevel !== undefined) {
      friends = friends.filter(f => f.level >= searchDto.minLevel!);
    }
    if (searchDto.maxLevel !== undefined) {
      friends = friends.filter(f => f.level <= searchDto.maxLevel!);
    }

    // 在线状态过滤
    if (searchDto.onlineOnly) {
      friends = friends.filter(f => f.isOnline);
    }

    // 距离过滤
    if (searchDto.maxDistance && searchDto.longitude !== undefined && searchDto.latitude !== undefined) {
      friends = friends.filter(f => {
        const distance = this.calculateDistance(
          searchDto.longitude!,
          searchDto.latitude!,
          f.longitude,
          f.latitude
        );
        return distance <= searchDto.maxDistance!;
      });
    }

    return XResultUtils.ok(friends);
  }

  /**
   * 查找附近玩家（模拟实现）
   * 使用BaseRepository的findMany方法优化性能
   */
  async findNearbyCharacters(characterId: string, nearbyDto: NearbyCharactersDto): Promise<XResult<any[]>> {
    // 这里应该查询所有玩家的位置信息
    // 由于没有全局玩家位置表，这里返回模拟数据
    // 实际实现中需要从Character服务获取玩家位置信息

    const radius = nearbyDto.radius || 5000; // 默认5公里
    const limit = nearbyDto.limit || 20;

    // TODO: 实现真实的附近玩家查询
    // 1. 查询Character服务获取附近玩家
    // 2. 过滤已经是好友的玩家
    // 3. 计算距离并排序

    return XResultUtils.ok([]);
  }



  /**
   * 获取好友统计信息
   * 使用BaseRepository的findOne方法优化性能
   */
  async getFriendStats(characterId: string): Promise<XResult<any>> {
    const friendResult = await this.findFriendBycharacterIdLean(characterId);
    if (XResultUtils.isFailure(friendResult) || !friendResult.data) {
      return XResultUtils.ok({
        totalFriends: 0,
        onlineFriends: 0,
        todayNewFriends: 0,
        weekNewFriends: 0,
        pendingApplies: 0,
        blockedCharacters: 0,
        levelDistribution: {},
        vipDistribution: {},
      });
    }

    const friend = friendResult.data;
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;
    const oneWeekMs = 7 * oneDayMs;

    const todayNewFriends = friend.friends.filter(f =>
      (now - f.addTime) <= oneDayMs
    ).length;

    const weekNewFriends = friend.friends.filter(f =>
      (now - f.addTime) <= oneWeekMs
    ).length;

    // 等级分布统计
    const levelDistribution = friend.friends.reduce((acc, f) => {
      const levelRange = Math.floor(f.level / 10) * 10;
      const key = `${levelRange}-${levelRange + 9}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // VIP分布统计
    const vipDistribution = friend.friends.reduce((acc, f) => {
      const key = `VIP${f.vip}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return XResultUtils.ok({
      totalFriends: friend.totalFriends,
      onlineFriends: friend.onlineFriends,
      todayNewFriends,
      weekNewFriends,
      pendingApplies: friend.pendingApplies,
      blockedCharacters: friend.blackList?.length || 0,
      levelDistribution,
      vipDistribution,
    });
  }

  /**
   * 清理过期申请
   * 使用BaseRepository的updateMany方法优化性能
   */
  async cleanExpiredApplies(expireDays: number = 7): Promise<XResult<any>> {
    const expireTime = Date.now() - (expireDays * 24 * 60 * 60 * 1000);

    // 使用事务确保数据一致性
    return this.withTransaction(async (session) => {
      const result = await this.updateMany(
        {},
        {
          $pull: {
            applyList: { applyTime: { $lt: expireTime } },
            relationApply: { applyTime: { $lt: expireTime } }
          },
          $set: { lastUpdateTime: Date.now() }
        },
        session
      );

      // 重新计算待处理申请数
      await this.updateMany(
        {},
        [
          {
            $set: {
              pendingApplies: { $size: '$applyList' }
            }
          }
        ],
        session
      );

      if (XResultUtils.isSuccess(result)) {
        const modifiedCount = result.data.modifiedCount || 0;
        this.logger.log(`清理过期申请完成，影响记录数: ${modifiedCount}`);
        return XResultUtils.ok(result.data);
      }

      return result;
    });
  }

  /**
   * 更新玩家在所有好友列表中的位置信息
   * 使用BaseRepository的updateMany方法优化性能
   */
  async updateCharacterLocationInFriendLists(characterId: string, longitude: number, latitude: number): Promise<XResult<void>> {
    const result = await this.updateMany(
      { 'friends.characterId': characterId },
      {
        $set: {
          'friends.$.longitude': longitude,
          'friends.$.latitude': latitude,
          'friends.$.lastLocationUpdate': Date.now(),
        }
      }
    );

    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`更新玩家位置信息完成: ${characterId}`);
      return XResultUtils.ok(undefined);
    }

    return XResultUtils.error('更新玩家位置信息失败', 'UPDATE_LOCATION_FAILED');
  }

  /**
   * 批量更新好友在线状态
   * 使用BaseRepository的updateMany方法优化性能
   */
  async batchUpdateFriendStatus(characterIds: string[], isOnline: boolean): Promise<XResult<void>> {
    const result = await this.updateMany(
      { 'friends.characterId': { $in: characterIds } },
      {
        $set: {
          'friends.$.isOnline': isOnline,
          'friends.$.lastOnlineTime': Date.now(),
        }
      }
    );

    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`批量更新好友状态完成: ${characterIds.length}个玩家, 在线: ${isOnline}`);
      return XResultUtils.ok(undefined);
    }

    return XResultUtils.error('批量更新好友状态失败', 'BATCH_UPDATE_FAILED');
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加好友特定的验证规则
   */
  protected validateData(data: Partial<Friend>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.friendId) {
        return XResultUtils.error('好友ID不能为空', 'FRIEND_ID_REQUIRED');
      }

      if (!data.characterId) {
        return XResultUtils.error('角色ID不能为空', 'CHARACTER_ID_REQUIRED');
      }
    }

    if (data.friends && Array.isArray(data.friends)) {
      if (data.friends.length > 200) {
        return XResultUtils.error('好友数量不能超过200个', 'TOO_MANY_FRIENDS');
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对好友数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findFriendBycharacterId': 300,    // 好友记录缓存5分钟
      'findFriendBycharacterIdLean': 180, // 好友简介缓存3分钟
      'searchFriends': 120,              // 好友搜索缓存2分钟
      'findNearbyCharacters': 60,        // 附近玩家缓存1分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }

  // ==================== 私有方法 ====================

  /**
   * 生成好友ID
   */
  private generateFriendId(characterId: string): string {
    return `friend_${characterId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 计算两点间距离（简化实现）
   */
  private calculateDistance(lon1: number, lat1: number, lon2: number, lat2: number): number {
    const R = 6371; // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }
}
