/**
 * 球员相关的数据传输对象
 */

import { IsString, IsNumber, IsOptional, IsBoolean, IsArray, IsEnum, Min, Max, Length } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { HeroQuality } from '../types';
import { HeroPosition } from '@libs/game-constants';

// 创建球员DTO
export class CreateHeroDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '配置表ID' })
  @IsNumber()
  resId: number;

  @ApiProperty({ description: '球员名称' })
  @IsString()
  @Length(1, 50)
  name: string;

  @ApiProperty({ description: '位置', enum: HeroPosition })
  @IsEnum(HeroPosition)
  position: HeroPosition;

  @ApiProperty({ description: '品质', enum: HeroQuality })
  @IsEnum(HeroQuality)
  quality: HeroQuality;

  @ApiPropertyOptional({ description: '等级', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  level?: number;

  @ApiPropertyOptional({ description: '国籍' })
  @IsOptional()
  @IsString()
  nationality?: string;

  @ApiPropertyOptional({ description: '俱乐部' })
  @IsOptional()
  @IsString()
  club?: string;

  @ApiPropertyOptional({ description: '获得方式' })
  @IsOptional()
  @IsNumber()
  obtainType?: number;
}

// 更新球员DTO
export class UpdateHeroDto {
  @ApiPropertyOptional({ description: '球员名称' })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  name?: string;

  @ApiPropertyOptional({ description: '是否锁定' })
  @IsOptional()
  @IsBoolean()
  isLocked?: boolean;

  @ApiPropertyOptional({ description: '装备列表' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  equipments?: string[];

  @ApiPropertyOptional({ description: '合同天数' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  contractDays?: number;

  @ApiPropertyOptional({ description: '薪水' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  salary?: number;
}

// 训练类型枚举
export enum TrainingType {
  PRIMARY = 1,    // 初级训练
  INTERMEDIATE = 2, // 中级训练
  ADVANCED = 3,   // 高级训练
  TARGETED = 4,   // 定向训练
}

// 训练方式枚举
export enum TrainingMethod {
  GOLD = 1,      // 使用金币
  ITEM = 2,      // 使用道具
}

// 球员训练DTO
export class TrainHeroDto {
  @ApiProperty({ description: '球员ID' })
  @IsString()
  heroId: string;

  @ApiProperty({ description: '训练类型', enum: TrainingType })
  @IsEnum(TrainingType)
  trainingType: TrainingType;

  @ApiProperty({ description: '训练方式', enum: TrainingMethod })
  @IsEnum(TrainingMethod)
  trainingMethod: TrainingMethod;

  @ApiPropertyOptional({ description: '定向训练的属性类型（仅定向训练时需要）' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  targetAttributes?: string[];

  @ApiPropertyOptional({ description: '训练次数', minimum: 1, maximum: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  count?: number;

  @ApiPropertyOptional({ description: '服务器ID' })
  @IsOptional()
  @IsString()
  serverId?: string;
}

// 球员升星DTO
export class EvolveHeroDto {
  @ApiProperty({ description: '球员ID' })
  @IsString()
  heroId: string;

  @ApiProperty({ description: '消耗的球员ID列表' })
  @IsArray()
  @IsString({ each: true })
  materialHeroIds: string[];

  @ApiPropertyOptional({ description: '是否使用保护道具（锁定成功）' })
  @IsOptional()
  @IsBoolean()
  useProtection?: boolean;

  @ApiPropertyOptional({ description: '是否使用万能卡' })
  @IsOptional()
  @IsBoolean()
  useUniversalCard?: boolean;

  @ApiPropertyOptional({ description: '使用的道具列表' })
  @IsOptional()
  @IsArray()
  items?: Array<{
    itemId: string;
    count: number;
  }>;

  @ApiPropertyOptional({ description: '服务器ID' })
  @IsOptional()
  @IsString()
  serverId?: string;
}

// 球员升级DTO
export class LevelUpHeroDto {
  @ApiProperty({ description: '球员ID' })
  @IsString()
  heroId: string;

  @ApiPropertyOptional({ description: '目标等级' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  targetLevel?: number;

  @ApiPropertyOptional({ description: '是否使用经验道具' })
  @IsOptional()
  @IsBoolean()
  useExpItems?: boolean;
}

// 球员技能升级DTO
export class UpgradeSkillDto {
  @ApiProperty({ description: '球员ID' })
  @IsString()
  heroId: string;

  @ApiProperty({ description: '技能ID' })
  @IsNumber()
  skillId: number;

  @ApiPropertyOptional({ description: '目标等级' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  targetLevel?: number;
}

// 球员市场操作DTO
export class MarketOperationDto {
  @ApiProperty({ description: '球员ID' })
  @IsString()
  heroId: string;

  @ApiProperty({ description: '操作类型', enum: ['list', 'unlist', 'buy'] })
  @IsString()
  operation: 'list' | 'unlist' | 'buy';

  @ApiPropertyOptional({ description: '价格（上架时必填）' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  price?: number;
}

// 球员信息响应DTO
export class HeroInfoDto {
  @ApiProperty({ description: '球员ID' })
  heroId: string;

  @ApiProperty({ description: '球员唯一标识符（与heroId相同，用于兼容性）' })
  uid: string;

  @ApiProperty({ description: '角色ID' })
  characterId: string;

  @ApiProperty({ description: '区服ID' })
  serverId: string;

  @ApiProperty({ description: '配置表ID' })
  resId: number;

  @ApiProperty({ description: '球员名称' })
  name: string;

  @ApiProperty({ description: '位置' })
  position: HeroPosition;

  @ApiProperty({ description: '品质' })
  quality: HeroQuality;

  @ApiProperty({ description: '等级' })
  level: number;

  @ApiProperty({ description: '经验值' })
  exp: number;

  @ApiProperty({ description: '头像' })
  avatar: string;

  @ApiProperty({ description: '国籍' })
  nationality: string;

  @ApiProperty({ description: '俱乐部' })
  club: string;

  @ApiProperty({ description: '属性' })
  attributes: {
    speed: number;
    shooting: number;
    passing: number;
    defending: number;
    dribbling: number;
    physicality: number;
    goalkeeping: number;
  };

  @ApiProperty({ description: '技能列表' })
  skills: Array<{
    skillId: number;
    level: number;
    isActive: boolean;
  }>;

  @ApiProperty({ description: '升星信息' })
  evolution: {
    star: number;
    evolutionExp: number;
    evolutionCount: number;
  };

  @ApiProperty({ description: '总评分' })
  overallRating: number;

  @ApiProperty({ description: '是否锁定' })
  isLocked: boolean;

  @ApiProperty({ description: '是否在阵容中' })
  isInFormation: boolean;

  @ApiProperty({ description: '体力值' })
  energy: number;

  @ApiProperty({ description: '士气值' })
  morale: number;

  @ApiProperty({ description: '装备列表' })
  equipments: string[];

  @ApiProperty({ description: '合同剩余天数' })
  contractDays: number;

  @ApiProperty({ description: '薪水' })
  salary: number;

  @ApiProperty({ description: '市场价值' })
  marketValue: number;

  @ApiProperty({ description: '是否在转会市场' })
  isOnMarket: boolean;

  @ApiProperty({ description: '位置适应性' })
  adaptability: {
    GK: number;   // 门将适应性 (0-100)
    DC: number;   // 中后卫适应性
    DL: number;   // 左后卫适应性
    DR: number;   // 右后卫适应性
    DM: number;   // 后腰适应性
    MC: number;   // 中前卫适应性
    ML: number;   // 左前卫适应性
    MR: number;   // 右前卫适应性
    AM: number;   // 前腰适应性
    ST: number;   // 中锋适应性
    WL: number;   // 左边锋适应性
    WR: number;   // 右边锋适应性
  };

  @ApiProperty({ description: '比赛统计' })
  stats: {
    matchesPlayed: number;
    goals: number;
    assists: number;
    yellowCards: number;
    redCards: number;
  };

  @ApiProperty({ description: '获得时间' })
  obtainTime: number;

  @ApiProperty({ description: '是否可以训练' })
  canTrain: boolean;

  @ApiProperty({ description: '是否可以升星' })
  canEvolve: boolean;
}

// 训练结果DTO
export class TrainResultDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '球员ID' })
  heroId: string;

  @ApiProperty({ description: '训练类型' })
  trainingType: TrainingType;

  @ApiProperty({ description: '训练方式' })
  trainingMethod: TrainingMethod;

  @ApiProperty({ description: '属性提升详情' })
  attributeChanges: Record<string, {
    oldValue: number;
    newValue: number;
    increase: number;
  }>;

  @ApiProperty({ description: '消耗的金币' })
  goldCost: number;

  @ApiProperty({ description: '消耗的道具' })
  itemCost: Array<{
    itemId: string;
    quantity: number;
  }>;

  @ApiProperty({ description: '训练次数' })
  trainCount: number;

  @ApiProperty({ description: '当前训练阶段' })
  currentStage: number;

  @ApiProperty({ description: '是否达到阶段上限' })
  reachedStageLimit: boolean;

  @ApiProperty({ description: '下次训练时间' })
  nextTrainTime: number;

  @ApiProperty({ description: '训练时间' })
  trainingTime: number;
}

// 升星结果DTO
export class EvolveResultDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '球员ID' })
  heroId: string;

  @ApiProperty({ description: '原星级' })
  oldStar: number;

  @ApiProperty({ description: '新星级' })
  newStar: number;

  @ApiProperty({ description: '是否升星成功' })
  isEvolutionSuccess: boolean;

  @ApiProperty({ description: '成功率' })
  successRate: number;

  @ApiProperty({ description: '属性提升' })
  attributeBonus: {
    speed: number;
    shooting: number;
    passing: number;
    defending: number;
    dribbling: number;
    physicality: number;
    goalkeeping: number;
  };

  @ApiProperty({ description: '消耗的球员' })
  consumedHeroes: string[];

  @ApiProperty({ description: '消耗的道具' })
  consumedItems: Array<{
    itemId: string;
    count: number;
  }>;

  @ApiProperty({ description: '消耗的金币' })
  consumedGold: number;

  @ApiProperty({ description: '新的总评分' })
  newOverallRating: number;

  @ApiProperty({ description: '升星时间' })
  evolutionTime: number;
}

// 升级结果DTO
export class LevelUpResultDto {
  @ApiProperty({ description: '是否升级' })
  leveledUp: boolean;

  @ApiProperty({ description: '球员ID' })
  heroId: string;

  @ApiProperty({ description: '旧等级' })
  oldLevel: number;

  @ApiProperty({ description: '新等级' })
  newLevel: number;

  @ApiProperty({ description: '属性提升' })
  attributeIncrease: {
    speed: number;
    shooting: number;
    passing: number;
    defending: number;
    dribbling: number;
    physicality: number;
    goalkeeping: number;
  };

  @ApiProperty({ description: '消耗的经验' })
  expCost: number;

  @ApiProperty({ description: '剩余经验' })
  remainingExp: number;
}

// 球员列表查询DTO
export class GetHeroListDto {
  @ApiPropertyOptional({ description: '角色ID' })
  @IsOptional()
  @IsString()
  characterId?: string;

  @ApiPropertyOptional({ description: '位置过滤' })
  @IsOptional()
  @IsEnum(HeroPosition)
  position?: HeroPosition;

  @ApiPropertyOptional({ description: '品质过滤' })
  @IsOptional()
  @IsEnum(HeroQuality)
  quality?: HeroQuality;

  @ApiPropertyOptional({ description: '是否在阵容中' })
  @IsOptional()
  @IsBoolean()
  isInFormation?: boolean;

  @ApiPropertyOptional({ description: '是否在市场上' })
  @IsOptional()
  @IsBoolean()
  isOnMarket?: boolean;

  @ApiPropertyOptional({ description: '最小等级' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minLevel?: number;

  @ApiPropertyOptional({ description: '最大等级' })
  @IsOptional()
  @IsNumber()
  @Max(100)
  maxLevel?: number;

  @ApiPropertyOptional({ description: '排序字段' })
  @IsOptional()
  @IsString()
  sortBy?: 'level' | 'overallRating' | 'obtainTime' | 'marketValue';

  @ApiPropertyOptional({ description: '排序方向' })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @ApiPropertyOptional({ description: '页码', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}
