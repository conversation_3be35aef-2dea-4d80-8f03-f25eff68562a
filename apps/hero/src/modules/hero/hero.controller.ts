import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { HeroService } from './hero.service';

import { HeroPosition } from '@libs/game-constants';
import { Cache<PERSON>, CacheEvict, CachePut } from '@libs/redis';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import { BaseController } from '@libs/common/controller';

import {
  BreakthroughPayloadDto, 
  CheckContractExpirationPayloadDto,
  CreatePayloadDto,
  EvolvePayloadDto,
  GetBatchPayloadDto,
  GetBreakthroughInfoPayloadDto, 
  GetCareerInfoPayloadDto,
  GetConfigByPositionPayloadDto,
  GetConfigPayloadDto,
  GetEvolutionRequirementsPayloadDto,
  GetInfoPayloadDto,
  GetListPayloadDto,
  GetMarketPayloadDto,
  GetStatsPayloadDto,
  GetStatusPayloadDto, 
  MarketOperationPayloadDto, 
  ProcessRetirementPayloadDto,
  RecalculateAllAttributesPayloadDto,
  RecalculateAttributesPayloadDto,
  RecoverFatiguePayloadDto, RenewCareerPayloadDto,
  RenewContractPayloadDto,
  RevertBreakthroughPayloadDto,
  SetTreatStatusPayloadDto,
  SkillUpgradePayloadDto,
  TreatPayloadDto,
  UpdatePayloadDto,
  GetFormationPayloadDto,
  UpdateFatiguePayloadDto,
  LevelUpPayloadDto
} from "@hero/common/dto/hero-payload.dto";

import { XResponse } from '@libs/common/types/result.type';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class HeroController extends BaseController {
  protected readonly logger = new Logger(HeroController.name);

  constructor(private readonly heroService: HeroService) {
    super('HeroController');
  }

  // ==================== 球员配置管理 ====================

  /**
   * 获取球员配置信息
   */
  @MessagePattern('hero.getConfig')
  @Cacheable({
    key: 'hero:config:#{payload.heroId}',
    dataType: 'global',
    ttl: 3600
  })
  async getHeroConfig(@Payload() payload: GetConfigPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员配置信息: ${payload.heroId}`);
    const result = await this.heroService.getHeroConfig(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 根据位置获取球员配置
   */
  @MessagePattern('hero.getConfigByPosition')
  @Cacheable({
    key: 'hero:config:position:#{payload.position}:#{payload.limit}',
    dataType: 'global',
    ttl: 1800
  })
  async getHeroConfigsByPosition(@Payload() payload: GetConfigByPositionPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`根据位置获取球员配置: ${payload.position}`);
    const result = await this.heroService.getHeroConfigsByPosition(payload.position, payload.limit);
    return this.fromResult(result);
  }

  // ==================== 球员状态管理 ====================

  /**
   * 设置球员治疗状态
   */
  @MessagePattern('hero.setTreatStatus')
  async setHeroTreatStatus(@Payload() payload: SetTreatStatusPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`设置球员治疗状态: ${payload.heroId}, 治疗中: ${payload.isTreat}`);
    const result = await this.heroService.setHeroTreatStatus(payload.heroId, payload.isTreat);
    return this.fromResult(result);
  }

  /**
   * 更新球员疲劳值
   */
  @MessagePattern('hero.updateFatigue')
  async updateHeroFatigue(@Payload() payload: UpdateFatiguePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新球员疲劳值: ${payload.heroId}, 变化: ${payload.fatigueChange}`);
    const result = await this.heroService.updateHeroFatigue(payload.heroId, payload.fatigueChange);
    return this.fromResult(result);
  }



  // ==================== 球员实例管理 ====================

  /**
   * 创建新球员
   */
  @MessagePattern('hero.create')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createHero(@Payload() payload: CreatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`创建球员请求: ${JSON.stringify(payload)}`);
    const result = await this.heroService.createHero(payload);
    return this.fromResult(result);
  }

  /**
   * 批量获取球员信息
   */
  @MessagePattern('hero.getBatch')
  async getBatchHeroes(@Payload() payload: GetBatchPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量获取球员信息: ${payload.heroIds.length}个球员`);
    const result = await this.heroService.getBatchHeroes(payload.heroIds);
    return this.fromResult(result);
  }

  /**
   * 获取球员信息
   */
  @MessagePattern('hero.getInfo')
  @Cacheable({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getHeroInfo(@Payload() payload: GetInfoPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员信息: ${payload.heroId}`);
    const result = await this.heroService.getHeroInfo(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 更新球员信息
   */
  @MessagePattern('hero.update')
  @CachePut({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async updateHero(@Payload() payload: UpdatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新球员信息: ${payload.heroId}`);
    const result = await this.heroService.updateHero(payload.heroId, payload);
    return this.fromResult(result);
  }

  /**
   * 获取球员列表
   */
  @MessagePattern('hero.getList')
  // TODO 条件查询分析缓存必要性
  // @Cacheable({
  //   key: 'character:heroes:#{payload.characterId}:#{payload.page}:#{payload.limit}',
  //   dataType: 'server',
  //   serverId: '#{payload.serverId}',
  //   ttl: 1800
  // })
  async getHeroList(@Payload() payload: GetListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员列表: ${JSON.stringify(payload)}`);
    const result = await this.heroService.getHeroList(payload);
    return this.fromResult(result);
  }



  /**
   * 球员升级
   */
  @MessagePattern('hero.levelUp')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async levelUpHero(@Payload() payload: LevelUpPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`球员升级: ${payload.heroId}`);
      // TODO: 实现球员升级逻辑，使用Result模式
      // const result = await this.heroService.levelUpHero(payload);
      // return this.fromResult(result);
      // 临时实现
      return this.toSuccessResponse({
        leveledUp: true,
        heroId: payload.heroId,
        oldLevel: 1,
        newLevel: 2,
        attributeIncrease: {
          speed: 1,
          shooting: 1,
          passing: 1,
          defending: 1,
          dribbling: 1,
          physicality: 1,
          goalkeeping: 1,
        },
        expCost: 100,
        remainingExp: 0,
      }, '升级成功');
    }, payload);
  }

  /**
   * 技能升级
   */
  @MessagePattern('hero.skill.upgrade')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async upgradeSkill(@Payload() payload: SkillUpgradePayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`技能升级: ${payload.heroId}, 技能: ${payload.skillId}`);
      // TODO: 实现技能升级逻辑，使用Result模式
      // const result = await this.heroService.upgradeSkill(payload);
      // return this.fromResult(result);
      // 临时实现
      return this.toSuccessResponse({
        heroId: payload.heroId,
        skillId: payload.skillId,
        oldLevel: 1,
        newLevel: 2,
        cost: 1000,
      }, '技能升级成功');
    }, payload);
  }

  /**
   * 市场操作
   */
  @MessagePattern('hero.market.operation')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async marketOperation(@Payload() payload: MarketOperationPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`市场操作: ${payload.heroId}, 操作: ${payload.operation}`);
      // TODO: 实现市场操作逻辑，使用Result模式
      // const result = await this.heroService.marketOperation(payload);
      // return this.fromResult(result);
      // 临时实现
      return this.toSuccessResponse({
        heroId: payload.heroId,
        operation: payload.operation,
        price: payload.price,
        success: true,
      }, '操作成功');
    }, payload);
  }

  /**
   * 获取阵容中的球员
   */
  @MessagePattern('hero.getFormation')
  @Cacheable({
    key: 'character:formation:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getFormationHeroes(@Payload() payload: GetFormationPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取阵容球员: ${payload.characterId}`);
      // TODO: 实现获取阵容球员逻辑，使用Result模式
      // const result = await this.heroService.getFormationHeroes(payload);
      // return this.fromResult(result);
      // 临时实现
      return this.toSuccessResponse({
        characterId: payload.characterId,
        heroes: [],
        formation: '4-4-2',
      }, '获取成功');
    }, payload);
  }

  /**
   * 获取市场球员
   */
  @MessagePattern('hero.getMarket')
  @Cacheable({
    key: 'server:market:heroes:#{payload.page}:#{payload.limit}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getMarketHeroes(@Payload() payload: GetMarketPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取市场球员: 页码${payload.page || 1}`);
      // TODO: 实现获取市场球员逻辑，使用Result模式
      // const result = await this.heroService.getMarketHeroes(payload);
      // return this.fromResult(result);
      // 临时实现
      return this.toSuccessResponse({
        list: [],
        total: 0,
        page: payload.page || 1,
        limit: payload.limit || 20,
      }, '获取成功');
    }, payload);
  }

  /**
   * 获取球员统计
   * 基于old项目: 统计角色的球员数据
   */
  @MessagePattern('hero.getStats')
  @Cacheable({
    key: 'character:hero:stats:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getHeroStats(@Payload() payload: GetStatsPayloadDto): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取球员统计: ${payload.characterId}`);
      const result = await this.heroService.getCharacterHeroStats(payload.characterId);
      return this.fromResult(result);
    }, payload);
  }

  // ==================== 升星系统接口 ====================

  /**
   * 获取升星需求
   */
  @MessagePattern('hero.evolution.requirements')
  @Cacheable({
    key: 'hero:evolution:requirements:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getEvolutionRequirements(@Payload() payload: GetEvolutionRequirementsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取升星需求: ${payload.heroId}`);
    const requirements = await this.heroService.getEvolutionRequirements(payload.heroId);
    return this.fromResult(requirements);
  }

  /**
   * 球员升星
   */
  @MessagePattern('hero.evolve')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async evolveHero(@Payload() payload: EvolvePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球员升星: ${payload.heroId}`);
    const result = await this.heroService.evolveHero({
      heroId: payload.heroId,
      materialHeroIds: payload.materialHeroIds,
      useUniversalCard: payload.useUniversalCard,
    });
    return this.fromResult(result);
  }

  // ==================== 状态管理接口 ====================

  /**
   * 获取球员状态
   */
  @MessagePattern('hero.status.get')
  @Cacheable({
    key: 'hero:status:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60
  })
  async getHeroStatus(@Payload() payload: GetStatusPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员状态: ${payload.heroId}`);
    const result = await this.heroService.getHeroStatus(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 治疗球员
   */
  @MessagePattern('hero.treat')
  @CacheEvict({
    key: 'hero:status:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async treatHero(@Payload() payload: TreatPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`治疗球员: ${payload.heroId}, 类型: ${payload.treatmentType}`);
    const result = await this.heroService.treatHero(payload.heroId, payload.treatmentType as any);
    return this.fromResult(result);
  }

  /**
   * 球员续约
   */
  @MessagePattern('hero.contract.renew')
  @CacheEvict({
    key: 'hero:status:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async renewHeroContract(@Payload() payload: RenewContractPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球员续约: ${payload.heroId}, 天数: ${payload.contractDays}`);
    const result = await this.heroService.renewHeroContract(payload.heroId, payload.contractDays);
    return this.fromResult(result);
  }

  /**
   * 恢复球员疲劳
   */
  @MessagePattern('hero.fatigue.recover')
  @CacheEvict({
    key: 'hero:status:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async recoverHeroFatigue(@Payload() payload: RecoverFatiguePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`恢复球员疲劳: ${payload.heroId}, 恢复值: ${payload.recoveryValue}`);
    const result = await this.heroService.recoverHeroFatigue(payload.heroId, payload.recoveryValue);
    return this.fromResult(result);
  }

  // ==================== 突破系统接口 ====================

  /**
   * 获取突破信息
   */
  @MessagePattern('hero.breakthrough.info')
  @Cacheable({
    key: 'hero:breakthrough:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getBreakthroughInfo(@Payload() payload: GetBreakthroughInfoPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取突破信息: ${payload.heroId}`);
    const result = await this.heroService.getBreakthroughInfo(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 球员突破
   */
  @MessagePattern('hero.breakthrough')
  @CacheEvict({
    key: 'hero:breakthrough:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async breakthroughHero(@Payload() payload: BreakthroughPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球员突破: ${payload.heroId}`);
    const result = await this.heroService.breakthroughHero(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 撤销突破
   */
  @MessagePattern('hero.breakthrough.revert')
  @CacheEvict({
    key: 'hero:breakthrough:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async revertBreakthrough(@Payload() payload: RevertBreakthroughPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`撤销突破: ${payload.heroId}`);
    const result = await this.heroService.revertBreakthrough(payload.heroId);
    return this.fromResult(result);
  }

  // ==================== 属性计算接口 ====================

  /**
   * 重新计算球员属性
   */
  @MessagePattern('hero.attributes.recalculate')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async recalculateHeroAttributes(@Payload() payload: RecalculateAttributesPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`重新计算球员属性: ${payload.heroId}`);
    const result = await this.heroService.reCalcHeroAttributes(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 批量重新计算角色所有球员属性
   */
  @MessagePattern('hero.attributes.recalculateAll')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async recalculateAllHeroAttributes(@Payload() payload: RecalculateAllAttributesPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量重新计算球员属性: ${payload.characterId}`);
    const result = await this.heroService.reCalcAllHeroAttributes(payload.characterId);
    return this.fromResult(result);
  }

  // ==================== 生涯管理接口 ====================

  /**
   * 球员续约
   */
  @MessagePattern('hero.career.renew')
  @CacheEvict({
    key: 'hero:status:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async renewHeroCareer(@Payload() payload: RenewCareerPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球员续约: ${payload.heroId}, 天数: ${payload.days || 365}`);
    const result = await this.heroService.addHeroCareerDays(payload.heroId, payload.days);
    return this.fromResult(result);
  }

  /**
   * 获取球员生涯信息
   */
  @MessagePattern('hero.career.info')
  @Cacheable({
    key: 'hero:career:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getHeroCareerInfo(@Payload() payload: GetCareerInfoPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员生涯信息: ${payload.heroId}`);
    const result = await this.heroService.getHeroCareerInfo(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 检查合约到期
   */
  @MessagePattern('hero.career.checkExpiration')
  @Cacheable({
    key: 'character:contract:expiration:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800 // 30分钟
  })
  async checkContractExpiration(@Payload() payload: CheckContractExpirationPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`检查合约到期: ${payload.characterId}`);
    const result = await this.heroService.checkContractExpiration(payload.characterId);
    return this.fromResult(result);
  }

  /**
   * 处理球员退役
   */
  @MessagePattern('hero.career.processRetirement')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async processRetirement(@Payload() payload: ProcessRetirementPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`处理球员退役: ${payload.characterId}`);
    const result = await this.heroService.checkAndProcessRetirement(payload.characterId);
    return this.fromResult(result);
  }

  // ==================== 注意：球探、养成、生涯等功能已迁移到独立模块 ====================
  //
  // 这些功能现在由以下独立模块处理：
  // - ScoutModule: 球探系统相关功能
  // - CultivationModule: 球员养成系统功能
  // - CareerModule: 生涯管理系统功能
  // - TrainingModule: 训练系统功能
  //
  // hero.controller.ts 现在只处理基础的球员CRUD操作
}
