import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { SkillService } from './skill.service';

import { SkillPosition } from '../../common/types';
import { Cacheable, CacheEvict } from '@libs/redis';


import { XResponse } from '@libs/common/types/result.type';

import {
  ActivatePayloadDto, BatchOperationPayloadDto,
  DeactivatePayloadDto,
  GetActivePayloadDto,
  GetConfigByPositionPayloadDto,
  GetConfigListPayloadDto,
  GetConfigPayloadDto,
  GetListPayloadDto, GetStatsPayloadDto,
  LearnPayloadDto, ResetPayloadDto,
  UpgradePayloadDto, UsePayloadDto
} from "@hero/common/dto/skill-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class SkillController extends BaseController {
  constructor(private readonly skillService: SkillService) {
    super('SkillController');
  }

  // ==================== 技能配置管理 ====================

  /**
   * 获取技能配置信息
   */
  @MessagePattern('skill.getConfig')
  @Cacheable({
    key: 'skill:config:#{payload.skillId}',
    dataType: 'global',
    ttl: 3600
  })
  async getSkillConfig(@Payload() payload: GetConfigPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取技能配置信息: ${payload.skillId}`);
    const result = await this.skillService.getSkillConfig(payload.skillId);
    return this.fromResult(result);
  }

  /**
   * 获取技能配置列表
   */
  @MessagePattern('skill.getConfigList')
  @Cacheable({
    key: 'skill:config:list:#{payload.page}:#{payload.limit}:#{payload.type}:#{payload.position}',
    dataType: 'global',
    ttl: 1800
  })
  async getSkillConfigList(@Payload() payload: GetConfigListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取技能配置列表: ${JSON.stringify(payload.query)}`);
    const result = await this.skillService.getSkillConfigList(payload.query);
    return this.fromResult(result);
  }

  /**
   * 根据位置获取技能配置
   */
  @MessagePattern('skill.getConfigByPosition')
  @Cacheable({
    key: 'skill:config:position:#{payload.position}:#{payload.limit}',
    dataType: 'global',
    ttl: 1800
  })
  async getSkillConfigsByPosition(@Payload() payload: GetConfigByPositionPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`根据位置获取技能配置: ${payload.position}`);
    const result = await this.skillService.getSkillConfigsByPosition(payload.position, payload.limit);
    return this.fromResult(result);
  }

  // ==================== 球员技能管理 ====================

  /**
   * 球员学习技能
   */
  @MessagePattern('skill.learn')
  @CacheEvict({ 
    key: 'hero:skills:#{payload.learnDto.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async learnSkill(@Payload() payload: LearnPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`球员学习技能: ${JSON.stringify(payload)}`);
    const result = await this.skillService.learnSkill(payload);
    return this.fromResult(result);
  }

  /**
   * 升级球员技能
   */
  @MessagePattern('skill.upgrade')
  @CacheEvict({ 
    key: 'hero:skill:#{payload.upgradeDto.skillId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async upgradeSkill(@Payload() payload: UpgradePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`升级球员技能: ${JSON.stringify(payload.upgradeDto)}`);
    const result = await this.skillService.upgradeSkill(payload.upgradeDto);
    return this.fromResult(result);
  }

  /**
   * 激活球员技能
   */
  @MessagePattern('skill.activate')
  @CacheEvict({ 
    key: 'hero:skills:active:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async activateSkill(@Payload() payload: ActivatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`激活球员技能: ${JSON.stringify(payload)}`);
    const result = await this.skillService.activateSkill(payload.skillId, payload.slotPosition);
    return this.fromResult(result);
  }

  /**
   * 取消激活球员技能
   */
  @MessagePattern('skill.deactivate')
  @CacheEvict({ 
    key: 'hero:skills:active:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deactivateSkill(@Payload() payload: DeactivatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`取消激活球员技能: ${payload.skillId}`);
    const result = await this.skillService.deactivateSkill(payload.skillId);
    return this.fromResult(result);
  }

  /**
   * 获取球员技能列表
   */
  @MessagePattern('skill.getList')
  @Cacheable({ 
    key: 'hero:skills:#{payload.heroId}:#{payload.activeStatus}:#{payload.equippedOnly}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getSkillList(@Payload() payload: GetListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员技能列表: ${JSON.stringify(payload)}`);
    const result = await this.skillService.getSkillList(payload);
    return this.fromResult(result);
  }

  /**
   * 获取球员已激活的技能
   */
  @MessagePattern('skill.getActive')
  @Cacheable({ 
    key: 'hero:skills:active:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getActiveSkills(@Payload() payload: GetActivePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员已激活技能: ${payload.heroId}`);
    const result = await this.skillService.getActiveSkills(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 重置球员技能
   */
  @MessagePattern('skill.reset')
  @CacheEvict({ 
    key: 'hero:skills:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async resetSkills(@Payload() payload: ResetPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`重置球员技能: ${payload.heroId}, 类型: ${payload.resetType}`);
    // TODO: 实现重置球员技能逻辑
    return this.toSuccessResponse(undefined);
  }

  /**
   * 批量操作球员技能
   */
  @MessagePattern('skill.batchOperation')
  @CacheEvict({ 
    key: 'hero:skills:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchOperateSkills(@Payload() payload: BatchOperationPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量操作球员技能: ${payload.operation}, 数量: ${payload.skillIds.length}`);
    // TODO: 实现批量操作球员技能逻辑
    return this.toSuccessResponse(undefined);
  }

  /**
   * 获取技能统计
   */
  @MessagePattern('skill.getStats')
  @Cacheable({ 
    key: 'hero:skill:stats:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async getSkillStats(@Payload() payload: GetStatsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取技能统计: ${payload.heroId}`);
    const stats = await this.skillService.getSkillStats(payload.heroId);
    return this.toSuccessResponse(stats);
  }

  /**
   * 使用技能
   */
  @MessagePattern('skill.use')
  async useSkill(@Payload() payload: UsePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`使用技能: ${payload.skillId}, 球员: ${payload.heroId}`);
    // TODO: 实现使用技能逻辑
    return this.toSuccessResponse({
      skillId: payload.skillId,
      heroId: payload.heroId,
      damage: 0,
      healing: 0,
      effects: [],
    });
  }
}
