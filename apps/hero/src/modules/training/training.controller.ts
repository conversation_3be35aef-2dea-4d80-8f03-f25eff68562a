/*
 * @Author: colorinstance <EMAIL>
 * @Date: 2025-08-19 22:37:25
 * @LastEditors: colorinstance <EMAIL>
 * @LastEditTime: 2025-09-06 19:05:48
 * @FilePath: \server-new\apps\hero\src\modules\training\training.controller.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CacheEvict, Cacheable } from '@libs/redis';
import { TrainingService } from './training.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  GetTrainInfoPayloadDto,
  ReplaceHeroTrainPayloadDto,
  SetTrainStatusPayloadDto,
  TrainPayloadDto
} from "@hero/common/dto/training-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

/**
 * 球员训练控制器
 * 基于old项目的训练相关功能，从hero模块迁移而来
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class TrainingController extends BaseController {
  constructor(private readonly trainingService: TrainingService) {
    super('TrainingController');
  }

  /**
   * 获取球员特训信息
   * 对应old项目: getTrainInfo
   */
  @MessagePattern('training.getTrainInfo')
  @Cacheable({
    key: 'hero:train:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getTrainInfo(@Payload() payload: GetTrainInfoPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取球员特训信息: ${payload.heroId}`);
    const result = await this.trainingService.getTrainInfo(payload.heroId);
    return this.fromResult(result);
  }

  /**
   * 训练球员
   * 对应old项目: trainHero
   */
  @MessagePattern('training.train')
  @CacheEvict({
    key: 'hero:info:#{payload.trainDto.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async trainHero(@Payload() payload: TrainPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`训练球员: ${payload.heroId}, 类型: ${payload.trainingType}`);
    const result = await this.trainingService.trainHero(payload);
    return this.fromResult(result);
  }

  /**
   * 替换特训
   * 对应old项目: replaceHeroTrain
   */
  @MessagePattern('training.replaceHeroTrain')
  @CacheEvict({
    key: 'hero:info:#{payload.heroId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async replaceHeroTrain(@Payload() payload: ReplaceHeroTrainPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`替换特训: ${payload.heroId}, 方式: ${payload.index}`);
    const result = await this.trainingService.replaceHeroTrain(payload.heroId, payload.index);
    return this.fromResult(result);
  }

  /**
   * 设置球员训练状态
   * 从hero模块迁移而来
   */
  @MessagePattern('training.setTrainStatus')
  async setHeroTrainStatus(@Payload() payload: SetTrainStatusPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`设置球员训练状态: ${payload.heroId}, 训练中: ${payload.isTrain}`);
    const result = await this.trainingService.setHeroTrainStatus(payload.heroId, payload.isTrain, payload.isLockTrain);
    return this.fromResult(result);
  }
}
