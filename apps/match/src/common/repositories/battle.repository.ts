import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, ClientSession } from 'mongoose';
import { BattleRoom } from '../schemas/battle.schema';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 战斗数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 战斗房间CRUD操作
 * - 战斗记录查询和统计
 * - 战斗历史管理
 * - 过期房间清理
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 */
@Injectable()
export class BattleRepository extends BaseRepository<BattleRoom> {
  constructor(
    @InjectModel(BattleRoom.name) battleRoomModel: Model<BattleRoom>
  ) {
    super(battleRoomModel, 'BattleRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 根据房间UID查找战斗房间
   * 使用BaseRepository的findOne方法优化性能
   */
  async findByRoomId(roomId: string): Promise<XResult<BattleRoom | null>> {
    return this.findOne({ roomId: roomId });
  }

  /**
   * 根据房间UID查找战斗房间（Lean查询优化版本）
   * 性能提升60%+，适用于只需要数据而不需要Mongoose文档方法的场景
   */
  async findByRoomIdLean(roomId: string): Promise<XResult<any | null>> {
    return this.findOneLean({ roomId: roomId });
  }

  /**
   * 创建新的战斗房间
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async create(battleRoomData: Partial<BattleRoom>): Promise<XResult<BattleRoom>> {
    return this.createOne(battleRoomData);
  }

  /**
   * 更新战斗房间数据
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateByRoomId(
    roomId: string,
    updateData: Partial<BattleRoom>,
    session?: ClientSession
  ): Promise<XResult<BattleRoom | null>> {
    return this.updateOne(
      { roomId: roomId },
      { ...updateData, finishedAt: new Date() },
      session
    );
  }

  /**
   * 删除战斗房间
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async deleteByRoomId(roomId: string): Promise<XResult<boolean>> {
    const result = await this.deleteOne({ roomId: roomId });
    if (XResultUtils.isSuccess(result)) {
      return XResultUtils.ok(result.data !== null);
    }
    return XResultUtils.ok(false);
  }

  /**
   * 根据玩家ID查找战斗记录
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByCharacterId(characterId: string, limit: number = 10): Promise<XResult<BattleRoom[]>> {
    return this.findMany({
      $or: [
        { 'teamA.characterId': characterId },
        { 'teamB.characterId': characterId }
      ],
      status: 'finished'
    }, {
      sort: { finishedAt: -1 },
      limit
    });
  }

  /**
   * 根据玩家ID查找战斗记录（Lean查询优化版本）
   */
  async findByCharacterIdLean(characterId: string, limit: number = 10): Promise<XResult<any[]>> {
    return this.findManyLean({
      $or: [
        { 'teamA.characterId': characterId },
        { 'teamB.characterId': characterId }
      ],
      status: 'finished'
    }, {
      sort: { finishedAt: -1 },
      limit,
      select: ['roomId', 'battleType', 'status', 'finishedAt', 'teamA', 'teamB', 'result']
    });
  }

  /**
   * 根据战斗类型查找战斗记录
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByBattleType(battleType: number, limit: number = 50): Promise<XResult<BattleRoom[]>> {
    return this.findMany({
      battleType,
      status: 'finished'
    }, {
      sort: { finishedAt: -1 },
      limit
    });
  }

  /**
   * 根据战斗类型查找战斗记录（Lean查询优化版本）
   */
  async findByBattleTypeLean(battleType: number, limit: number = 50): Promise<XResult<any[]>> {
    return this.findManyLean({
      battleType,
      status: 'finished'
    }, {
      sort: { finishedAt: -1 },
      limit,
      select: ['roomId', 'battleType', 'status', 'finishedAt', 'teamA', 'teamB', 'result']
    });
  }

  /**
   * 清理过期的战斗房间
   * 使用BaseRepository的deleteMany方法优化性能
   */
  async cleanExpiredRooms(expireHours: number = 24): Promise<XResult<number>> {
    const expireTime = new Date(Date.now() - expireHours * 60 * 60 * 1000);

    const result = await this.deleteMany({
      status: 'active',
      createdAt: { $lt: expireTime }
    });

    if (XResultUtils.isSuccess(result)) {
      const deletedCount = result.data.deletedCount || 0;
      this.logger.log(`清理过期战斗房间: ${deletedCount}个`);
      return XResultUtils.ok(deletedCount);
    }

    return XResultUtils.ok(0);
  }

  /**
   * 获取战斗统计信息
   * 使用BaseRepository的count方法和并行查询优化性能
   */
  async getStatistics(): Promise<XResult<any>> {
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    // 并行执行所有统计查询
    const [totalResult, activeResult, finishedResult, todayResult] = await Promise.all([
      this.count({}),
      this.count({ status: 'active' }),
      this.count({ status: 'finished' }),
      this.count({
        createdAt: { $gte: todayStart },
        status: 'finished'
      })
    ]);

    // 检查所有查询是否成功
    if (XResultUtils.isFailure(totalResult) ||
        XResultUtils.isFailure(activeResult) ||
        XResultUtils.isFailure(finishedResult) ||
        XResultUtils.isFailure(todayResult)) {
      return XResultUtils.error('获取战斗统计信息失败', 'STATISTICS_QUERY_FAILED');
    }

    return XResultUtils.ok({
      totalRooms: totalResult.data,
      activeRooms: activeResult.data,
      finishedRooms: finishedResult.data,
      todayBattles: todayResult.data,
      timestamp: new Date(),
    });
  }

  /**
   * 批量查询战斗房间
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByRoomIds(roomIds: string[]): Promise<XResult<BattleRoom[]>> {
    return this.findMany({ roomId: { $in: roomIds } });
  }

  /**
   * 批量查询战斗房间（Lean查询优化版本）
   */
  async findByRoomIdsLean(roomIds: string[]): Promise<XResult<any[]>> {
    return this.findManyLean({ roomId: { $in: roomIds } }, {
      select: ['roomId', 'battleType', 'status', 'finishedAt', 'teamA', 'teamB', 'result']
    });
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加战斗房间特定的验证规则
   */
  protected validateData(data: Partial<BattleRoom>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.roomId) {
        return XResultUtils.error('房间UID不能为空', 'ROOM_UID_REQUIRED');
      }

      if (!data.battleType) {
        return XResultUtils.error('战斗类型不能为空', 'BATTLE_TYPE_REQUIRED');
      }

      if (!data.teamA || !data.teamB) {
        return XResultUtils.error('战斗双方队伍信息不能为空', 'TEAM_INFO_REQUIRED');
      }
    }

    if (data.battleType !== undefined && (data.battleType < 1 || data.battleType > 10)) {
      return XResultUtils.error('战斗类型必须在1-10之间', 'INVALID_BATTLE_TYPE');
    }

    if (data.status && !['active', 'finished', 'cancelled'].includes(data.status)) {
      return XResultUtils.error('无效的战斗状态', 'INVALID_BATTLE_STATUS');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对战斗数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findByRoomId': 300,              // 房间详情缓存5分钟
      'findByRoomIdLean': 180,          // 房间简介缓存3分钟
      'findByCharacterId': 120,          // 玩家战斗记录缓存2分钟
      'findByBattleType': 300,           // 战斗类型记录缓存5分钟
      'getStatistics': 60,               // 统计信息缓存1分钟
      'findByRoomIds': 180,             // 批量查询缓存3分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
