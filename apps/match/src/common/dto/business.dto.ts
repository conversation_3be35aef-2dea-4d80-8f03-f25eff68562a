import { IsString, <PERSON>N<PERSON>ber, IsOptional, IsArray, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 商业赛系统相关DTO
 * 基于old项目businessMatch.js的接口定义
 */

/**
 * 获取商业赛信息请求DTO
 */
export class GetBusinessMatchInfoDto {
  @IsString()
  characterId: string;

  @IsOptional()
  @IsString()
  serverId?: string;
}

/**
 * 商业赛搜索请求DTO
 */
export class BusinessSearchDto {
  @IsString()
  characterId: string;

  @IsString()
  name: string;                          // 搜索的对手名称

  @IsOptional()
  @IsString()
  serverId?: string;
}

/**
 * 商业赛匹配请求DTO
 */
export class BusinessMatchDto {
  @IsString()
  characterId: string;

  @IsString()
  enemyUid: string;                      // 对手UID

  @IsOptional()
  @IsString()
  serverId?: string;
}

/**
 * 购买商业赛次数请求DTO
 */
export class BuyBusinessMatchDto {
  @IsString()
  characterId: string;

  @IsNumber()
  num: number;                           // 购买次数

  @IsOptional()
  @IsString()
  serverId?: string;
}

/**
 * 商业赛匹配记录响应DTO
 */
export class BusinessMatchRecordDto {
  @IsString()
  characterId: string;

  @IsString()
  roomId: string;

  @IsString()
  teamA: string;

  @IsString()
  teamB: string;

  @IsNumber()
  result: number;

  @IsNumber()
  teamAScore: number;

  @IsNumber()
  teamBScore: number;

  @IsString()
  beginTime: string;

  @IsNumber()
  teamARank: number;

  @IsNumber()
  teamBRank: number;

  @IsNumber()
  fansChangeNum: number;

  @IsNumber()
  cash: number;
}

/**
 * 商业赛对手信息响应DTO
 */
export class BusinessRivalInfoDto {
  @IsString()
  characterId: string;

  @IsString()
  name: string;

  @IsNumber()
  faceIcon: number;

  @IsNumber()
  actualStrength: number;

  @IsNumber()
  ballFanCount: number;

  @IsNumber()
  fanRank: number;

  @IsBoolean()
  isGroundOpen: boolean;
}

/**
 * 商业赛战斗次数信息响应DTO
 */
export class BusinessFightTimesDto {
  @IsNumber()
  totalTimes: number;

  @IsNumber()
  leftTimes: number;

  @IsNumber()
  usedTimes: number;

  @IsString()
  lastResetTime: string;
}

/**
 * 获取商业赛信息响应DTO
 */
export class GetBusinessMatchInfoResponseDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessFightTimesDto)
  fightTimesInfo?: BusinessFightTimesDto;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessMatchRecordDto)
  lastMatchRecord?: BusinessMatchRecordDto[];

  @IsOptional()
  @IsNumber()
  searchCost?: number;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessRivalInfoDto)
  rankList?: BusinessRivalInfoDto[];
}

/**
 * 商业赛搜索响应DTO
 */
export class BusinessSearchResponseDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessRivalInfoDto)
  enemyInfo?: BusinessRivalInfoDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessFightTimesDto)
  fightTimesInfo?: BusinessFightTimesDto;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessRivalInfoDto)
  rivalList?: BusinessRivalInfoDto[];
}

/**
 * 商业赛匹配响应DTO
 */
export class BusinessMatchResponseDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessRivalInfoDto)
  enemyInfo?: BusinessRivalInfoDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessMatchRecordDto)
  matchRecord?: BusinessMatchRecordDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessFightTimesDto)
  fightTimesInfo?: BusinessFightTimesDto;

  @IsOptional()
  rewardInfo?: any;                      // 奖励信息
}
