/**
 * League模块的Payload DTO定义
 * 
 * 为league.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 联赛副本数据相关 ====================

/**
 * 获取联赛副本数据Payload DTO
 * @MessagePattern('league.getLeagueCopyData')
 * 扩展GetLeagueCopyDataDto，合并BasePayloadDto内容
 */
export class GetLeagueCopyDataPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;

  @ApiPropertyOptional({ description: '类型（可选）1普通 2巅峰', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '类型必须是数字' })
  type?: number;
}

// ==================== 2. PVE联赛战斗相关 ====================

/**
 * PVE联赛战斗Payload DTO
 * @MessagePattern('league.pveBattle')
 * 扩展PVELeagueBattleDto，合并BasePayloadDto内容
 */
export class PveBattlePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '联赛ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '联赛ID必须是数字' })
  leagueId: number;

  @ApiProperty({ description: '球队副本ID', example: 2001 })
  @Expose()
  @IsNumber({}, { message: '球队副本ID必须是数字' })
  teamCopyId: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 3. 联赛奖励相关 ====================

/**
 * 领取联赛奖励Payload DTO
 * @MessagePattern('league.takeLeagueReward')
 * 扩展TakeLeagueRewardDto，合并BasePayloadDto内容
 */
export class TakeLeagueRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '联赛ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '联赛ID必须是数字' })
  leagueId: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 4. 购买联赛次数相关 ====================

/**
 * 购买联赛次数Payload DTO
 * @MessagePattern('league.buyLeagueTimes')
 * 扩展BuyLeagueTimesDto，合并BasePayloadDto内容
 */
export class BuyLeagueTimesPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '购买次数', example: 5 })
  @Expose()
  @IsNumber({}, { message: '购买次数必须是数字' })
  times: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 5. 统计信息相关 ====================

/**
 * 获取联赛统计信息Payload DTO
 * @MessagePattern('league.getStatistics')
 */
export class GetStatisticsPayloadDto extends BasePayloadDto {
  // 仅继承BasePayloadDto，无需额外字段
}
