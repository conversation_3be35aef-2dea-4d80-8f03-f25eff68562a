import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, <PERSON>, <PERSON>, IsIn } from 'class-validator';
import { BasePayloadDto } from '@libs/common/dto';

/**
 * 获取信仰之战信息载荷DTO
 */
export class GetWarOfFaithInfoPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId
}

/**
 * 加入信仰之战载荷DTO
 */
export class JoinWarOfFaithPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId
}

/**
 * 退出信仰之战载荷DTO
 */
export class LeaveWarOfFaithPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId
}

/**
 * 挑战占领者载荷DTO
 */
export class ChallengeOccupierPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '目标信仰ID必须大于0' })
  targetBeliefId: number;

  @IsOptional()
  @IsString()
  formationId?: string;
}

/**
 * 获取战斗记录载荷DTO
 */
export class GetWarBattleHistoryPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number;

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId?: number;
}

/**
 * 获取参赛选手载荷DTO
 */
export class GetWarParticipantsPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId?: number;
}

/**
 * 获取占领历史载荷DTO
 */
export class GetOccupationHistoryPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId?: number;
}

/**
 * 获取挑战信息载荷DTO
 */
export class GetChallengeInfoPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId
}

/**
 * 获取战争统计载荷DTO
 */
export class GetWarStatsPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId
}

/**
 * 开始新轮战争载荷DTO
 */
export class StartNewWarRoundPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '赛季必须大于0' })
  season?: number;

  @IsOptional()
  @IsNumber()
  @Min(60000, { message: '持续时间至少1分钟' })
  duration?: number;
}

/**
 * 结束当前战争载荷DTO
 */
export class EndCurrentWarPayloadDto extends BasePayloadDto {
  // 只需要基础的serverId
}
