import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 商业赛匹配记录
 * 基于old项目businessMatch.js的matchRecord结构
 */
@Schema({ _id: false })
export class BusinessMatchRecord {
  @Prop({ required: true })
  characterId: string;                      // 玩家ID

  @Prop({ required: true })
  roomId: string;                       // 房间UID

  @Prop({ required: true })
  teamA: string;                         // A队玩家ID

  @Prop({ required: true })
  teamB: string;                         // B队玩家ID

  @Prop({ default: 0 })
  result: number;                        // 比赛结果 (0平局 1胜利 2失败)

  @Prop({ default: 0 })
  teamAScore: number;                    // A队比分

  @Prop({ default: 0 })
  teamBScore: number;                    // B队比分

  @Prop({ default: Date.now })
  beginTime: Date;                       // 比赛开始时间

  @Prop({ default: 0 })
  teamARank: number;                     // A队排名

  @Prop({ default: 0 })
  teamBRank: number;                     // B队排名

  @Prop({ default: 0 })
  fansChangeNum: number;                 // 球迷变化数量

  @Prop({ default: 0 })
  cash: number;                          // 获得的现金奖励
}

/**
 * 商业赛对手信息
 * 基于old项目的敌方信息结构
 */
@Schema({ _id: false })
export class BusinessRivalInfo {
  @Prop({ required: true })
  characterId: string;                     // 对手玩家ID

  @Prop({ default: '' })
  name: string;                          // 对手名称

  @Prop({ default: 0 })
  faceIcon: number;                      // 头像图标

  @Prop({ default: 0 })
  actualStrength: number;                // 实际战力

  @Prop({ default: 0 })
  ballFanCount: number;                  // 球迷数量

  @Prop({ default: 0 })
  fanRank: number;                       // 球迷排名

  @Prop({ default: false })
  isGroundOpen: boolean;                 // 球场是否开放

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

/**
 * 商业赛战斗次数信息
 * 基于old项目的fightTimesInfo结构
 */
@Schema({ _id: false })
export class BusinessFightTimes {
  @Prop({ default: 0 })
  totalTimes: number;                    // 总次数

  @Prop({ default: 0 })
  leftTimes: number;                     // 剩余次数

  @Prop({ default: 0 })
  usedTimes: number;                     // 已使用次数

  @Prop({ default: Date.now })
  lastResetTime: Date;                   // 最后重置时间
}

/**
 * 商业赛奖励信息
 * 基于old项目的businessRewardInfo结构
 */
@Schema({ _id: false })
export class BusinessRewardInfo {
  @Prop({ default: 0 })
  totalCash: number;                     // 总现金

  @Prop({ default: 0 })
  winCash: number;                       // 胜利现金

  @Prop({ default: 0 })
  loseCash: number;                      // 失败现金

  @Prop({ default: 0 })
  drawCash: number;                      // 平局现金

  @Prop({ default: 0 })
  matchWinRatio: number;                 // 胜利比例

  @Prop({ default: 0 })
  matchLoseRatio: number;                // 失败比例

  @Prop({ default: 0 })
  matchDrawRatio: number;                // 平局比例

  @Prop({ default: 0 })
  enemyFansCount: number;                // 敌方球迷数量

  @Prop({ default: 0 })
  myFansCount: number;                   // 我方球迷数量

  @Prop({ default: 0 })
  fansQ: number;                         // 球迷质量系数
}

/**
 * 商业赛主文档
 * 基于old项目businessMatch.js的主要数据结构
 */
@Schema({
  collection: 'business_matches',
  timestamps: true,
  versionKey: false,
})
export class BusinessMatch extends Document {
  @Prop({ required: true, unique: true })
  uid: string;                           // 玩家UID

  @Prop({ default: 0 })
  searchCost: number;                    // 搜索费用

  @Prop({ type: [BusinessMatchRecord], default: [] })
  matchRecordList: BusinessMatchRecord[]; // 匹配记录列表（最多30条）

  @Prop({ type: [BusinessRivalInfo], default: [] })
  rivalList: BusinessRivalInfo[];        // 对手列表

  @Prop({ type: BusinessFightTimes })
  fightTimes: BusinessFightTimes;        // 战斗次数信息

  @Prop({ default: 30 })
  maxRecordNum: number;                  // 最大记录数量

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

export const BusinessMatchSchema = SchemaFactory.createForClass(BusinessMatch);

// 定义方法接口 - 基于BusinessService的真实业务逻辑
export interface BusinessMatchMethods {
  // 战斗次数管理 - 基于BusinessService
  canFight(): boolean;
  consumeFightTime(): boolean;
  addFightTimes(times: number): void;
  resetDailyFightTimes(): void;
  getFightTimesInfo(): any;

  // 匹配记录管理 - 基于BusinessService
  addMatchRecord(record: BusinessMatchRecord): void;
  getLastMatchRecords(limit: number): BusinessMatchRecord[];
  getMatchRecordByRoomId(roomId: string): BusinessMatchRecord | null;
  removeOldRecords(): void;

  // 对手管理 - 基于BusinessService
  addRival(rival: BusinessRivalInfo): void;
  removeRival(characterId: string): boolean;
  getRival(characterId: string): BusinessRivalInfo | null;
  updateRivalInfo(characterId: string, updates: Partial<BusinessRivalInfo>): boolean;

  // 统计分析 - 基于BusinessService
  getWinRate(): number;
  getTotalMatches(): number;
  getWinCount(): number;
  getLossCount(): number;
  getDrawCount(): number;
  getTotalCashEarned(): number;

  // 数据验证 - 基于BusinessService
  isSearchCostValid(): boolean;
  needsDailyReset(): boolean;

  // 数据转换 - 基于BusinessService
  toClientBusinessInfo(): any;
  getBusinessStats(): any;
}

// 定义Document类型
export type BusinessMatchDocument = BusinessMatch & Document & BusinessMatchMethods;

// 创建索引
BusinessMatchSchema.index({ uid: 1 });
BusinessMatchSchema.index({ 'matchRecordList.roomId': 1 });
BusinessMatchSchema.index({ 'matchRecordList.beginTime': -1 });
BusinessMatchSchema.index({ lastUpdateTime: 1 });

// ==================== 实例方法实现 ====================

/**
 * 检查是否可以战斗
 * 基于BusinessService: 战斗次数检查逻辑
 */
BusinessMatchSchema.methods.canFight = function(): boolean {
  if (!this.fightTimes) return false;
  return this.fightTimes.leftTimes > 0;
};

/**
 * 消耗战斗次数
 * 基于BusinessService: 战斗次数消耗逻辑
 */
BusinessMatchSchema.methods.consumeFightTime = function(): boolean {
  if (!this.canFight()) return false;

  this.fightTimes.leftTimes -= 1;
  this.fightTimes.usedTimes += 1;
  this.lastUpdateTime = new Date();
  return true;
};

/**
 * 增加战斗次数
 * 基于BusinessService: buyBusinessMatch方法逻辑
 */
BusinessMatchSchema.methods.addFightTimes = function(times: number): void {
  if (!this.fightTimes) {
    this.fightTimes = {
      totalTimes: times,
      leftTimes: times,
      usedTimes: 0,
      lastResetTime: new Date()
    };
  } else {
    this.fightTimes.totalTimes += times;
    this.fightTimes.leftTimes += times;
  }
  this.lastUpdateTime = new Date();
};

/**
 * 重置每日战斗次数
 * 基于BusinessService: getFightTimesInfo方法中的重置逻辑
 */
BusinessMatchSchema.methods.resetDailyFightTimes = function(): void {
  this.fightTimes = {
    totalTimes: 5, // 默认每日5次
    leftTimes: 5,
    usedTimes: 0,
    lastResetTime: new Date()
  };
  this.lastUpdateTime = new Date();
};

/**
 * 获取战斗次数信息
 * 基于BusinessService: getFightTimesInfo方法逻辑
 */
BusinessMatchSchema.methods.getFightTimesInfo = function(): any {
  // 检查是否需要每日重置
  if (this.needsDailyReset()) {
    this.resetDailyFightTimes();
  }

  return {
    totalTimes: this.fightTimes?.totalTimes || 0,
    leftTimes: this.fightTimes?.leftTimes || 0,
    usedTimes: this.fightTimes?.usedTimes || 0,
    lastResetTime: this.fightTimes?.lastResetTime || new Date()
  };
};

/**
 * 添加匹配记录
 * 基于BusinessService: processBattleResult方法逻辑
 */
BusinessMatchSchema.methods.addMatchRecord = function(record: BusinessMatchRecord): void {
  this.matchRecordList.unshift(record); // 添加到开头

  // 保持最大记录数量限制
  if (this.matchRecordList.length > this.maxRecordNum) {
    this.matchRecordList = this.matchRecordList.slice(0, this.maxRecordNum);
  }

  this.lastUpdateTime = new Date();
};

/**
 * 获取最近的匹配记录
 * 基于BusinessService: getBusinessMatchInfo方法逻辑
 */
BusinessMatchSchema.methods.getLastMatchRecords = function(limit: number): BusinessMatchRecord[] {
  return this.matchRecordList
    .sort((a, b) => b.beginTime.getTime() - a.beginTime.getTime())
    .slice(0, limit);
};

/**
 * 根据房间ID获取匹配记录
 * 基于BusinessService: 匹配记录查询逻辑
 */
BusinessMatchSchema.methods.getMatchRecordByRoomId = function(roomId: string): BusinessMatchRecord | null {
  return this.matchRecordList.find(record => record.roomId === roomId) || null;
};

/**
 * 移除旧记录
 * 基于BusinessService: 记录管理逻辑
 */
BusinessMatchSchema.methods.removeOldRecords = function(): void {
  // 保留最近30条记录
  if (this.matchRecordList.length > this.maxRecordNum) {
    this.matchRecordList = this.matchRecordList
      .sort((a, b) => b.beginTime.getTime() - a.beginTime.getTime())
      .slice(0, this.maxRecordNum);
    this.lastUpdateTime = new Date();
  }
};

/**
 * 添加对手
 * 基于BusinessService: 对手管理逻辑
 */
BusinessMatchSchema.methods.addRival = function(rival: BusinessRivalInfo): void {
  // 检查是否已存在
  const existingIndex = this.rivalList.findIndex(r => r.characterId === rival.characterId);
  if (existingIndex !== -1) {
    // 更新现有对手信息
    this.rivalList[existingIndex] = rival;
  } else {
    this.rivalList.push(rival);
  }
  this.lastUpdateTime = new Date();
};

/**
 * 移除对手
 * 基于BusinessService: 对手管理逻辑
 */
BusinessMatchSchema.methods.removeRival = function(characterId: string): boolean {
  const index = this.rivalList.findIndex(rival => rival.characterId === characterId);
  if (index === -1) return false;

  this.rivalList.splice(index, 1);
  this.lastUpdateTime = new Date();
  return true;
};

/**
 * 获取对手信息
 * 基于BusinessService: 对手查询逻辑
 */
BusinessMatchSchema.methods.getRival = function(characterId: string): BusinessRivalInfo | null {
  return this.rivalList.find(rival => rival.characterId === characterId) || null;
};

/**
 * 更新对手信息
 * 基于BusinessService: 对手信息更新逻辑
 */
BusinessMatchSchema.methods.updateRivalInfo = function(characterId: string, updates: Partial<BusinessRivalInfo>): boolean {
  const rival = this.getRival(characterId);
  if (!rival) return false;

  Object.assign(rival, updates);
  rival.lastUpdateTime = new Date();
  this.lastUpdateTime = new Date();
  return true;
};

/**
 * 获取胜率
 * 基于BusinessService: 统计分析逻辑
 */
BusinessMatchSchema.methods.getWinRate = function(): number {
  const totalMatches = this.getTotalMatches();
  if (totalMatches === 0) return 0;

  const winCount = this.getWinCount();
  return Math.round((winCount / totalMatches) * 100);
};

/**
 * 获取总比赛数
 * 基于BusinessService: 统计分析逻辑
 */
BusinessMatchSchema.methods.getTotalMatches = function(): number {
  return this.matchRecordList.length;
};

/**
 * 获取胜利次数
 * 基于BusinessService: 统计分析逻辑
 */
BusinessMatchSchema.methods.getWinCount = function(): number {
  return this.matchRecordList.filter(record => record.result === 1).length;
};

/**
 * 获取失败次数
 * 基于BusinessService: 统计分析逻辑
 */
BusinessMatchSchema.methods.getLossCount = function(): number {
  return this.matchRecordList.filter(record => record.result === 2).length;
};

/**
 * 获取平局次数
 * 基于BusinessService: 统计分析逻辑
 */
BusinessMatchSchema.methods.getDrawCount = function(): number {
  return this.matchRecordList.filter(record => record.result === 0).length;
};

/**
 * 获取总收入
 * 基于BusinessService: 奖励统计逻辑
 */
BusinessMatchSchema.methods.getTotalCashEarned = function(): number {
  return this.matchRecordList.reduce((total, record) => total + (record.cash || 0), 0);
};

/**
 * 检查搜索费用是否有效
 * 基于BusinessService: 搜索费用验证逻辑
 */
BusinessMatchSchema.methods.isSearchCostValid = function(): boolean {
  return this.searchCost > 0;
};

/**
 * 检查是否需要每日重置
 * 基于BusinessService: getFightTimesInfo方法中的重置检查逻辑
 */
BusinessMatchSchema.methods.needsDailyReset = function(): boolean {
  if (!this.fightTimes || !this.fightTimes.lastResetTime) return true;

  const now = new Date();
  const lastReset = new Date(this.fightTimes.lastResetTime);

  return now.getDate() !== lastReset.getDate() ||
         now.getMonth() !== lastReset.getMonth() ||
         now.getFullYear() !== lastReset.getFullYear();
};

/**
 * 转换为客户端商业赛信息
 * 基于BusinessService: getBusinessMatchInfo方法逻辑
 */
BusinessMatchSchema.methods.toClientBusinessInfo = function(): any {
  const fightTimesInfo = this.getFightTimesInfo();

  return {
    uid: this.uid,
    searchCost: this.searchCost,
    fightTimesInfo: {
      totalTimes: fightTimesInfo.totalTimes,
      leftTimes: fightTimesInfo.leftTimes,
      usedTimes: fightTimesInfo.usedTimes,
      lastResetTime: fightTimesInfo.lastResetTime.toISOString()
    },
    lastMatchRecord: this.getLastMatchRecords(5).map(record => ({
      characterId: record.characterId,
      roomId: record.roomId,
      teamA: record.teamA,
      teamB: record.teamB,
      result: record.result,
      teamAScore: record.teamAScore,
      teamBScore: record.teamBScore,
      beginTime: record.beginTime.toISOString(),
      teamARank: record.teamARank,
      teamBRank: record.teamBRank,
      fansChangeNum: record.fansChangeNum,
      cash: record.cash
    })),
    rivalList: this.rivalList.map(rival => ({
      characterId: rival.characterId,
      name: rival.name,
      faceIcon: rival.faceIcon,
      actualStrength: rival.actualStrength,
      ballFanCount: rival.ballFanCount,
      fanRank: rival.fanRank,
      isGroundOpen: rival.isGroundOpen
    })),
    stats: this.getBusinessStats()
  };
};

/**
 * 获取商业赛统计数据
 * 基于BusinessService: 统计分析需求
 */
BusinessMatchSchema.methods.getBusinessStats = function(): any {
  return {
    totalMatches: this.getTotalMatches(),
    winCount: this.getWinCount(),
    lossCount: this.getLossCount(),
    drawCount: this.getDrawCount(),
    winRate: this.getWinRate(),
    totalCashEarned: this.getTotalCashEarned(),
    averageCashPerMatch: this.getTotalMatches() > 0 ?
      Math.round(this.getTotalCashEarned() / this.getTotalMatches()) : 0,
    rivalCount: this.rivalList.length,
    lastMatchTime: this.matchRecordList.length > 0 ?
      Math.max(...this.matchRecordList.map(r => r.beginTime.getTime())) : null
  };
};
