import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 锦标赛战斗记录
 * 基于old项目worldCup.js、middleEastCup.js等的战斗记录结构
 */
@Schema({ _id: false })
export class TournamentBattleRecord {
  @Prop({ required: true })
  roundNum: number;                      // 轮次编号

  @Prop({ required: true })
  enemyTeamId: number;                   // 对手队伍ID

  @Prop({ default: 0 })
  selfScore: number;                     // 己方比分

  @Prop({ default: 0 })
  enemyScore: number;                    // 敌方比分

  @Prop({ default: 0 })
  result: number;                        // 战斗结果 (0失败 1胜利)

  @Prop({ default: Date.now })
  battleTime: Date;                      // 战斗时间

  @Prop({ type: Object, default: {} })
  rewardInfo: any;                       // 奖励信息
}

/**
 * 锦标赛奖励记录
 * 基于old项目的奖励处理逻辑
 */
@Schema({ _id: false })
export class TournamentReward {
  @Prop({ default: 0 })
  bonus: number;                         // 奖励状态 (0不可领取 1可领取 4已领取)

  @Prop({ default: 0 })
  added: number;                         // 额外奖励状态

  @Prop({ type: [Object], default: [] })
  itemList: any[];                       // 物品奖励列表

  @Prop({ default: 0 })
  cash: number;                          // 现金奖励

  @Prop({ default: 0 })
  gold: number;                          // 球币奖励
}

/**
 * 世界杯锦标赛数据
 * 基于old项目worldCup.js的数据结构
 */
@Schema({ _id: false })
export class WorldCupData {
  @Prop({ default: Date.now })
  reTime: Date;                          // 刷新时间

  @Prop({ default: 0 })
  buyCount: number;                      // 购买次数

  @Prop({ default: 0 })
  joinCount: number;                     // 可进入次数

  @Prop({ default: 0 })
  isJoin: number;                        // 是否已经在比赛了 (0没有 1有)

  @Prop({ type: Object, default: {} })
  groupMatch: any;                       // 记录小组赛

  @Prop({ default: 1 })
  groupNum: number;                      // 小组赛轮次

  @Prop({ default: 0 })
  worldCupId: number;                    // 记录副本ID

  @Prop({ default: 0 })
  isGetReward: number;                   // 是否领取奖励

  @Prop({ default: 0 })
  isOut: number;                         // 是否出局

  @Prop({ default: 0 })
  isRecord: number;                      // 是否记录过

  @Prop({ type: Object, default: {} })
  eliminateMatch: any;                   // 记录淘汰赛
}

/**
 * 区域杯赛数据（中东杯、海湾杯、MLS等）
 * 基于old项目middleEastCup.js、gulfCup.js、MLS.js的数据结构
 */
@Schema({ _id: false })
export class RegionalCupData {
  @Prop({ default: 0 })
  contestNum: number;                    // 挑战次数

  @Prop({ default: 0 })
  sponsorId: number;                     // 赞助商ID

  @Prop({ default: 0 })
  isBegin: number;                       // 是否已经在挑战 (0没有 1有)

  @Prop({ type: [Number], default: [] })
  rivalTeamList: number[];               // 对手列表

  @Prop({ type: [Number], default: [] })
  teamList: number[];                    // 选择的队伍

  @Prop({ type: [TournamentReward], default: [] })
  awardList: TournamentReward[];         // 奖励列表

  @Prop({ default: Date.now })
  flashTime: Date;                       // 刷新时间

  @Prop({ default: 0 })
  contestNumMax: number;                 // 挑战次数上限

  @Prop({ default: 0 })
  luckyValue: number;                    // 幸运值

  @Prop({ default: 0 })
  luckyMax: number;                      // 幸运值上限

  @Prop({ default: 0 })
  conditionId: number;                   // 奖励条件ID

  @Prop({ default: 0 })
  goal: number;                          // 总进球数

  @Prop({ default: 0 })
  fumble: number;                        // 总失球数

  @Prop({ default: 0 })
  diff: number;                          // 总净胜球数

  @Prop({ default: 0 })
  win: number;                           // 胜利次数

  @Prop({ type: [TournamentBattleRecord], default: [] })
  combatList: TournamentBattleRecord[];  // 战斗列表

  @Prop({ type: [Object], default: [] })
  rewardList: any[];                     // 保存发送邮件奖励列表
}

/**
 * 锦标赛主文档
 * 基于old项目中各种锦标赛的数据结构
 */
@Schema({
  collection: 'tournaments',
  timestamps: true,
  versionKey: false,
})
export class Tournament extends Document {
  @Prop({ required: true, unique: true })
  uid: string;                           // 玩家UID

  @Prop({ type: WorldCupData })
  worldCup: WorldCupData;                // 世界杯数据

  @Prop({ type: RegionalCupData })
  middleEastCup: RegionalCupData;        // 中东杯数据

  @Prop({ type: RegionalCupData })
  gulfCup: RegionalCupData;              // 海湾杯数据

  @Prop({ type: RegionalCupData })
  mls: RegionalCupData;                  // MLS数据

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

export const TournamentSchema = SchemaFactory.createForClass(Tournament);

// 定义方法接口 - 基于TournamentService的真实业务逻辑
export interface TournamentMethods {
  // 锦标赛管理 - 基于TournamentService
  getTournamentData(tournamentType: string): WorldCupData | RegionalCupData | null;
  joinTournament(tournamentType: string): boolean;
  leaveTournament(tournamentType: string): boolean;
  isJoinedTournament(tournamentType: string): boolean;

  // 战斗记录管理 - 基于TournamentService
  addBattleRecord(tournamentType: string, record: TournamentBattleRecord): boolean;
  getBattleRecords(tournamentType: string): TournamentBattleRecord[];
  getLastBattleRecord(tournamentType: string): TournamentBattleRecord | null;
  updateBattleStats(tournamentType: string, isWin: boolean, goals: number, conceded: number): void;

  // 挑战次数管理 - 基于TournamentService
  canChallenge(tournamentType: string): boolean;
  consumeChallenge(tournamentType: string): boolean;
  addChallengeTimes(tournamentType: string, times: number): void;
  resetDailyChallenges(tournamentType: string): void;

  // 幸运值管理 - 基于TournamentService
  addLuckyValue(tournamentType: string, value: number): boolean;
  canUseLuckyValue(tournamentType: string, requiredValue: number): boolean;
  consumeLuckyValue(tournamentType: string, value: number): boolean;

  // 奖励管理 - 基于TournamentService
  addReward(tournamentType: string, reward: any): void;
  getUnclaimedRewards(tournamentType: string): any[];
  claimReward(tournamentType: string, rewardId: string): boolean;

  // 统计分析 - 基于TournamentService
  getTournamentStats(tournamentType: string): any;
  getOverallStats(): any;
  getWinRate(tournamentType: string): number;

  // 数据转换 - 基于TournamentService
  toClientTournamentData(tournamentType?: string): any;
  getTournamentSummary(): any;
}

// 定义Document类型
export type TournamentDocument = Tournament & Document & TournamentMethods;

// 创建索引
TournamentSchema.index({ uid: 1 });
TournamentSchema.index({ 'worldCup.isJoin': 1 });
TournamentSchema.index({ 'worldCup.worldCupId': 1 });
TournamentSchema.index({ lastUpdateTime: 1 });

// ==================== 实例方法实现 ====================

/**
 * 获取锦标赛数据
 * 基于TournamentService: 锦标赛数据查询逻辑
 */
TournamentSchema.methods.getTournamentData = function(tournamentType: string): WorldCupData | RegionalCupData | null {
  switch (tournamentType) {
    case 'worldCup':
      return this.worldCup;
    case 'middleEastCup':
      return this.middleEastCup;
    case 'gulfCup':
      return this.gulfCup;
    case 'mls':
      return this.mls;
    default:
      return null;
  }
};

/**
 * 加入锦标赛
 * 基于TournamentService: 锦标赛参与逻辑
 */
TournamentSchema.methods.joinTournament = function(tournamentType: string): boolean {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return false;

  if (tournament.isJoin === 0) {
    tournament.isJoin = 1;
    tournament.joinTime = new Date();
    this.lastUpdateTime = new Date();
    return true;
  }

  return false;
};

/**
 * 离开锦标赛
 * 基于TournamentService: 锦标赛退出逻辑
 */
TournamentSchema.methods.leaveTournament = function(tournamentType: string): boolean {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return false;

  if (tournament.isJoin === 1) {
    tournament.isJoin = 0;
    this.lastUpdateTime = new Date();
    return true;
  }

  return false;
};

/**
 * 检查是否已加入锦标赛
 * 基于TournamentService: 参与状态检查逻辑
 */
TournamentSchema.methods.isJoinedTournament = function(tournamentType: string): boolean {
  const tournament = this.getTournamentData(tournamentType);
  return tournament ? tournament.isJoin === 1 : false;
};

/**
 * 添加战斗记录
 * 基于TournamentService: 战斗记录管理逻辑
 */
TournamentSchema.methods.addBattleRecord = function(tournamentType: string, record: TournamentBattleRecord): boolean {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return false;

  tournament.combatList.unshift(record); // 添加到开头

  // 保持记录数量限制
  if (tournament.combatList.length > 50) {
    tournament.combatList = tournament.combatList.slice(0, 50);
  }

  this.lastUpdateTime = new Date();
  return true;
};

/**
 * 获取战斗记录
 * 基于TournamentService: 战斗记录查询逻辑
 */
TournamentSchema.methods.getBattleRecords = function(tournamentType: string): TournamentBattleRecord[] {
  const tournament = this.getTournamentData(tournamentType);
  return tournament ? tournament.combatList : [];
};

/**
 * 获取最后一次战斗记录
 * 基于TournamentService: 最近战斗查询逻辑
 */
TournamentSchema.methods.getLastBattleRecord = function(tournamentType: string): TournamentBattleRecord | null {
  const records = this.getBattleRecords(tournamentType);
  return records.length > 0 ? records[0] : null;
};

/**
 * 更新战斗统计
 * 基于TournamentService: 战斗统计更新逻辑
 */
TournamentSchema.methods.updateBattleStats = function(tournamentType: string, isWin: boolean, goals: number, conceded: number): void {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return;

  // 更新统计数据
  tournament.goal += goals;
  tournament.fumble += conceded;
  tournament.diff += (goals - conceded);

  if (isWin) {
    tournament.win += 1;
  }

  this.lastUpdateTime = new Date();
};

/**
 * 检查是否可以挑战
 * 基于TournamentService: 挑战次数检查逻辑
 */
TournamentSchema.methods.canChallenge = function(tournamentType: string): boolean {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return false;

  return tournament.contestNum < tournament.contestNumMax;
};

/**
 * 消耗挑战次数
 * 基于TournamentService: 挑战次数消耗逻辑
 */
TournamentSchema.methods.consumeChallenge = function(tournamentType: string): boolean {
  if (!this.canChallenge(tournamentType)) return false;

  const tournament = this.getTournamentData(tournamentType);
  if (tournament) {
    tournament.contestNum += 1;
    this.lastUpdateTime = new Date();
    return true;
  }

  return false;
};

/**
 * 增加挑战次数
 * 基于TournamentService: 挑战次数购买逻辑
 */
TournamentSchema.methods.addChallengeTimes = function(tournamentType: string, times: number): void {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return;

  tournament.contestNumMax += times;
  this.lastUpdateTime = new Date();
};

/**
 * 重置每日挑战次数
 * 基于TournamentService: 每日重置逻辑
 */
TournamentSchema.methods.resetDailyChallenges = function(tournamentType: string): void {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return;

  tournament.contestNum = 0;
  tournament.contestNumMax = 5; // 默认每日5次
  this.lastUpdateTime = new Date();
};

/**
 * 增加幸运值
 * 基于TournamentService: 幸运值管理逻辑
 */
TournamentSchema.methods.addLuckyValue = function(tournamentType: string, value: number): boolean {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return false;

  tournament.luckyValue = Math.min(tournament.luckyMax, tournament.luckyValue + value);
  this.lastUpdateTime = new Date();
  return true;
};

/**
 * 检查幸运值是否足够
 * 基于TournamentService: 幸运值检查逻辑
 */
TournamentSchema.methods.canUseLuckyValue = function(tournamentType: string, requiredValue: number): boolean {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return false;

  return tournament.luckyValue >= requiredValue;
};

/**
 * 消耗幸运值
 * 基于TournamentService: 幸运值消耗逻辑
 */
TournamentSchema.methods.consumeLuckyValue = function(tournamentType: string, value: number): boolean {
  if (!this.canUseLuckyValue(tournamentType, value)) return false;

  const tournament = this.getTournamentData(tournamentType);
  if (tournament) {
    tournament.luckyValue -= value;
    this.lastUpdateTime = new Date();
    return true;
  }

  return false;
};

/**
 * 添加奖励
 * 基于TournamentService: 奖励管理逻辑
 */
TournamentSchema.methods.addReward = function(tournamentType: string, reward: any): void {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return;

  tournament.rewardList.push({
    ...reward,
    id: Date.now().toString(),
    status: 0, // 0未领取 1已领取
    createTime: new Date()
  });

  this.lastUpdateTime = new Date();
};

/**
 * 获取未领取奖励
 * 基于TournamentService: 奖励查询逻辑
 */
TournamentSchema.methods.getUnclaimedRewards = function(tournamentType: string): any[] {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return [];

  return tournament.rewardList.filter((reward: any) => reward.status === 0);
};

/**
 * 领取奖励
 * 基于TournamentService: 奖励领取逻辑
 */
TournamentSchema.methods.claimReward = function(tournamentType: string, rewardId: string): boolean {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return false;

  const reward = tournament.rewardList.find((r: any) => r.id === rewardId && r.status === 0);
  if (reward) {
    reward.status = 1;
    reward.claimTime = new Date();
    this.lastUpdateTime = new Date();
    return true;
  }

  return false;
};

/**
 * 获取锦标赛统计
 * 基于TournamentService: 统计分析逻辑
 */
TournamentSchema.methods.getTournamentStats = function(tournamentType: string): any {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return null;

  const totalBattles = tournament.combatList.length;
  const winRate = totalBattles > 0 ? Math.round((tournament.win / totalBattles) * 100) : 0;

  return {
    tournamentType,
    isJoined: tournament.isJoin === 1,
    totalBattles,
    wins: tournament.win,
    winRate,
    totalGoals: tournament.goal,
    totalConceded: tournament.fumble,
    goalDifference: tournament.diff,
    challengesUsed: tournament.contestNum,
    challengesMax: tournament.contestNumMax,
    luckyValue: tournament.luckyValue,
    luckyMax: tournament.luckyMax,
    unclaimedRewards: this.getUnclaimedRewards(tournamentType).length,
    joinTime: tournament.joinTime
  };
};

/**
 * 获取总体统计
 * 基于TournamentService: 总体统计逻辑
 */
TournamentSchema.methods.getOverallStats = function(): any {
  const tournaments = ['worldCup', 'middleEastCup', 'gulfCup', 'mls'];
  let totalBattles = 0;
  let totalWins = 0;
  let totalGoals = 0;
  let totalConceded = 0;
  let joinedTournaments = 0;
  let totalUnclaimedRewards = 0;

  const tournamentStats: any = {};

  for (const tournamentType of tournaments) {
    const stats = this.getTournamentStats(tournamentType);
    if (stats) {
      tournamentStats[tournamentType] = stats;
      totalBattles += stats.totalBattles;
      totalWins += stats.wins;
      totalGoals += stats.totalGoals;
      totalConceded += stats.totalConceded;
      totalUnclaimedRewards += stats.unclaimedRewards;

      if (stats.isJoined) {
        joinedTournaments++;
      }
    }
  }

  return {
    joinedTournaments,
    totalBattles,
    totalWins,
    overallWinRate: totalBattles > 0 ? Math.round((totalWins / totalBattles) * 100) : 0,
    totalGoals,
    totalConceded,
    overallGoalDifference: totalGoals - totalConceded,
    totalUnclaimedRewards,
    tournaments: tournamentStats,
    lastUpdateTime: this.lastUpdateTime
  };
};

/**
 * 获取胜率
 * 基于TournamentService: 胜率计算逻辑
 */
TournamentSchema.methods.getWinRate = function(tournamentType: string): number {
  const tournament = this.getTournamentData(tournamentType);
  if (!tournament) return 0;

  const totalBattles = tournament.combatList.length;
  return totalBattles > 0 ? Math.round((tournament.win / totalBattles) * 100) : 0;
};

/**
 * 转换为客户端锦标赛数据
 * 基于TournamentService: 客户端数据转换需求
 */
TournamentSchema.methods.toClientTournamentData = function(tournamentType?: string): any {
  /*if (tournamentType) {
    // 返回单个锦标赛数据
    const tournament = this.getTournamentData(tournamentType);
    if (!tournament) return null;

    return {
      tournamentType,
      ...this.getTournamentStats(tournamentType),
      combatList: tournament.combatList.slice(0, 10).map((record: TournamentBattleRecord) => ({
        roomId: record.roomId,
        result: record.result,
        teamAScore: record.teamAScore,
        teamBScore: record.teamBScore,
        beginTime: record.beginTime.toISOString(),
        endTime: record.endTime?.toISOString(),
        opponentName: record.opponentName,
        opponentLevel: record.opponentLevel
      })),
      rewardList: this.getUnclaimedRewards(tournamentType).map((reward: any) => ({
        id: reward.id,
        type: reward.type,
        items: reward.items,
        createTime: reward.createTime.toISOString()
      }))
    };
  } else {
    // 返回所有锦标赛数据
    const tournaments = ['worldCup', 'middleEastCup', 'gulfCup', 'mls'];
    const result: any = {
      uid: this.uid,
      overallStats: this.getOverallStats(),
      tournaments: {}
    };

    for (const type of tournaments) {
      const tournamentData = this.toClientTournamentData(type);
      if (tournamentData) {
        result.tournaments[type] = tournamentData;
      }
    }

    return result;
  }*/
};

/**
 * 获取锦标赛摘要
 * 基于TournamentService: 摘要信息获取需求
 */
TournamentSchema.methods.getTournamentSummary = function(): any {
  /*const overallStats = this.getOverallStats();
  const tournaments = ['worldCup', 'middleEastCup', 'gulfCup', 'mls'];

  const activeTournaments = tournaments.filter(type => this.isJoinedTournament(type));
  const availableChallenges = tournaments.reduce((total, type) => {
    const tournament = this.getTournamentData(type);
    if (tournament && tournament.isJoin === 1) {
      return total + (tournament.contestNumMax - tournament.contestNum);
    }
    return total;
  }, 0);

  // 获取最近的战斗记录
  let recentBattles: TournamentBattleRecord[] = [];
  for (const type of tournaments) {
    const records = this.getBattleRecords(type);
    recentBattles = recentBattles.concat(records);
  }

  // 按时间排序，取最近5条
  recentBattles.sort((a, b) => b.beginTime.getTime() - a.beginTime.getTime());
  recentBattles = recentBattles.slice(0, 5);

  return {
    uid: this.uid,
    activeTournaments: activeTournaments.length,
    totalTournaments: tournaments.length,
    availableChallenges,
    totalBattles: overallStats.totalBattles,
    overallWinRate: overallStats.overallWinRate,
    totalUnclaimedRewards: overallStats.totalUnclaimedRewards,
    recentBattles: recentBattles.map((battle: TournamentBattleRecord) => ({
      roomId: battle.roomId,
      result: battle.result,
      teamAScore: battle.teamAScore,
      teamBScore: battle.teamBScore,
      beginTime: battle.beginTime.toISOString(),
      opponentName: battle.opponentName
    })),
    lastUpdateTime: this.lastUpdateTime.toISOString()
  };*/
};
