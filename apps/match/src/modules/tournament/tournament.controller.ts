import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { TournamentService } from './tournament.service';
import {
  WorldCupInfoResponseDto,
  JoinWorldCupResponseDto,
  WorldCupBattleResponseDto,
  RegionalCupInfoResponseDto
} from '../../common/dto/tournament.dto';
import { Cacheable, CacheEvict } from '@libs/redis';


import { XResponse } from '@libs/common/types/result.type';

import {
  BuyWorldCupTimesPayloadDto,
  GetStatisticsPayloadDto,
  GetWorldCupInfoPayloadDto,
  GetWorldCupRewardPayloadDto,
  JoinRegionalCupPayloadDto,
  JoinWorldCupPayloadDto,
  RegionalCupBattlePayloadDto,
  ResetDailyDataPayloadDto,
  WorldCupBattlePayloadDto
} from "@match/common/dto/tournament-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

/**
 * 锦标赛系统控制器
 * 严格基于old项目worldCup.js、middleEastCup.js等的接口设计
 * 
 * 核心接口：
 * - tournament.getWorldCupInfo: 获取世界杯信息
 * - tournament.joinWorldCup: 参加世界杯
 * - tournament.worldCupBattle: 世界杯战斗
 * - tournament.buyWorldCupTimes: 购买世界杯次数
 * - tournament.getWorldCupReward: 领取世界杯奖励
 * - tournament.joinRegionalCup: 参加区域杯赛
 * - tournament.regionalCupBattle: 区域杯赛战斗
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class TournamentController extends BaseController {
  constructor(private readonly tournamentService: TournamentService) {
    super('TournamentController');
  }

  /**
   * 获取世界杯信息
   * 基于old项目的getWorldCupInfo接口
   */
  @MessagePattern('tournament.getWorldCupInfo')
  @Cacheable({
    key: 'tournament:worldcup:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getWorldCupInfo(@Payload() payload: GetWorldCupInfoPayloadDto): Promise<XResponse<WorldCupInfoResponseDto>> {
    this.logger.log(`获取世界杯信息: ${payload.characterId}`);
    
    const result = await this.tournamentService.getWorldCupInfo(payload);
    return this.fromResult(result);
  }

  /**
   * 参加世界杯
   * 基于old项目的joinWorldCup接口
   */
  @MessagePattern('tournament.joinWorldCup')
  @CacheEvict({
    key: 'tournament:worldcup:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async joinWorldCup(@Payload() payload: JoinWorldCupPayloadDto): Promise<XResponse<JoinWorldCupResponseDto>> {
    this.logger.log(`参加世界杯: ${payload.characterId}, 副本${payload.worldCupId}`);
    
    const result = await this.tournamentService.joinWorldCup(payload);
    return this.fromResult(result);
  }

  /**
   * 世界杯战斗
   * 基于old项目的worldCupBattle接口
   */
  @MessagePattern('tournament.worldCupBattle')
  @CacheEvict({
    key: 'tournament:worldcup:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async worldCupBattle(@Payload() payload: WorldCupBattlePayloadDto): Promise<XResponse<WorldCupBattleResponseDto>> {
    this.logger.log(`世界杯战斗: ${payload.characterId}`);
    
    const result = await this.tournamentService.worldCupBattle(payload);
    return this.fromResult(result);
  }

  /**
   * 购买世界杯次数
   * 基于old项目的buyWorldCupTimes接口
   */
  @MessagePattern('tournament.buyWorldCupTimes')
  @CacheEvict({
    key: 'tournament:worldcup:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyWorldCupTimes(@Payload() payload: BuyWorldCupTimesPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`购买世界杯次数: ${payload.characterId}`);
    
    const result = await this.tournamentService.buyWorldCupTimes(payload);
    return this.fromResult(result);
  }

  /**
   * 领取世界杯奖励
   * 基于old项目的getWorldCupReward接口
   */
  @MessagePattern('tournament.getWorldCupReward')
  @CacheEvict({
    key: 'tournament:worldcup:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async getWorldCupReward(@Payload() payload: GetWorldCupRewardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`领取世界杯奖励: ${payload.characterId}`);
    
    const result = await this.tournamentService.getWorldCupReward(payload);
    return this.fromResult(result);
  }

  /**
   * 参加区域杯赛
   * 基于old项目的joinRegionalCup接口
   */
  @MessagePattern('tournament.joinRegionalCup')
  @CacheEvict({
    key: 'tournament:regional:#{payload.characterId}:#{payload.cupType}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async joinRegionalCup(@Payload() payload: JoinRegionalCupPayloadDto): Promise<XResponse<RegionalCupInfoResponseDto>> {
    this.logger.log(`参加区域杯赛: ${payload.characterId}, 类型: ${payload.cupType}`);

    const result = await this.tournamentService.joinRegionalCup(payload);
    return this.fromResult(result);
  }

  /**
   * 区域杯赛战斗
   * 基于old项目的MiddleEastCupBattle、gulfCupBattle等接口
   * 修复：实现缺失的区域杯赛战斗功能
   */
  @MessagePattern('tournament.regionalCupBattle')
  @CacheEvict({
    key: 'tournament:regional:#{payload.characterId}:#{payload.cupType}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async regionalCupBattle(@Payload() payload: RegionalCupBattlePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`区域杯赛战斗: ${payload.characterId}, 类型: ${payload.cupType}`);

    const result = await this.tournamentService.regionalCupBattle(payload);
    return this.fromResult(result);
  }

  /**
   * 获取锦标赛统计信息（管理接口）
   * TODO: 未实现
   */
  @MessagePattern('tournament.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取锦标赛统计信息');
    
    return this.toSuccessResponse({});
  }

  /**
   * 重置每日锦标赛数据（管理接口）
   * TODO: 未实现
   */
  @MessagePattern('tournament.resetDailyData')
  async resetDailyData(@Payload() payload: ResetDailyDataPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`重置每日锦标赛数据: ${payload.characterId || 'all'}`);
    
    return this.toSuccessResponse({});
  }
}
