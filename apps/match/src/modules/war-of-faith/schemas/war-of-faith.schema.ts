import { Prop, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 参赛选手信息子文档
@Schema({ _id: false })
export class WarParticipant {
  @Prop({ required: true })
  playerId: string; // 玩家ID

  @Prop({ required: true })
  playerName: string; // 玩家名称

  @Prop({ required: true })
  beliefId: number; // 信仰ID

  @Prop({ required: true })
  beliefName: string; // 信仰名称

  @Prop({ default: 0 })
  power: number; // 战力

  @Prop({ default: 0 })
  level: number; // 等级

  @Prop({ default: 'active' })
  status: string; // 状态：active, eliminated

  @Prop({ default: 0 })
  wins: number; // 胜场

  @Prop({ default: 0 })
  losses: number; // 负场

  @Prop({ default: () => Date.now() })
  joinTime: number; // 加入时间
}

// 战斗记录子文档
@Schema({ _id: false })
export class BattleRecord {
  @Prop({ required: true })
  battleId: string; // 战斗ID

  @Prop({ required: true })
  attackerId: string; // 攻击者ID

  @Prop({ required: true })
  defenderId: string; // 防守者ID

  @Prop({ required: true })
  attackerBeliefId: number; // 攻击者信仰ID

  @Prop({ required: true })
  defenderBeliefId: number; // 防守者信仰ID

  @Prop({ required: true })
  winnerId: string; // 获胜者ID

  @Prop({ required: true })
  result: 'attacker_win' | 'defender_win' | 'draw'; // 战斗结果

  @Prop({ default: 0 })
  duration: number; // 战斗时长（秒）

  @Prop({ type: Object, default: {} })
  battleData: any; // 战斗详细数据

  @Prop({ default: () => Date.now() })
  battleTime: number; // 战斗时间
}

// 占领记录子文档
@Schema({ _id: false })
export class OccupationRecord {
  @Prop({ required: true })
  beliefId: number; // 占领信仰ID

  @Prop({ required: true })
  beliefName: string; // 占领信仰名称

  @Prop({ default: () => Date.now() })
  occupyTime: number; // 占领时间

  @Prop({ default: 0 })
  duration: number; // 占领持续时间（毫秒）

  @Prop({ default: 'active' })
  status: string; // 状态：active, ended

  @Prop({ default: 0 })
  defenseCount: number; // 防守次数

  @Prop({ default: 0 })
  challengeCount: number; // 被挑战次数
}

// 挑战次数记录子文档
@Schema({ _id: false })
export class ChallengeRecord {
  @Prop({ required: true })
  playerId: string; // 玩家ID

  @Prop({ required: true })
  beliefId: number; // 信仰ID

  @Prop({ default: 0 })
  usedChallenges: number; // 已使用挑战次数

  @Prop({ default: 3 })
  maxChallenges: number; // 最大挑战次数

  @Prop({ default: () => Date.now() })
  lastChallengeTime: number; // 最后挑战时间

  @Prop({ default: () => Date.now() })
  resetTime: number; // 重置时间
}

@Schema({ collection: 'war_of_faiths', timestamps: true })
export class WarOfFaith extends Document {
  @Prop({ required: true })
  warId: string; // 战争ID

  @Prop({ required: true })
  serverId: string; // 服务器ID

  @Prop({ default: 1 })
  season: number; // 赛季

  @Prop({ default: 1 })
  round: number; // 轮次

  @Prop({ default: 'preparation', enum: ['preparation', 'battle', 'ended'] })
  status: string; // 状态：准备、战斗、结束

  @Prop({ type: [WarParticipant], default: [] })
  participants: WarParticipant[]; // 参赛选手

  @Prop({ type: [BattleRecord], default: [] })
  battles: BattleRecord[]; // 战斗记录

  @Prop({ type: [OccupationRecord], default: [] })
  occupations: OccupationRecord[]; // 占领记录

  @Prop({ type: [ChallengeRecord], default: [] })
  challenges: ChallengeRecord[]; // 挑战次数记录

  @Prop({ default: 0 })
  currentOccupierId: number; // 当前占领者信仰ID

  @Prop({ default: () => Date.now() })
  startTime: number; // 开始时间

  @Prop({ default: 0 })
  endTime: number; // 结束时间

  @Prop({ default: 24 * 60 * 60 * 1000 })
  duration: number; // 持续时间（毫秒）

  @Prop({ type: Object, default: {} })
  rewards: any; // 奖励配置

  @Prop({ type: Object, default: {} })
  settings: any; // 战争设置

  @Prop({ default: () => Date.now() })
  lastUpdateTime: number; // 最后更新时间
}

export type WarOfFaithDocument = WarOfFaith & Document;
export const WarOfFaithSchema = SchemaFactory.createForClass(WarOfFaith);

// 创建索引
WarOfFaithSchema.index({ warId: 1 }, { unique: true });
WarOfFaithSchema.index({ serverId: 1, season: 1, round: 1 });
WarOfFaithSchema.index({ status: 1 });
WarOfFaithSchema.index({ startTime: -1 });
WarOfFaithSchema.index({ 'participants.playerId': 1 });
WarOfFaithSchema.index({ 'participants.beliefId': 1 });
WarOfFaithSchema.index({ 'battles.battleTime': -1 });
WarOfFaithSchema.index({ currentOccupierId: 1 });

// 添加实例方法
WarOfFaithSchema.methods.addParticipant = function(participant: WarParticipant) {
  // 检查是否已存在
  const existing = this.participants.find((p: WarParticipant) => p.playerId === participant.playerId);
  if (existing) {
    return false;
  }

  this.participants.push(participant);
  return true;
};

WarOfFaithSchema.methods.removeParticipant = function(playerId: string) {
  const index = this.participants.findIndex((p: WarParticipant) => p.playerId === playerId);
  if (index !== -1) {
    this.participants.splice(index, 1);
    return true;
  }
  return false;
};

WarOfFaithSchema.methods.addBattleRecord = function(battle: BattleRecord) {
  this.battles.push(battle);
  
  // 更新参赛者战绩
  const attacker = this.participants.find((p: WarParticipant) => p.playerId === battle.attackerId);
  const defender = this.participants.find((p: WarParticipant) => p.playerId === battle.defenderId);

  if (battle.result === 'attacker_win') {
    if (attacker) attacker.wins += 1;
    if (defender) defender.losses += 1;
  } else if (battle.result === 'defender_win') {
    if (attacker) attacker.losses += 1;
    if (defender) defender.wins += 1;
  }

  // 保持最多1000条战斗记录
  if (this.battles.length > 1000) {
    this.battles = this.battles.slice(-1000);
  }
};

WarOfFaithSchema.methods.updateOccupation = function(beliefId: number, beliefName: string) {
  // 结束当前占领
  const currentOccupation = this.occupations.find((o: OccupationRecord) => o.status === 'active');
  if (currentOccupation) {
    currentOccupation.status = 'ended';
    currentOccupation.duration = Date.now() - currentOccupation.occupyTime;
  }

  // 开始新占领
  this.occupations.push({
    beliefId,
    beliefName,
    occupyTime: Date.now(),
    duration: 0,
    status: 'active',
    defenseCount: 0,
    challengeCount: 0
  } as OccupationRecord);

  this.currentOccupierId = beliefId;
};

WarOfFaithSchema.methods.getChallengeRecord = function(playerId: string): ChallengeRecord | null {
  return this.challenges.find((c: ChallengeRecord) => c.playerId === playerId) || null;
};

WarOfFaithSchema.methods.updateChallengeRecord = function(playerId: string, beliefId: number) {
  let record = this.challenges.find((c: ChallengeRecord) => c.playerId === playerId);
  
  if (!record) {
    record = {
      playerId,
      beliefId,
      usedChallenges: 0,
      maxChallenges: 3,
      lastChallengeTime: Date.now(),
      resetTime: Date.now()
    } as ChallengeRecord;
    this.challenges.push(record);
  }

  record.usedChallenges += 1;
  record.lastChallengeTime = Date.now();

  return record;
};

WarOfFaithSchema.methods.resetDailyChallenges = function() {
  const now = Date.now();
  this.challenges.forEach((record: ChallengeRecord) => {
    record.usedChallenges = 0;
    record.resetTime = now;
  });
};

WarOfFaithSchema.methods.getParticipantsByBelief = function(beliefId: number): WarParticipant[] {
  return this.participants.filter((p: WarParticipant) => p.beliefId === beliefId);
};

WarOfFaithSchema.methods.getBattleStats = function() {
  const stats = {
    totalBattles: this.battles.length,
    beliefStats: new Map()
  };

  this.battles.forEach((battle: BattleRecord) => {
    // 统计攻击者信仰
    if (!stats.beliefStats.has(battle.attackerBeliefId)) {
      stats.beliefStats.set(battle.attackerBeliefId, { wins: 0, losses: 0, battles: 0 });
    }
    const attackerStats = stats.beliefStats.get(battle.attackerBeliefId);
    attackerStats.battles += 1;
    if (battle.result === 'attacker_win') {
      attackerStats.wins += 1;
    } else if (battle.result === 'defender_win') {
      attackerStats.losses += 1;
    }

    // 统计防守者信仰
    if (!stats.beliefStats.has(battle.defenderBeliefId)) {
      stats.beliefStats.set(battle.defenderBeliefId, { wins: 0, losses: 0, battles: 0 });
    }
    const defenderStats = stats.beliefStats.get(battle.defenderBeliefId);
    defenderStats.battles += 1;
    if (battle.result === 'defender_win') {
      defenderStats.wins += 1;
    } else if (battle.result === 'attacker_win') {
      defenderStats.losses += 1;
    }
  });

  return {
    totalBattles: stats.totalBattles,
    beliefStats: Object.fromEntries(stats.beliefStats)
  };
};

// 设置toJSON选项
WarOfFaithSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});
