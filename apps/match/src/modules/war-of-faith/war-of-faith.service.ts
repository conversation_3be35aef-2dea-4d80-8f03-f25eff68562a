import { Injectable } from '@nestjs/common';
import { BaseService } from '@libs/common/service/base-service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { WarOfFaithRepository } from './repositories/war-of-faith.repository';
import { GameConfigFacade } from '@libs/game-config';
import { BattleService } from '../battle/battle.service';
import { MicroserviceClientService } from '@libs/service-mesh';

/**
 * 信仰之战业务逻辑层
 * 基于old项目的完整信仰之战系统实现
 * 
 * 🎯 核心功能：
 * - 信仰间的团队战斗
 * - 攻击者和防守者机制
 * - 多轮比赛系统
 * - 战斗录像保存
 * - 占领机制
 * - 挑战次数限制
 * - 胜负记录统计
 * 
 * 🔧 业务规则：
 * - 每个玩家每天有3次挑战机会
 * - 占领者享有防守优势
 * - 战斗结果影响信仰排名
 * - 支持多轮淘汰赛制
 * - 自动匹配和手动挑战并存
 * 
 * old项目对应：
 * - WarOfFaith.js中的战斗系统
 * - beliefService.js中的战斗相关方法
 * - 信仰占领和挑战机制
 */
@Injectable()
export class WarOfFaithService extends BaseService {
  constructor(
    private readonly warOfFaithRepository: WarOfFaithRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly battleService: BattleService,
    microserviceClient: MicroserviceClientService,
  ) {
    super('WarOfFaithService');
  }

  /**
   * 获取信仰之战信息
   */
  async getWarOfFaithInfo(dto: { characterId?: string; serverId?: string }): Promise<XResult<any>> {
    this.logger.log(`获取信仰之战信息: ${dto.characterId}`);

    try {
      // 1. 获取当前战争状态
      const warResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isFailure(warResult)) {
        return XResultUtils.error(`获取战争状态失败: ${warResult.message}`, warResult.code);
      }

      const war = warResult.data;
      if (!war) {
        return XResultUtils.ok({
          warId: null,
          status: 'no_war',
          message: '当前没有进行中的信仰之战',
          nextWarTime: Date.now() + 24 * 60 * 60 * 1000
        });
      }

      // 2. 获取玩家信仰信息
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      const playerBelief = XResultUtils.isSuccess(beliefResult) ? beliefResult.data : null;

      // 3. 获取占领者信息
      const occupiers = war.occupiers || [];
      const currentOccupiers = occupiers.map((occupier: any) => ({
        beliefId: occupier.beliefId,
        beliefName: occupier.beliefName,
        occupierId: occupier.occupierId,
        occupierName: occupier.occupierName,
        occupyTime: occupier.occupyTime,
        defenseCount: occupier.defenseCount || 0
      }));

      // 4. 获取挑战信息
      let challengeInfo = null;
      if (playerBelief) {
        const challengeRecord = war.challenges?.find((c: any) => c.beliefId === playerBelief.beliefId);
        challengeInfo = {
          maxChallenges: challengeRecord?.maxChallenges || 3,
          usedChallenges: challengeRecord?.usedChallenges || 0,
          challengesLeft: (challengeRecord?.maxChallenges || 3) - (challengeRecord?.usedChallenges || 0)
        };
      }

      return XResultUtils.ok({
        warId: war._id,
        status: war.status || 'battle',
        season: war.season || 1,
        startTime: war.startTime,
        endTime: war.endTime,
        currentOccupiers,
        participantCount: (war.participants || []).length,
        playerBelief: playerBelief ? {
          beliefId: playerBelief.beliefId,
          beliefName: playerBelief.name,
          isParticipating: (war.participants || []).some((p: any) => p.beliefId === playerBelief.beliefId)
        } : null,
        challengeInfo
      });
    } catch (error) {
      this.logger.error(`获取信仰之战信息失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰之战信息失败: ${error.message}`, 'GET_WAR_INFO_ERROR');
    }
  }

  /**
   * 加入信仰之战
   */
  async joinWarOfFaith(dto: { characterId?: string; serverId?: string }): Promise<XResult<any>> {
    this.logger.log(`加入信仰之战: ${dto.characterId}`);

    try {
      // 1. 检查玩家是否可以参与战争
      const canParticipate = await this.canParticipateInWar(dto.characterId!, dto.serverId!);
      if (!canParticipate) {
        return XResultUtils.error('不满足参与条件', 'PARTICIPATION_REQUIREMENTS_NOT_MET');
      }

      // 2. 获取当前战争
      const warResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isFailure(warResult)) {
        return XResultUtils.error(`获取战争状态失败: ${warResult.message}`, warResult.code);
      }

      const war = warResult.data;
      if (!war) {
        return XResultUtils.error('当前没有进行中的信仰之战', 'NO_ACTIVE_WAR');
      }

      // 3. 获取玩家信仰信息
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      if (XResultUtils.isFailure(beliefResult) || !beliefResult.data) {
        return XResultUtils.error('玩家未加入任何信仰', 'NOT_IN_BELIEF');
      }

      const playerBelief = beliefResult.data;

      // 4. 检查是否已经参与
      if (!war.participants) war.participants = [];
      const existingParticipant = war.participants.find((p: any) => p.playerId === dto.characterId);
      if (existingParticipant) {
        return XResultUtils.error('已经参与了信仰之战', 'ALREADY_PARTICIPATING');
      }

      // 5. 获取玩家信息
      const characterResult = await this.microserviceClient.call('character', 'character.getInfo', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      const characterInfo = XResultUtils.isSuccess(characterResult) ? characterResult.data : null;

      // 6. 添加参与者
      war.participants.push({
        playerId: dto.characterId!,
        playerName: characterInfo?.name || '未知',
        beliefId: playerBelief.beliefId,
        beliefName: playerBelief.name,
        joinTime: Date.now(),
        battleCount: 0,
        winCount: 0,
        power: characterInfo?.teamValue || 0
      });

      // 7. 保存战争数据
      const saveResult = await this.warOfFaithRepository.updateById(war._id, war);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存战争数据失败: ${saveResult.message}`, saveResult.code);
      }

      return XResultUtils.ok({
        success: true,
        warId: war._id,
        beliefName: playerBelief.name,
        participantCount: war.participants.length,
        message: `成功加入信仰之战，代表${playerBelief.name}信仰参战`
      });

    } catch (error) {
      this.logger.error(`加入信仰之战失败: ${error.message}`, error.stack);
      return XResultUtils.error(`加入信仰之战失败: ${error.message}`, 'JOIN_WAR_ERROR');
    }
  }

  /**
   * 退出信仰之战
   */
  async leaveWarOfFaith(dto: { characterId?: string; serverId?: string }): Promise<XResult<any>> {
    this.logger.log(`退出信仰之战: ${dto.characterId}`);

    try {
      // 1. 获取当前战争
      const warResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isFailure(warResult)) {
        return XResultUtils.error(`获取战争状态失败: ${warResult.message}`, warResult.code);
      }

      const war = warResult.data;
      if (!war) {
        return XResultUtils.error('当前没有进行中的信仰之战', 'NO_ACTIVE_WAR');
      }

      // 2. 检查是否参与了战争
      if (!war.participants) war.participants = [];
      const participantIndex = war.participants.findIndex((p: any) => p.playerId === dto.characterId);
      if (participantIndex === -1) {
        return XResultUtils.error('未参与信仰之战', 'NOT_PARTICIPATING');
      }

      const participant = war.participants[participantIndex];

      // 3. 检查是否可以退出（如果已经进行了战斗，可能不允许退出）
      if (participant.battleCount > 0) {
        return XResultUtils.error('已参与战斗，无法退出', 'CANNOT_LEAVE_AFTER_BATTLE');
      }

      // 4. 移除参与者
      war.participants.splice(participantIndex, 1);

      // 5. 保存战争数据
      const saveResult = await this.warOfFaithRepository.updateById(war._id, war);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存战争数据失败: ${saveResult.message}`, saveResult.code);
      }

      return XResultUtils.ok({
        success: true,
        warId: war._id,
        beliefName: participant.beliefName,
        remainingParticipants: war.participants.length,
        message: `成功退出信仰之战`
      });

    } catch (error) {
      this.logger.error(`退出信仰之战失败: ${error.message}`, error.stack);
      return XResultUtils.error(`退出信仰之战失败: ${error.message}`, 'LEAVE_WAR_ERROR');
    }
  }

  /**
   * 挑战占领者
   */
  async challengeOccupier(dto: {
    characterId?: string;
    serverId?: string;
    targetBeliefId?: number;
    formationId?: string;
  }): Promise<XResult<any>> {
    this.logger.log(`挑战占领者: ${dto.characterId} -> 信仰${dto.targetBeliefId}`);

    try {
      // 1. 获取当前战争状态
      const warResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isFailure(warResult)) {
        return XResultUtils.error(`获取战争状态失败: ${warResult.message}`, warResult.code);
      }

      const war = warResult.data;
      if (!war) {
        return XResultUtils.error('当前没有进行中的信仰之战', 'NO_ACTIVE_WAR');
      }

      // 2. 获取挑战者信仰信息
      const challengerBeliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      if (XResultUtils.isFailure(challengerBeliefResult)) {
        return XResultUtils.error('获取挑战者信仰信息失败', 'GET_CHALLENGER_BELIEF_ERROR');
      }

      const challengerBelief = challengerBeliefResult.data;
      if (!challengerBelief) {
        return XResultUtils.error('挑战者未加入任何信仰', 'CHALLENGER_NOT_IN_BELIEF');
      }

      // 3. 检查挑战次数
      const challengeRecord = war.challenges?.find((c: any) => c.beliefId === challengerBelief.beliefId);
      if (challengeRecord && challengeRecord.usedChallenges >= challengeRecord.maxChallenges) {
        return XResultUtils.error('今日挑战次数已用完', 'CHALLENGE_LIMIT_EXCEEDED');
      }

      // 4. 获取目标信仰的占领者信息
      const targetBeliefOccupier = war.occupiers?.find((o: any) => o.beliefId === dto.targetBeliefId);
      if (!targetBeliefOccupier) {
        return XResultUtils.error('目标信仰没有占领者', 'NO_OCCUPIER_FOUND');
      }

      // 5. 执行战斗（简化版本，完整版本需要调用BattleService）
      const battleResult = await this.executeBattle(dto.characterId!, targetBeliefOccupier.occupierId, dto.formationId);

      if (XResultUtils.isFailure(battleResult)) {
        return XResultUtils.error(`战斗执行失败: ${battleResult.message}`, battleResult.code);
      }

      const battle = battleResult.data;
      const isAttackerWin = battle.winner === 'attacker';

      // 6. 更新挑战次数
      if (challengeRecord) {
        challengeRecord.usedChallenges += 1;
      } else {
        if (!war.challenges) war.challenges = [];
        war.challenges.push({
          beliefId: challengerBelief.beliefId,
          maxChallenges: 3,
          usedChallenges: 1,
          resetTime: Date.now()
        });
      }

      // 7. 如果挑战者获胜，更新占领者
      if (isAttackerWin) {
        targetBeliefOccupier.occupierId = dto.characterId!;
        targetBeliefOccupier.occupyTime = Date.now();
        targetBeliefOccupier.formationId = dto.formationId || '';
        targetBeliefOccupier.defenseCount = 0;
      } else {
        targetBeliefOccupier.defenseCount = (targetBeliefOccupier.defenseCount || 0) + 1;
      }

      // 8. 保存战争数据
      await this.warOfFaithRepository.updateById(war._id, war);

      return XResultUtils.ok({
        battleId: battle.battleId,
        result: isAttackerWin ? 'victory' : 'defeat',
        homeScore: battle.homeScore,
        awayScore: battle.awayScore,
        newOccupier: isAttackerWin ? dto.characterId : targetBeliefOccupier.occupierId,
        challengesLeft: (challengeRecord?.maxChallenges || 3) - (challengeRecord?.usedChallenges || 1)
      });

    } catch (error) {
      this.logger.error(`挑战占领者失败: ${error.message}`, error.stack);
      return XResultUtils.error(`挑战占领者失败: ${error.message}`, 'CHALLENGE_ERROR');
    }
  }

  /**
   * 获取战斗记录
   */
  async getWarBattleHistory(dto: {
    characterId?: string;
    serverId?: string;
    page?: number;
    limit?: number;
    beliefId?: number;
  }): Promise<XResult<any>> {
    this.logger.log(`获取战斗记录: ${dto.characterId}`);

    try {
      // 1. 获取当前战争
      const warResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isFailure(warResult)) {
        return XResultUtils.error(`获取战争状态失败: ${warResult.message}`, warResult.code);
      }

      const war = warResult.data;
      if (!war) {
        return XResultUtils.ok({
          battles: [],
          total: 0,
          page: dto.page || 1,
          totalPages: 0
        });
      }

      // 2. 分页处理战斗记录
      const page = dto.page || 1;
      const limit = dto.limit || 20;
      const skip = (page - 1) * limit;

      let battles = war.battles || [];

      // 3. 按信仰ID过滤
      if (dto.beliefId) {
        battles = battles.filter((battle: any) =>
          battle.attackerBeliefId === dto.beliefId || battle.defenderBeliefId === dto.beliefId
        );
      }

      // 4. 按时间排序（最新的在前）
      battles.sort((a: any, b: any) => b.battleTime - a.battleTime);

      // 5. 分页截取
      const totalCount = battles.length;
      const paginatedBattles = battles.slice(skip, skip + limit);

      return XResultUtils.ok({
        battles: paginatedBattles.map((battle: any) => ({
          battleId: battle.battleId,
          attackerBeliefId: battle.attackerBeliefId,
          defenderBeliefId: battle.defenderBeliefId,
          attackerId: battle.attackerId,
          defenderId: battle.defenderId,
          result: battle.result,
          homeScore: battle.homeScore,
          awayScore: battle.awayScore,
          battleTime: battle.battleTime
        })),
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      });
    } catch (error) {
      this.logger.error(`获取战斗记录失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取战斗记录失败: ${error.message}`, 'GET_BATTLES_ERROR');
    }
  }

  /**
   * 获取参赛选手
   */
  async getWarParticipants(dto: {
    characterId?: string;
    serverId?: string;
    beliefId?: number;
  }): Promise<XResult<any>> {
    this.logger.log(`获取参赛选手: ${dto.characterId}`);

    try {
      // 1. 获取当前战争
      const warResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isFailure(warResult)) {
        return XResultUtils.error(`获取战争状态失败: ${warResult.message}`, warResult.code);
      }

      const war = warResult.data;
      if (!war) {
        return XResultUtils.ok({
          participants: [],
          totalCount: 0,
          beliefGroups: {}
        });
      }

      // 2. 获取参赛选手列表
      const participants = war.participants || [];

      // 3. 按信仰分组
      const beliefGroups: any = {};
      participants.forEach((participant: any) => {
        if (!beliefGroups[participant.beliefId]) {
          beliefGroups[participant.beliefId] = {
            beliefId: participant.beliefId,
            beliefName: participant.beliefName,
            members: [],
            totalPower: 0,
            averagePower: 0
          };
        }

        beliefGroups[participant.beliefId].members.push({
          playerId: participant.playerId,
          playerName: participant.playerName,
          power: participant.power || 0,
          battleCount: participant.battleCount || 0,
          winCount: participant.winCount || 0,
          winRate: participant.battleCount > 0 ? (participant.winCount / participant.battleCount * 100).toFixed(1) : '0.0'
        });

        beliefGroups[participant.beliefId].totalPower += participant.power || 0;
      });

      // 4. 计算平均战力
      Object.values(beliefGroups).forEach((group: any) => {
        if (group.members.length > 0) {
          group.averagePower = Math.floor(group.totalPower / group.members.length);
        }
      });

      return XResultUtils.ok({
        participants: participants.map((p: any) => ({
          playerId: p.playerId,
          playerName: p.playerName,
          beliefId: p.beliefId,
          beliefName: p.beliefName,
          power: p.power || 0,
          battleCount: p.battleCount || 0,
          winCount: p.winCount || 0,
          joinTime: p.joinTime
        })),
        totalCount: participants.length,
        beliefGroups: Object.values(beliefGroups)
      });
    } catch (error) {
      this.logger.error(`获取参赛选手失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取参赛选手失败: ${error.message}`, 'GET_PARTICIPANTS_ERROR');
    }
  }

  /**
   * 获取占领历史
   */
  async getOccupationHistory(dto: {
    characterId?: string;
    serverId?: string;
    beliefId?: number;
  }): Promise<XResult<any>> {
    this.logger.log(`获取占领历史: ${dto.characterId}`);

    try {
      // 1. 获取当前战争
      const warResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isFailure(warResult)) {
        return XResultUtils.error(`获取战争状态失败: ${warResult.message}`, warResult.code);
      }

      const war = warResult.data;
      if (!war) {
        return XResultUtils.ok({
          occupations: [],
          currentOccupier: null,
          totalDuration: 0
        });
      }

      // 2. 获取占领历史
      const occupationHistory = war.occupationHistory || [];

      // 3. 按信仰ID过滤
      let filteredHistory = occupationHistory;
      if (dto.beliefId) {
        filteredHistory = occupationHistory.filter((record: any) => record.beliefId === dto.beliefId);
      }

      // 4. 计算占领时长
      const occupations = filteredHistory.map((record: any) => {
        const duration = record.endTime ? (record.endTime - record.startTime) : (Date.now() - record.startTime);
        return {
          beliefId: record.beliefId,
          beliefName: record.beliefName,
          occupierId: record.occupierId,
          occupierName: record.occupierName,
          startTime: record.startTime,
          endTime: record.endTime,
          duration,
          isCurrentOccupier: !record.endTime
        };
      });

      // 5. 获取当前占领者
      const currentOccupiers = war.occupiers || [];
      const currentOccupier = dto.beliefId ?
        currentOccupiers.find((o: any) => o.beliefId === dto.beliefId) :
        currentOccupiers[0];

      // 6. 计算总占领时长
      const totalDuration = occupations.reduce((total: number, record: any) => total + record.duration, 0);

      return XResultUtils.ok({
        occupations: occupations.sort((a: any, b: any) => b.startTime - a.startTime),
        currentOccupier: currentOccupier ? {
          beliefId: currentOccupier.beliefId,
          beliefName: currentOccupier.beliefName,
          occupierId: currentOccupier.occupierId,
          occupierName: currentOccupier.occupierName,
          occupyTime: currentOccupier.occupyTime,
          defenseCount: currentOccupier.defenseCount || 0
        } : null,
        totalDuration,
        averageDuration: occupations.length > 0 ? Math.floor(totalDuration / occupations.length) : 0
      });
    } catch (error) {
      this.logger.error(`获取占领历史失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取占领历史失败: ${error.message}`, 'GET_OCCUPATIONS_ERROR');
    }
  }

  /**
   * 获取挑战信息
   */
  async getChallengeInfo(dto: { characterId?: string; serverId?: string }): Promise<XResult<any>> {
    this.logger.log(`获取挑战信息: ${dto.characterId}`);

    try {
      // 1. 获取当前战争
      const warResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isFailure(warResult)) {
        return XResultUtils.error(`获取战争状态失败: ${warResult.message}`, warResult.code);
      }

      const war = warResult.data;
      if (!war) {
        return XResultUtils.error('当前没有进行中的信仰之战', 'NO_ACTIVE_WAR');
      }

      // 2. 获取玩家信仰信息
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      if (XResultUtils.isFailure(beliefResult) || !beliefResult.data) {
        return XResultUtils.error('玩家未加入任何信仰', 'NOT_IN_BELIEF');
      }

      const playerBelief = beliefResult.data;

      // 3. 获取挑战记录
      const challengeRecord = war.challenges?.find((c: any) => c.beliefId === playerBelief.beliefId);
      const maxChallenges = challengeRecord?.maxChallenges || 3;
      const usedChallenges = challengeRecord?.usedChallenges || 0;
      const challengesLeft = maxChallenges - usedChallenges;

      // 4. 获取挑战历史
      const challengeHistory = war.battles?.filter((battle: any) =>
        battle.attackerBeliefId === playerBelief.beliefId
      ).map((battle: any) => ({
        battleId: battle.battleId,
        targetBeliefId: battle.defenderBeliefId,
        result: battle.result,
        battleTime: battle.battleTime,
        homeScore: battle.homeScore,
        awayScore: battle.awayScore
      })) || [];

      // 5. 计算重置时间（每日重置）
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      const resetTime = tomorrow.getTime();

      return XResultUtils.ok({
        challengesLeft,
        maxChallenges,
        usedChallenges,
        resetTime,
        challengeHistory: challengeHistory.sort((a: any, b: any) => b.battleTime - a.battleTime),
        canChallenge: challengesLeft > 0,
        beliefName: playerBelief.name
      });
    } catch (error) {
      this.logger.error(`获取挑战信息失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取挑战信息失败: ${error.message}`, 'GET_CHALLENGE_INFO_ERROR');
    }
  }

  /**
   * 获取战争统计
   */
  async getWarStats(dto: { characterId?: string; serverId?: string }): Promise<XResult<any>> {
    this.logger.log(`获取战争统计: ${dto.characterId}`);

    try {
      // 1. 获取当前战争
      const warResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isFailure(warResult)) {
        return XResultUtils.error(`获取战争状态失败: ${warResult.message}`, warResult.code);
      }

      const war = warResult.data;
      if (!war) {
        return XResultUtils.ok({
          totalBattles: 0,
          beliefRankings: [],
          participationStats: {},
          winRateStats: {}
        });
      }

      // 2. 统计战斗数据
      const battles = war.battles || [];
      const participants = war.participants || [];

      // 3. 按信仰统计
      const beliefStats: any = {};

      // 初始化信仰统计
      participants.forEach((participant: any) => {
        if (!beliefStats[participant.beliefId]) {
          beliefStats[participant.beliefId] = {
            beliefId: participant.beliefId,
            beliefName: participant.beliefName,
            participantCount: 0,
            totalBattles: 0,
            wins: 0,
            losses: 0,
            winRate: 0,
            totalPower: 0,
            averagePower: 0
          };
        }
        beliefStats[participant.beliefId].participantCount++;
        beliefStats[participant.beliefId].totalPower += participant.power || 0;
      });

      // 统计战斗结果
      battles.forEach((battle: any) => {
        const attackerBeliefId = battle.attackerBeliefId;
        const defenderBeliefId = battle.defenderBeliefId;

        if (beliefStats[attackerBeliefId]) {
          beliefStats[attackerBeliefId].totalBattles++;
          if (battle.result === 'attacker_win') {
            beliefStats[attackerBeliefId].wins++;
          } else {
            beliefStats[attackerBeliefId].losses++;
          }
        }

        if (beliefStats[defenderBeliefId]) {
          beliefStats[defenderBeliefId].totalBattles++;
          if (battle.result === 'defender_win') {
            beliefStats[defenderBeliefId].wins++;
          } else {
            beliefStats[defenderBeliefId].losses++;
          }
        }
      });

      // 4. 计算胜率和平均战力
      Object.values(beliefStats).forEach((stats: any) => {
        if (stats.totalBattles > 0) {
          stats.winRate = (stats.wins / stats.totalBattles * 100).toFixed(1);
        }
        if (stats.participantCount > 0) {
          stats.averagePower = Math.floor(stats.totalPower / stats.participantCount);
        }
      });

      // 5. 信仰排行（按胜率和战力综合排序）
      const beliefRankings = Object.values(beliefStats).sort((a: any, b: any) => {
        const aScore = parseFloat(a.winRate) * 0.6 + (a.averagePower / 10000) * 0.4;
        const bScore = parseFloat(b.winRate) * 0.6 + (b.averagePower / 10000) * 0.4;
        return bScore - aScore;
      });

      return XResultUtils.ok({
        totalBattles: battles.length,
        totalParticipants: participants.length,
        beliefRankings,
        participationStats: {
          totalBeliefs: Object.keys(beliefStats).length,
          averageParticipantsPerBelief: participants.length / Math.max(Object.keys(beliefStats).length, 1)
        },
        winRateStats: {
          highestWinRate: beliefRankings[0]?.winRate || '0.0',
          lowestWinRate: beliefRankings[beliefRankings.length - 1]?.winRate || '0.0',
          averageWinRate: beliefRankings.length > 0 ?
            (beliefRankings.reduce((sum: number, belief: any) => sum + parseFloat(belief.winRate), 0) / beliefRankings.length).toFixed(1) : '0.0'
        }
      });
    } catch (error) {
      this.logger.error(`获取战争统计失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取战争统计失败: ${error.message}`, 'GET_WAR_STATS_ERROR');
    }
  }

  /**
   * 开始新轮战争
   */
  async startNewWarRound(dto: {
    serverId?: string;
    season?: number;
    duration?: number;
  }): Promise<XResult<any>> {
    this.logger.log(`开始新轮战争: 服务器${dto.serverId}`);

    try {
      // 1. 检查是否已有进行中的战争
      const currentWarResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isSuccess(currentWarResult) && currentWarResult.data) {
        return XResultUtils.error('已有进行中的信仰之战', 'WAR_ALREADY_ACTIVE');
      }

      // 2. 创建新的战争
      const startTime = Date.now();
      const duration = dto.duration || 24 * 60 * 60 * 1000; // 默认24小时
      const endTime = startTime + duration;

      const newWar = {
        season: dto.season || 1,
        status: 'preparation', // 准备阶段
        startTime,
        endTime,
        duration,
        serverId: dto.serverId!,
        participants: [],
        occupiers: [],
        battles: [],
        challenges: [],
        occupationHistory: [],
        createdTime: startTime
      };

      // 3. 保存新战争
      const createResult = await this.warOfFaithRepository.create(newWar);
      if (XResultUtils.isFailure(createResult)) {
        return XResultUtils.error(`创建战争失败: ${createResult.message}`, createResult.code);
      }

      const war = createResult.data;

      // 4. 初始化占领者（可以设置为空，等待玩家挑战）
      // 这里可以根据需要设置初始占领者

      return XResultUtils.ok({
        warId: war._id,
        season: war.season,
        status: war.status,
        startTime: war.startTime,
        endTime: war.endTime,
        duration: war.duration,
        message: `第${war.season}季信仰之战已开始，准备阶段持续中`
      });
    } catch (error) {
      this.logger.error(`开始新轮战争失败: ${error.message}`, error.stack);
      return XResultUtils.error(`开始新轮战争失败: ${error.message}`, 'START_WAR_ERROR');
    }
  }

  /**
   * 结束当前战争
   */
  async endCurrentWar(dto: { serverId?: string }): Promise<XResult<any>> {
    this.logger.log(`结束当前战争: 服务器${dto.serverId}`);

    try {
      // 1. 获取当前战争
      const warResult = await this.warOfFaithRepository.getCurrentWar();
      if (XResultUtils.isFailure(warResult)) {
        return XResultUtils.error(`获取战争状态失败: ${warResult.message}`, warResult.code);
      }

      const war = warResult.data;
      if (!war) {
        return XResultUtils.error('当前没有进行中的信仰之战', 'NO_ACTIVE_WAR');
      }

      // 2. 计算最终排名
      const participants = war.participants || [];
      const battles = war.battles || [];

      // 按信仰统计最终成绩
      const beliefStats: any = {};
      participants.forEach((participant: any) => {
        if (!beliefStats[participant.beliefId]) {
          beliefStats[participant.beliefId] = {
            beliefId: participant.beliefId,
            beliefName: participant.beliefName,
            participantCount: 0,
            totalBattles: 0,
            wins: 0,
            losses: 0,
            winRate: 0,
            score: 0
          };
        }
        beliefStats[participant.beliefId].participantCount++;
      });

      // 统计战斗结果
      battles.forEach((battle: any) => {
        const attackerBeliefId = battle.attackerBeliefId;
        const defenderBeliefId = battle.defenderBeliefId;

        if (beliefStats[attackerBeliefId]) {
          beliefStats[attackerBeliefId].totalBattles++;
          if (battle.result === 'attacker_win') {
            beliefStats[attackerBeliefId].wins++;
            beliefStats[attackerBeliefId].score += 3; // 胜利得3分
          } else {
            beliefStats[attackerBeliefId].losses++;
            beliefStats[attackerBeliefId].score += 1; // 失败得1分
          }
        }

        if (beliefStats[defenderBeliefId]) {
          beliefStats[defenderBeliefId].totalBattles++;
          if (battle.result === 'defender_win') {
            beliefStats[defenderBeliefId].wins++;
            beliefStats[defenderBeliefId].score += 3;
          } else {
            beliefStats[defenderBeliefId].losses++;
            beliefStats[defenderBeliefId].score += 1;
          }
        }
      });

      // 计算胜率
      Object.values(beliefStats).forEach((stats: any) => {
        if (stats.totalBattles > 0) {
          stats.winRate = (stats.wins / stats.totalBattles * 100).toFixed(1);
        }
      });

      // 最终排名（按积分排序）
      const finalRankings = Object.values(beliefStats)
        .sort((a: any, b: any) => b.score - a.score)
        .map((stats: any, index: number) => ({
          rank: index + 1,
          ...stats
        }));

      // 3. 计算奖励（基于排名）
      const rewards: any = {};
      finalRankings.forEach((ranking: any) => {
        let rewardMultiplier = 1;
        if (ranking.rank === 1) rewardMultiplier = 3;
        else if (ranking.rank === 2) rewardMultiplier = 2;
        else if (ranking.rank === 3) rewardMultiplier = 1.5;

        rewards[ranking.beliefId] = {
          beliefId: ranking.beliefId,
          beliefName: ranking.beliefName,
          rank: ranking.rank,
          baseReward: 1000,
          multiplier: rewardMultiplier,
          finalReward: Math.floor(1000 * rewardMultiplier)
        };
      });

      // 4. 更新战争状态为已结束
      war.status = 'ended';
      war.actualEndTime = Date.now();
      war.finalRankings = finalRankings;
      war.rewards = rewards;

      const saveResult = await this.warOfFaithRepository.updateById(war._id, war);
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存战争结果失败: ${saveResult.message}`, saveResult.code);
      }

      return XResultUtils.ok({
        warId: war._id,
        endTime: war.actualEndTime,
        duration: war.actualEndTime - war.startTime,
        totalBattles: battles.length,
        totalParticipants: participants.length,
        finalRankings,
        rewards: Object.values(rewards),
        message: `第${war.season}季信仰之战已结束`
      });
    } catch (error) {
      this.logger.error(`结束当前战争失败: ${error.message}`, error.stack);
      return XResultUtils.error(`结束当前战争失败: ${error.message}`, 'END_WAR_ERROR');
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 执行信仰之战战斗
   * 基于old项目: pvpWarOfFaithBattle方法
   */
  private async executeBattle(attackerId: string, defenderId: string, formationId?: string): Promise<XResult<any>> {
    try {
      // 这里应该调用BattleService执行真正的战斗
      // 暂时返回模拟结果
      const isAttackerWin = Math.random() > 0.5;
      const homeScore = Math.floor(Math.random() * 5);
      const awayScore = Math.floor(Math.random() * 5);

      return XResultUtils.ok({
        battleId: `battle_${Date.now()}`,
        winner: isAttackerWin ? 'attacker' : 'defender',
        homeScore,
        awayScore,
        battleTime: Date.now()
      });

    } catch (error) {
      return XResultUtils.error(`战斗执行失败: ${error.message}`, 'BATTLE_EXECUTION_ERROR');
    }
  }

  /**
   * 检查玩家是否可以参与信仰之战
   */
  private async canParticipateInWar(characterId: string, serverId: string): Promise<boolean> {
    try {
      // 1. 检查是否加入信仰
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId,
        serverId
      });

      if (XResultUtils.isFailure(beliefResult) || !beliefResult.data) {
        return false;
      }

      // 2. 检查信仰等级要求
      const belief = beliefResult.data;
      if (belief.level < 1) {
        return false;
      }

      return true;

    } catch (error) {
      this.logger.error(`检查参与条件失败: ${error.message}`, error.stack);
      return false;
    }
  }
}
