import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { WarOfFaithController } from './war-of-faith.controller';
import { WarOfFaithService } from './war-of-faith.service';
import { WarOfFaith, WarOfFaithSchema } from './schemas/war-of-faith.schema';
import { WarOfFaithRepository } from './repositories/war-of-faith.repository';

// 导入共享模块
import { GameConfigModule } from '@libs/game-config';
import { MicroserviceKitModule } from '@libs/common/microservice-kit/microservice-kit.module';
import { RedisModule } from '@libs/redis';

/**
 * 信仰之战系统模块
 * 基于old项目的完整信仰之战实现
 * 
 * 🎯 核心功能：
 * - 信仰间的团队战斗
 * - 攻击者和防守者机制
 * - 多轮比赛系统
 * - 战斗录像保存
 * - 占领机制
 * - 挑战次数限制
 * - 胜负记录统计
 * 
 * 🔗 依赖关系：
 * - Character服务：信仰信息和玩家数据
 * - Hero服务：英雄数据和阵容
 * - GameConfig模块：战斗配置数据
 * - MicroserviceKit模块：跨服务通信
 * - Redis模块：缓存和状态管理
 * 
 * old项目对应：
 * - WarOfFaith.js中的战斗系统
 * - beliefService.js中的战斗相关方法
 * - 信仰占领和挑战机制
 */
@Module({
  imports: [
    // MongoDB Schema注册
    MongooseModule.forFeature([
      { name: WarOfFaith.name, schema: WarOfFaithSchema },
    ]),
    
    // 共享模块
    GameConfigModule,
    MicroserviceKitModule,
    RedisModule,
  ],
  controllers: [
    WarOfFaithController,
  ],
  providers: [
    WarOfFaithService,
    WarOfFaithRepository,
  ],
  exports: [
    WarOfFaithService,
    WarOfFaithRepository,
  ],
})
export class WarOfFaithModule {}
