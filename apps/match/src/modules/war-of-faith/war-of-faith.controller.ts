import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { WarOfFaithService } from './war-of-faith.service';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import { Cacheable, CacheEvict } from '@libs/redis';
import {
  GetWarOfFaithInfoPayloadDto,
  JoinWarOfFaithPayloadDto,
  LeaveWarOfFaithPayloadDto,
  ChallengeOccupierPayloadDto,
  GetWarBattleHistoryPayloadDto,
  GetWarParticipantsPayloadDto,
  GetOccupationHistoryPayloadDto,
  GetChallengeInfoPayloadDto,
  GetWarStatsPayloadDto,
  StartNewWarRoundPayloadDto,
  EndCurrentWarPayloadDto
} from '../../common/dto/war-of-faith-payload.dto';

/**
 * 信仰之战控制器
 * 基于old项目的完整信仰之战系统实现
 * 
 * 🎯 核心功能：
 * - 信仰间的团队战斗
 * - 攻击者和防守者机制
 * - 多轮比赛系统
 * - 战斗录像保存
 * - 占领机制
 * - 挑战次数限制
 * - 胜负记录统计
 * 
 * 🚀 架构特性：
 * - 继承BaseController的标准化处理
 * - WebSocket微服务接口(@MessagePattern)
 * - Redis缓存装饰器(@Cacheable/@CacheEvict)
 * - 统一的XResponse响应格式
 * - 完整的日志记录和性能监控
 * 
 * old项目对应：
 * - WarOfFaith.js中的战斗系统
 * - beliefService.js中的战斗相关方法
 * - 信仰占领和挑战机制
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class WarOfFaithController extends BaseController {
  constructor(
    private readonly warOfFaithService: WarOfFaithService,
  ) {
    super('WarOfFaithController');
  }

  /**
   * 获取信仰之战信息
   * 基于old项目: getWarOfFaithInfo接口
   * 
   * 🔧 功能说明：
   * - 获取当前战争状态
   * - 显示参赛信仰和选手
   * - 显示占领情况
   * - 显示挑战次数
   * 
   * 对应old项目: WarOfFaith.getWarInfo
   */
  @MessagePattern('warOfFaith.getInfo')
  @Cacheable({
    key: 'warOfFaith:info:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 5 * 60 // 5分钟缓存
  })
  async getWarOfFaithInfo(payload: GetWarOfFaithInfoPayloadDto) {
    const result = await this.warOfFaithService.getWarOfFaithInfo(payload);
    return this.fromResult(result, '获取信仰之战信息成功');
  }

  /**
   * 加入信仰之战
   * 基于old项目: joinWarOfFaith接口
   * 
   * 🔧 功能说明：
   * - 玩家报名参加信仰之战
   * - 检查参赛条件
   * - 更新参赛列表
   * - 记录参赛时间
   * 
   * 对应old项目: WarOfFaith.joinWar
   */
  @MessagePattern('warOfFaith.join')
  @CacheEvict({
    key: 'warOfFaith:*:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async joinWarOfFaith(payload: { characterId: string; serverId: string }) {
    const result = await this.warOfFaithService.joinWarOfFaith(payload);
    return this.fromResult(result, '加入信仰之战成功');
  }

  /**
   * 退出信仰之战
   * 基于old项目: leaveWarOfFaith接口
   * 
   * 🔧 功能说明：
   * - 玩家退出信仰之战
   * - 更新参赛列表
   * - 处理退出惩罚
   * - 记录退出时间
   * 
   * 对应old项目: WarOfFaith.leaveWar
   */
  @MessagePattern('warOfFaith.leave')
  @CacheEvict({
    key: 'warOfFaith:*:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async leaveWarOfFaith(payload: { characterId: string; serverId: string }) {
    const result = await this.warOfFaithService.leaveWarOfFaith(payload);
    return this.fromResult(result, '退出信仰之战成功');
  }

  /**
   * 挑战信仰占领者
   * 基于old项目: challengeOccupier接口
   * 
   * 🔧 功能说明：
   * - 挑战当前占领信仰
   * - 检查挑战次数限制
   * - 执行战斗逻辑
   * - 更新占领状态
   * 
   * 对应old项目: WarOfFaith.challenge
   */
  @MessagePattern('warOfFaith.challenge')
  @CacheEvict({
    key: 'warOfFaith:*:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async challengeOccupier(payload: {
    characterId: string;
    serverId: string;
    targetBeliefId: number;
    formationId?: string;
  }) {
    const result = await this.warOfFaithService.challengeOccupier(payload);
    return this.fromResult(result, '挑战占领者成功');
  }

  /**
   * 获取战斗记录
   * 基于old项目: getWarBattleHistory接口
   * 
   * 🔧 功能说明：
   * - 查询战斗历史记录
   * - 支持分页和筛选
   * - 显示战斗详情
   * - 支持录像回放
   * 
   * 对应old项目: WarOfFaith.getBattleHistory
   */
  @MessagePattern('warOfFaith.getBattleHistory')
  @Cacheable({
    key: 'warOfFaith:battles:#{payload.serverId}:#{payload.page || 1}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 15 * 60 // 15分钟缓存
  })
  async getWarBattleHistory(payload: {
    characterId: string;
    serverId: string;
    page?: number;
    limit?: number;
    beliefId?: number;
  }) {
    const result = await this.warOfFaithService.getWarBattleHistory(payload);
    return this.fromResult(result, '获取战斗记录成功');
  }

  /**
   * 获取参赛选手列表
   * 基于old项目: getWarParticipants接口
   * 
   * 🔧 功能说明：
   * - 获取所有参赛选手
   * - 按信仰分组显示
   * - 显示战力和战绩
   * - 支持排序和筛选
   * 
   * 对应old项目: WarOfFaith.getParticipants
   */
  @MessagePattern('warOfFaith.getParticipants')
  @Cacheable({
    key: 'warOfFaith:participants:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 10 * 60 // 10分钟缓存
  })
  async getWarParticipants(payload: {
    characterId: string;
    serverId: string;
    beliefId?: number;
  }) {
    const result = await this.warOfFaithService.getWarParticipants(payload);
    return this.fromResult(result, '获取参赛选手成功');
  }

  /**
   * 获取占领历史
   * 基于old项目: getOccupationHistory接口
   * 
   * 🔧 功能说明：
   * - 查询占领历史记录
   * - 显示占领时长统计
   * - 显示防守成功率
   * - 支持时间范围筛选
   * 
   * 对应old项目: WarOfFaith.getOccupationHistory
   */
  @MessagePattern('warOfFaith.getOccupationHistory')
  @Cacheable({
    key: 'warOfFaith:occupations:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 30 * 60 // 30分钟缓存
  })
  async getOccupationHistory(payload: {
    characterId: string;
    serverId: string;
    beliefId?: number;
  }) {
    const result = await this.warOfFaithService.getOccupationHistory(payload);
    return this.fromResult(result, '获取占领历史成功');
  }

  /**
   * 获取挑战次数信息
   * 基于old项目: getChallengeInfo接口
   * 
   * 🔧 功能说明：
   * - 查询玩家剩余挑战次数
   * - 显示重置时间
   * - 显示挑战历史
   * - 支持购买额外次数
   * 
   * 对应old项目: WarOfFaith.getChallengeInfo
   */
  @MessagePattern('warOfFaith.getChallengeInfo')
  async getChallengeInfo(payload: { characterId: string; serverId: string }) {
    const result = await this.warOfFaithService.getChallengeInfo(payload);
    return this.fromResult(result, '获取挑战信息成功');
  }

  /**
   * 获取战争统计
   * 基于old项目: getWarStats接口
   * 
   * 🔧 功能说明：
   * - 统计各信仰战绩
   * - 显示胜率排行
   * - 统计参与度数据
   * - 生成战争报告
   * 
   * 对应old项目: WarOfFaith.getWarStats
   */
  @MessagePattern('warOfFaith.getStats')
  @Cacheable({
    key: 'warOfFaith:stats:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60 * 60 // 1小时缓存
  })
  async getWarStats(payload: { characterId: string; serverId: string }) {
    const result = await this.warOfFaithService.getWarStats(payload);
    return this.fromResult(result, '获取战争统计成功');
  }

  /**
   * 开始新一轮信仰之战
   * 基于old项目: startNewWarRound接口
   * 
   * 🔧 功能说明：
   * - 管理员开启新轮战争
   * - 重置参赛状态
   * - 清理历史数据
   * - 发放奖励
   * 
   * 对应old项目: WarOfFaith.startNewRound
   */
  @MessagePattern('warOfFaith.startNewRound')
  @CacheEvict({
    key: 'warOfFaith:*:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async startNewWarRound(payload: {
    serverId: string;
    season?: number;
    duration?: number;
  }) {
    const result = await this.warOfFaithService.startNewWarRound(payload);
    return this.fromResult(result, '开启新轮战争成功');
  }

  /**
   * 结束当前信仰之战
   * 基于old项目: endCurrentWar接口
   * 
   * 🔧 功能说明：
   * - 管理员结束当前战争
   * - 计算最终排名
   * - 发放奖励
   * - 生成战争报告
   * 
   * 对应old项目: WarOfFaith.endWar
   */
  @MessagePattern('warOfFaith.endWar')
  @CacheEvict({
    key: 'warOfFaith:*:#{payload.serverId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async endCurrentWar(payload: { serverId: string }) {
    const result = await this.warOfFaithService.endCurrentWar(payload);
    return this.fromResult(result, '结束战争成功');
  }
}
