import { Injectable, Logger } from '@nestjs/common';
import { BusinessRepository } from '../../common/repositories/business.repository';
import { GameConfigFacade } from '@libs/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import { HeroPosition } from '@libs/game-constants';
import { BattleService } from '../battle/battle.service';
import { BattleDataService } from '../../common/services/battle-data.service';
import {
  GetBusinessMatchInfoDto,
  BusinessSearchDto,
  BusinessMatchDto,
  BuyBusinessMatchDto,
  GetBusinessMatchInfoResponseDto,
  BusinessSearchResponseDto,
  BusinessMatchResponseDto
} from '../../common/dto/business.dto';
import { BusinessMatch, BusinessMatchRecord, BusinessRivalInfo } from '../../common/schemas/business.schema';

// Result模式相关导入
import { BaseService } from '@libs/common/service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 商业赛系统服务 - 已适配Result模式
 * 严格基于old项目businessMatch.js的业务逻辑实现
 *
 * 核心功能：
 * - 商业赛信息获取
 * - 对手搜索和匹配
 * - 商业赛战斗处理
 * - 战斗次数管理
 * - 奖励计算和发放
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的微服务调用和错误处理
 */
@Injectable()
export class BusinessService extends BaseService {

  constructor(
    private readonly businessRepository: BusinessRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly battleService: BattleService,
    private readonly battleDataService: BattleDataService,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('BusinessService', microserviceClient);
  }

  /**
   * 获取商业赛信息
   * 基于old项目: PlayerMatchService.prototype.getBusinessMatchInfo
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getBusinessMatchInfo(dto: GetBusinessMatchInfoDto): Promise<XResult<GetBusinessMatchInfoResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`获取商业赛信息: ${dto.characterId}`);

      // 初始化商业赛数据
      const businessMatchResult = await this.businessRepository.findByUid(dto.characterId);
      if (XResultUtils.isFailure(businessMatchResult)) {
        return XResultUtils.error(`查询商业赛数据失败: ${businessMatchResult.message}`, businessMatchResult.code);
      }

      let businessMatch = businessMatchResult.data;
      if (!businessMatch) {
        const initResult = await this.initBusinessMatchData(dto.characterId);
        if (XResultUtils.isFailure(initResult)) {
          return XResultUtils.error(`初始化商业赛数据失败: ${initResult.message}`, initResult.code);
        }
        businessMatch = initResult.data;
      }

      // 获取战斗次数信息
      const fightTimesInfoResult = await this.getFightTimesInfo(dto.characterId);
      if (XResultUtils.isFailure(fightTimesInfoResult)) {
        return XResultUtils.error(`获取战斗次数信息失败: ${fightTimesInfoResult.message}`, fightTimesInfoResult.code);
      }
      const fightTimesInfo = fightTimesInfoResult.data;

      // 获取最近的匹配记录
      const lastMatchRecordResult = await this.businessRepository.getLastMatchRecords(dto.characterId, 5);
      if (XResultUtils.isFailure(lastMatchRecordResult)) {
        return XResultUtils.error(`获取匹配记录失败: ${lastMatchRecordResult.message}`, lastMatchRecordResult.code);
      }
      const lastMatchRecord = lastMatchRecordResult.data;

      // 获取搜索费用
      const searchCostResult = await this.getSearchCost();
      if (XResultUtils.isFailure(searchCostResult)) {
        return XResultUtils.error(`获取搜索费用失败: ${searchCostResult.message}`, searchCostResult.code);
      }
      const searchCost = searchCostResult.data;

      // 获取球迷排名列表
      const rankListResult = await this.getGlobalFansRank();
      if (XResultUtils.isFailure(rankListResult)) {
        return XResultUtils.error(`获取球迷排名失败: ${rankListResult.message}`, rankListResult.code);
      }
      const rankList = rankListResult.data;

      const responseData: GetBusinessMatchInfoResponseDto = {
        fightTimesInfo: {
          totalTimes: fightTimesInfo.totalTimes,
          leftTimes: fightTimesInfo.leftTimes,
          usedTimes: fightTimesInfo.usedTimes,
          lastResetTime: fightTimesInfo.lastResetTime.toISOString(),
        },
        lastMatchRecord: lastMatchRecord.map(record => ({
          characterId: record.characterId,
          roomId: record.roomId,
          teamA: record.teamA,
          teamB: record.teamB,
          result: record.result,
          teamAScore: record.teamAScore,
          teamBScore: record.teamBScore,
          beginTime: record.beginTime.toISOString(),
          teamARank: record.teamARank,
          teamBRank: record.teamBRank,
          fansChangeNum: record.fansChangeNum,
          cash: record.cash,
        })),
        searchCost,
        rankList: rankList.map(rank => ({
          characterId: rank.characterId,
          name: rank.name,
          faceIcon: rank.faceIcon,
          actualStrength: rank.actualStrength,
          ballFanCount: rank.ballFanCount,
          fanRank: rank.fanRank,
          isGroundOpen: rank.isGroundOpen,
        })),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_business_match_info',
      metadata: { characterId: dto.characterId }
    });
  }

  /**
   * 商业赛搜索
   * 基于old项目: PlayerMatchService.prototype.businessSearch
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async businessSearch(dto: BusinessSearchDto): Promise<XResult<BusinessSearchResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`商业赛搜索: ${dto.characterId}, 搜索: ${dto.name}`);

      // 验证搜索名称
      if (!dto.name || dto.name.trim() === '') {
        return XResultUtils.error('名字不能为空', 'EMPTY_NAME');
      }

      // 检查搜索费用
      const searchCostResult = await this.getSearchCost();
      if (XResultUtils.isFailure(searchCostResult)) {
        return XResultUtils.error(`获取搜索费用失败: ${searchCostResult.message}`, searchCostResult.code);
      }
      const searchCost = searchCostResult.data;

      const characterInfoResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        { characterId: dto.characterId }
      );

      if (XResultUtils.isFailure(characterInfoResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterInfoResult.message}`, characterInfoResult.code);
      }

      if (characterInfoResult.data.cash < searchCost) {
        return XResultUtils.error('欧元不足', 'INSUFFICIENT_CASH');
      }

      // 搜索对手
      const enemyInfoResult = await this.searchEnemy(dto.name);
      if (XResultUtils.isFailure(enemyInfoResult)) {
        return XResultUtils.error(`搜索对手失败: ${enemyInfoResult.message}`, enemyInfoResult.code);
      }

      const enemyInfo = enemyInfoResult.data;
      if (!enemyInfo) {
        return XResultUtils.error('未找到对手', 'ENEMY_NOT_FOUND');
      }

      // 检查对手球场是否开放
      if (!enemyInfo.isGroundOpen) {
        return XResultUtils.error('对手球场未开放', 'ENEMY_GROUND_CLOSED');
      }

      // 获取战斗次数信息
      const fightTimesInfoResult = await this.getFightTimesInfo(dto.characterId);
      if (XResultUtils.isFailure(fightTimesInfoResult)) {
        return XResultUtils.error(`获取战斗次数信息失败: ${fightTimesInfoResult.message}`, fightTimesInfoResult.code);
      }
      const fightTimesInfo = fightTimesInfoResult.data;

      // 获取对手列表
      const rivalListResult = await this.getRivalList(dto.characterId);
      if (XResultUtils.isFailure(rivalListResult)) {
        return XResultUtils.error(`获取对手列表失败: ${rivalListResult.message}`, rivalListResult.code);
      }
      const rivalList = rivalListResult.data;

      const responseData: BusinessSearchResponseDto = {
        enemyInfo: {
          characterId: enemyInfo.characterId,
          name: enemyInfo.name,
          faceIcon: enemyInfo.faceIcon,
          actualStrength: enemyInfo.actualStrength,
          ballFanCount: enemyInfo.ballFanCount,
          fanRank: enemyInfo.fanRank,
          isGroundOpen: enemyInfo.isGroundOpen,
        },
        fightTimesInfo: {
          totalTimes: fightTimesInfo.totalTimes,
          leftTimes: fightTimesInfo.leftTimes,
          usedTimes: fightTimesInfo.usedTimes,
          lastResetTime: fightTimesInfo.lastResetTime.toISOString(),
        },
        rivalList: rivalList.map(rival => ({
          characterId: rival.characterId,
          name: rival.name,
          faceIcon: rival.faceIcon,
          actualStrength: rival.actualStrength,
          ballFanCount: rival.ballFanCount,
          fanRank: rival.fanRank,
          isGroundOpen: rival.isGroundOpen,
        })),
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'business_search',
      metadata: {
        characterId: dto.characterId,
        searchName: dto.name
      }
    });
  }

  /**
   * 商业赛匹配
   * 基于old项目: PlayerMatchService.prototype.businessMatch
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async businessMatch(dto: BusinessMatchDto): Promise<XResult<BusinessMatchResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`商业赛匹配: ${dto.characterId} vs ${dto.enemyUid}`);

      // 检查战斗次数
      const fightTimesInfoResult = await this.getFightTimesInfo(dto.characterId);
      if (XResultUtils.isFailure(fightTimesInfoResult)) {
        return XResultUtils.error(`获取战斗次数信息失败: ${fightTimesInfoResult.message}`, fightTimesInfoResult.code);
      }

      const fightTimesInfo = fightTimesInfoResult.data;
      if (fightTimesInfo.leftTimes <= 0) {
        return XResultUtils.error('战斗次数不足', 'INSUFFICIENT_FIGHT_TIMES');
      }

      // 获取对手信息
      const enemyInfoResult = await this.getEnemyInfo(dto.enemyUid);
      if (XResultUtils.isFailure(enemyInfoResult)) {
        return XResultUtils.error(`获取对手信息失败: ${enemyInfoResult.message}`, enemyInfoResult.code);
      }

      const enemyInfo = enemyInfoResult.data;
      if (!enemyInfo) {
        return XResultUtils.error('对手不存在', 'ENEMY_NOT_FOUND');
      }

      if (!enemyInfo.isGroundOpen) {
        return XResultUtils.error('对手球场未开放', 'ENEMY_GROUND_CLOSED');
      }

      // 获取玩家阵容数据 - 使用BaseService的标准化微服务调用
      const characterFormationResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'formation.getFormations',
        { characterId: dto.characterId }
      );

      if (XResultUtils.isFailure(characterFormationResult)) {
        return XResultUtils.error(`获取玩家战斗数据失败: ${characterFormationResult.message}`, characterFormationResult.code);
      }

      // 获取对手阵容数据
      const enemyFormationResult = await this.getEnemyBattleData(dto.enemyUid);
      if (XResultUtils.isFailure(enemyFormationResult)) {
        return XResultUtils.error(`获取对手战斗数据失败: ${enemyFormationResult.message}`, enemyFormationResult.code);
      }

      const enemyFormationData = enemyFormationResult.data;
      if (!enemyFormationData) {
        return XResultUtils.error('对手战斗数据为空', 'ENEMY_BATTLE_DATA_EMPTY');
      }

      // 转换阵容数据为战斗数据格式 - 使用共享服务（已适配Result模式）
      const characterBattleDataResult = await this.battleDataService.convertFormationToBattleData(characterFormationResult.data, dto.characterId);
      const enemyBattleDataResult = await this.battleDataService.convertFormationToBattleData(enemyFormationData, dto.enemyUid);

      if (XResultUtils.isFailure(characterBattleDataResult)) {
        return XResultUtils.error(`玩家战斗数据转换失败: ${characterBattleDataResult.message}`, characterBattleDataResult.code);
      }

      if (XResultUtils.isFailure(enemyBattleDataResult)) {
        return XResultUtils.error(`对手战斗数据转换失败: ${enemyBattleDataResult.message}`, enemyBattleDataResult.code);
      }

      const characterBattleData = characterBattleDataResult.data;
      const enemyBattleData = enemyBattleDataResult.data;

      if (!characterBattleData || !enemyBattleData) {
        return XResultUtils.error('战斗数据为空', 'BATTLE_DATA_EMPTY');
      }

      this.logger.log(`玩家战斗数据转换完成: 球员数量=${characterBattleData.heroes.length}`);
      this.logger.log(`对手战斗数据转换完成: 球员数量=${enemyBattleData.heroes.length}`);

      // 调用本地战斗服务 - 注意：battleService现在返回XResult类型
      const battleResult = await this.battleService.pvpBattle({
        homeCharacterId: dto.characterId,
        awayCharacterId: dto.enemyUid,
        homeBattleData: characterBattleData,
        awayBattleData: enemyBattleData,
        battleType: 'business',
      });

      if (XResultUtils.isFailure(battleResult)) {
        return XResultUtils.error(`战斗计算失败: ${battleResult.message}`, battleResult.code);
      }

      const battleData = battleResult.data;

      // 处理战斗结果
      const processResult = await this.processBattleResult(
        dto.characterId,
        dto.enemyUid,
        battleData,
        enemyInfo
      );

      if (XResultUtils.isFailure(processResult)) {
        return XResultUtils.error(`处理战斗结果失败: ${processResult.message}`, processResult.code);
      }

      const responseData: BusinessMatchResponseDto = {
        enemyInfo: {
          characterId: enemyInfo.characterId,
          name: enemyInfo.name,
          faceIcon: enemyInfo.faceIcon,
          actualStrength: enemyInfo.actualStrength,
          ballFanCount: enemyInfo.ballFanCount,
          fanRank: enemyInfo.fanRank,
          isGroundOpen: enemyInfo.isGroundOpen,
        },
        matchRecord: processResult.data.matchRecord,
        fightTimesInfo: processResult.data.fightTimesInfo,
        rewardInfo: processResult.data.rewardInfo,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'business_match',
      metadata: {
        characterId: dto.characterId,
        enemyUid: dto.enemyUid
      }
    });
  }

  /**
   * 购买商业赛次数
   * 基于old项目: PlayerMatchService.prototype.buyBusinessMatch
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async buyBusinessMatch(dto: BuyBusinessMatchDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`购买商业赛次数: ${dto.characterId}, 数量: ${dto.num}`);

      // 获取购买费用配置
      const buyGoldCostResult = await this.getBuyGoldCost();
      if (XResultUtils.isFailure(buyGoldCostResult)) {
        return XResultUtils.error(`获取购买费用失败: ${buyGoldCostResult.message}`, buyGoldCostResult.code);
      }

      const buyGoldCost = buyGoldCostResult.data;
      const totalCost = buyGoldCost * dto.num;

      // 检查球币是否足够 - 使用BaseService的标准化微服务调用
      const characterInfoResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        { characterId: dto.characterId }
      );

      if (XResultUtils.isFailure(characterInfoResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterInfoResult.message}`, characterInfoResult.code);
      }

      if (characterInfoResult.data.gold < totalCost) {
        return XResultUtils.error('球币不足', 'INSUFFICIENT_GOLD');
      }

      // TODO 经济系统测试通过后开启（使用BaseService的标准化微服务调用）
      // 扣除球币
      // const deductResult = await this.callMicroservice(
      //   MICROSERVICE_NAMES.ECONOMY_SERVICE,
      //   'currency.deductCurrency',
      //   {
      //     characterId: dto.characterId,
      //     currencyType: 'gold',
      //     amount: totalCost,
      //     source: 'buy_business_match',
      //   }
      // );
      //
      // if (XResultUtils.isFailure(deductResult)) {
      //   return XResultUtils.error(`扣除球币失败: ${deductResult.message}`, deductResult.code);
      // }

      // 增加战斗次数
      const fightTimesInfoResult = await this.getFightTimesInfo(dto.characterId);
      if (XResultUtils.isFailure(fightTimesInfoResult)) {
        return XResultUtils.error(`获取战斗次数信息失败: ${fightTimesInfoResult.message}`, fightTimesInfoResult.code);
      }

      const fightTimesInfo = fightTimesInfoResult.data;
      fightTimesInfo.totalTimes += dto.num;
      fightTimesInfo.leftTimes += dto.num;

      const updateResult = await this.businessRepository.updateFightTimes(dto.characterId, fightTimesInfo);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新战斗次数失败: ${updateResult.message}`, updateResult.code);
      }

      const responseData = {
        addedTimes: dto.num,
        totalCost,
        newFightTimes: fightTimesInfo,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'buy_business_match',
      metadata: {
        characterId: dto.characterId,
        num: dto.num
      }
    });
  }

  // ==================== 私有方法 ====================

  /**
   * 初始化商业赛数据
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async initBusinessMatchData(characterId: string): Promise<XResult<BusinessMatch>> {
    const searchCostResult = await this.getSearchCost();
    if (XResultUtils.isFailure(searchCostResult)) {
      return XResultUtils.error(`获取搜索费用失败: ${searchCostResult.message}`, searchCostResult.code);
    }

    const searchCost = searchCostResult.data;
    const fightTimesInfo = {
      totalTimes: 5, // 默认5次
      leftTimes: 5,
      usedTimes: 0,
      lastResetTime: new Date(),
    };

    const businessMatchData: Partial<BusinessMatch> = {
      uid: characterId,
      searchCost,
      matchRecordList: [],
      rivalList: [],
      fightTimes: fightTimesInfo,
      maxRecordNum: 30,
      lastUpdateTime: new Date(),
    };

    const createResult = await this.businessRepository.create(businessMatchData);
    if (XResultUtils.isFailure(createResult)) {
      return XResultUtils.error(`创建商业赛数据失败: ${createResult.message}`, createResult.code);
    }

    return XResultUtils.ok(createResult.data);
  }

  /**
   * 获取搜索费用
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getSearchCost(): Promise<XResult<number>> {
    const systemParam = await this.gameConfig.systemParam.get(1); // matchSearchCost的ID
    const cost = systemParam?.parameter || 10000; // 默认10000欧元
    return XResultUtils.ok(cost);
  }

  /**
   * 获取购买费用
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getBuyGoldCost(): Promise<XResult<number>> {
    const systemParam = await this.gameConfig.systemParam.get(2); // matchBuyGold的ID
    const cost = systemParam?.parameter || 100; // 默认100球币
    return XResultUtils.ok(cost);
  }

  /**
   * 获取战斗次数信息
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getFightTimesInfo(characterId: string): Promise<XResult<any>> {
    const businessMatchResult = await this.businessRepository.findByUid(characterId);
    if (XResultUtils.isFailure(businessMatchResult)) {
      // 返回默认值而不是错误
      const defaultFightTimes = {
        totalTimes: 5,
        leftTimes: 5,
        usedTimes: 0,
        lastResetTime: new Date(),
      };
      this.logger.warn(`查询商业赛数据失败，返回默认值: ${businessMatchResult.message}`);
      return XResultUtils.ok(defaultFightTimes);
    }

    const businessMatch = businessMatchResult.data;
    if (!businessMatch || !businessMatch.fightTimes) {
      const defaultFightTimes = {
        totalTimes: 5,
        leftTimes: 5,
        usedTimes: 0,
        lastResetTime: new Date(),
      };
      return XResultUtils.ok(defaultFightTimes);
    }

    // 检查是否需要重置（每日重置）
    const now = new Date();
    const lastReset = new Date(businessMatch.fightTimes.lastResetTime);
    const isNewDay = now.getDate() !== lastReset.getDate() ||
                     now.getMonth() !== lastReset.getMonth() ||
                     now.getFullYear() !== lastReset.getFullYear();

    if (isNewDay) {
      // 重置每日次数
      const resetFightTimes = {
        totalTimes: 5,
        leftTimes: 5,
        usedTimes: 0,
        lastResetTime: now,
      };
      const updateResult = await this.businessRepository.updateFightTimes(characterId, resetFightTimes);
      if (XResultUtils.isFailure(updateResult)) {
        this.logger.warn(`重置战斗次数失败: ${updateResult.message}`);
      }
      return XResultUtils.ok(resetFightTimes);
    }

    return XResultUtils.ok(businessMatch.fightTimes);
  }

  /**
   * 搜索对手
   * 基于old项目: accountService.searchPlayerName
   * 已适配Result模式：返回XResult类型，使用标准化微服务调用，移除try-catch
   */
  private async searchEnemy(name: string): Promise<XResult<BusinessRivalInfo | null>> {
    // 调用Character服务搜索玩家 - 使用BaseService的标准化微服务调用
    const searchResult = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.searchByName',
      { name }
    );

    if (XResultUtils.isFailure(searchResult)) {
      return XResultUtils.error(`搜索玩家失败: ${searchResult.message}`, searchResult.code);
    }

    if (!searchResult.data) {
      this.logger.log(`未找到对手: ${name}`);
      return XResultUtils.ok(null);
    }

    const characterData = searchResult.data;

    // 获取球迷数量和排名信息（如果需要的话）
    // TODO: 可以调用Hero服务获取更详细的球迷信息
    // const fansInfoResult = await this.callMicroservice(
    //   MICROSERVICE_NAMES.HERO_SERVICE,
    //   'ground.getFansInfo',
    //   { characterId: characterData.characterId }
    // );

    const rivalInfo: BusinessRivalInfo = {
      characterId: characterData.characterId,
      name: characterData.name,
      faceIcon: characterData.faceIcon || 0,
      actualStrength: characterData.actualStrength || 0,
      ballFanCount: characterData.ballFanCount || 0,
      fanRank: 0, // TODO: 从排名服务获取
      isGroundOpen: characterData.isGroundOpen || false,
      lastUpdateTime: characterData.lastUpdateTime || new Date(),
    };

    return XResultUtils.ok(rivalInfo);
  }

  /**
   * 获取对手信息
   * 已适配Result模式：返回XResult类型，使用标准化微服务调用，移除try-catch
   */
  private async getEnemyInfo(enemyUid: string): Promise<XResult<BusinessRivalInfo | null>> {
    // 获取对手基础信息 - 使用BaseService的标准化微服务调用
    const enemyInfoResult = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.getInfo',
      { characterId: enemyUid }
    );

    if (XResultUtils.isFailure(enemyInfoResult)) {
      return XResultUtils.error(`获取对手基础信息失败: ${enemyInfoResult.message}`, enemyInfoResult.code);
    }

    if (!enemyInfoResult.data) {
      return XResultUtils.ok(null);
    }

    // 获取球迷信息 - 使用BaseService的标准化微服务调用
    const fansInfoResult = await this.callMicroservice(
      MICROSERVICE_NAMES.HERO_SERVICE,
      'ground.getFansInfo',
      { characterId: enemyUid }
    );

    // 解析球迷信息响应 - 修复类型定义
    interface FansData {
      ballFanCount?: number;
      fanRank?: number;
      isGroundOpen?: boolean;
    }

    let fansData: FansData = {};
    if (XResultUtils.isSuccess(fansInfoResult)) {
      fansData = fansInfoResult.data || {};
      this.logger.log(`对手球迷信息: ${enemyUid}, 响应: ${JSON.stringify(fansInfoResult.data)}`);
    } else {
      this.logger.warn(`获取对手球迷信息失败: ${fansInfoResult.message}`);
    }

    const rivalInfo: BusinessRivalInfo = {
      characterId: enemyUid,
      name: enemyInfoResult.data.name,
      faceIcon: enemyInfoResult.data.faceIcon || 0,
      actualStrength: enemyInfoResult.data.actualStrength || 0,
      ballFanCount: fansData.ballFanCount || 0,
      fanRank: fansData.fanRank || 0,
      isGroundOpen: fansData.isGroundOpen || false,
      lastUpdateTime: new Date(),
    };

    return XResultUtils.ok(rivalInfo);
  }

  /**
   * 获取对手战斗数据
   * 已适配Result模式：返回XResult类型，使用标准化微服务调用，移除try-catch
   */
  private async getEnemyBattleData(enemyUid: string): Promise<XResult<any>> {
    const formationResult = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'formation.getFormations',
      { characterId: enemyUid }
    );

    if (XResultUtils.isFailure(formationResult)) {
      return XResultUtils.error(`获取对手阵容数据失败: ${formationResult.message}`, formationResult.code);
    }

    if (!formationResult.data) {
      return XResultUtils.error('对手阵容数据为空', 'ENEMY_FORMATION_DATA_EMPTY');
    }

    return XResultUtils.ok(formationResult.data);
  }



  /**
   * 获取全球球迷排名
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getGlobalFansRank(): Promise<XResult<BusinessRivalInfo[]>> {
    // 直接调用Ranking服务，而不是微服务调用
    // TODO: 注入RankingService并直接调用
    // const rankResult = await this.rankingService.getGlobalFansRank({ limit: 50 });

    // 临时返回模拟数据，避免微服务调用错误
    const mockRankList = [
        {
          characterId: 'mock_rival_1',
          name: '模拟对手1',
          faceIcon: 0,
          actualStrength: 5000,
          ballFanCount: 1000,
          fanRank: 1,
          isGroundOpen: true,
          lastUpdateTime: new Date(),
        },
        {
          characterId: 'mock_rival_2',
          name: '模拟对手2',
          faceIcon: 0,
          actualStrength: 5500,
          ballFanCount: 1200,
          fanRank: 2,
          isGroundOpen: true,
          lastUpdateTime: new Date(),
        },
      ];

    return XResultUtils.ok(mockRankList);
  }

  /**
   * 获取对手列表
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getRivalList(characterId: string): Promise<XResult<BusinessRivalInfo[]>> {
    const businessMatchResult = await this.businessRepository.findByUid(characterId);
    if (XResultUtils.isFailure(businessMatchResult)) {
      // 返回空数组而不是错误
      this.logger.warn(`查询商业赛数据失败，返回空对手列表: ${businessMatchResult.message}`);
      return XResultUtils.ok([]);
    }

    const businessMatch = businessMatchResult.data;
    const rivalList = businessMatch?.rivalList || [];
    return XResultUtils.ok(rivalList);
  }

  /**
   * 处理战斗结果
   * 已适配Result模式：返回XResult类型，使用标准化微服务调用，移除try-catch
   */
  private async processBattleResult(
    characterId: string,
    enemyUid: string,
    battleResult: any,
    enemyInfo: BusinessRivalInfo
  ): Promise<XResult<any>> {
    // 判断战斗结果
    const isWin = battleResult.winner === 1;
    const isDraw = battleResult.winner === 0;
    const result = isWin ? 1 : (isDraw ? 0 : 2);

    // 计算奖励
    const rewardInfoResult = await this.calculateReward(characterId, enemyInfo, result);
    if (XResultUtils.isFailure(rewardInfoResult)) {
      return XResultUtils.error(`计算奖励失败: ${rewardInfoResult.message}`, rewardInfoResult.code);
    }
    const rewardInfo = rewardInfoResult.data;

    // 创建匹配记录
    const matchRecord: BusinessMatchRecord = {
      characterId: characterId,
      roomId: battleResult.roomId,
      teamA: characterId,
      teamB: enemyUid,
      result,
      teamAScore: battleResult.homeScore,
      teamBScore: battleResult.awayScore,
      beginTime: new Date(),
      teamARank: 0, // 需要从排名服务获取
      teamBRank: 0,
      fansChangeNum: rewardInfo.fansChange || 0,
      cash: rewardInfo.cash || 0,
    };

    // 保存匹配记录
    const addRecordResult = await this.businessRepository.addMatchRecord(characterId, matchRecord);
    if (XResultUtils.isFailure(addRecordResult)) {
      return XResultUtils.error(`保存匹配记录失败: ${addRecordResult.message}`, addRecordResult.code);
    }

    // 更新战斗次数
    const fightTimesInfoResult = await this.getFightTimesInfo(characterId);
    if (XResultUtils.isFailure(fightTimesInfoResult)) {
      return XResultUtils.error(`获取战斗次数信息失败: ${fightTimesInfoResult.message}`, fightTimesInfoResult.code);
    }

    const fightTimesInfo = fightTimesInfoResult.data;
    fightTimesInfo.leftTimes = Math.max(0, fightTimesInfo.leftTimes - 1);
    fightTimesInfo.usedTimes += 1;

    const updateTimesResult = await this.businessRepository.updateFightTimes(characterId, fightTimesInfo);
    if (XResultUtils.isFailure(updateTimesResult)) {
      return XResultUtils.error(`更新战斗次数失败: ${updateTimesResult.message}`, updateTimesResult.code);
    }

    // 发放奖励 - 使用BaseService的标准化微服务调用
    if (rewardInfo.cash > 0) {
      const rewardResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.currency.add',
        {
          characterId,
          currencyDto: {
            currencyType: 'worldCoin', // 商业赛奖励使用欧元(worldCoin)
            amount: rewardInfo.cash,
            reason: 'business_match_reward'
          },
          serverId: 'server_001' // TODO: 从请求中获取serverId
        }
      );

      if (XResultUtils.isSuccess(rewardResult)) {
        this.logger.log(`奖励发放成功 - 角色: ${characterId}, 欧元: ${rewardInfo.cash}`);
      } else {
        this.logger.error(`奖励发放失败 - 角色: ${characterId}, 欧元: ${rewardInfo.cash}: ${rewardResult.message}`);
        // 奖励发放失败不影响战斗结果，只记录错误
      }
    }

    const responseData = {
      matchRecord: {
        characterId: matchRecord.characterId,
        roomId: matchRecord.roomId,
        teamA: matchRecord.teamA,
        teamB: matchRecord.teamB,
        result: matchRecord.result,
        teamAScore: matchRecord.teamAScore,
        teamBScore: matchRecord.teamBScore,
        beginTime: matchRecord.beginTime.toISOString(),
        teamARank: matchRecord.teamARank,
        teamBRank: matchRecord.teamBRank,
        fansChangeNum: matchRecord.fansChangeNum,
        cash: matchRecord.cash,
      },
      fightTimesInfo: {
        totalTimes: fightTimesInfo.totalTimes,
        leftTimes: fightTimesInfo.leftTimes,
        usedTimes: fightTimesInfo.usedTimes,
        lastResetTime: fightTimesInfo.lastResetTime.toISOString(),
      },
      rewardInfo,
    };

    return XResultUtils.ok(responseData);
  }

  /**
   * 计算奖励
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async calculateReward(characterId: string, enemyInfo: BusinessRivalInfo, result: number): Promise<XResult<any>> {
    // 基础奖励计算
    let baseCash = 10000; // 基础现金奖励
    let fansChange = 0;

    // 根据结果调整奖励
    if (result === 1) { // 胜利
      baseCash *= 1.5;
      fansChange = Math.floor(enemyInfo.ballFanCount * 0.01); // 获得对手1%的球迷
    } else if (result === 0) { // 平局
      baseCash *= 1.0;
      fansChange = 0;
    } else { // 失败
      baseCash *= 0.5;
      fansChange = -Math.floor(enemyInfo.ballFanCount * 0.005); // 失去0.5%的球迷
    }

    // 根据对手实力调整奖励
    const strengthFactor = Math.max(0.5, Math.min(2.0, enemyInfo.actualStrength / 100000));
    baseCash = Math.floor(baseCash * strengthFactor);

    const rewardData = {
      cash: baseCash,
      fansChange,
      strengthFactor,
    };

    return XResultUtils.ok(rewardData);
  }


}
