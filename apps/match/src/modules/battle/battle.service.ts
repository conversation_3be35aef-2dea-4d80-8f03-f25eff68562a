import { Injectable, Logger } from '@nestjs/common';
import { BattleRepository } from '../../common/repositories/battle.repository';
import { GameConfigFacade } from '@libs/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import {
  PveBattleDto,
  PvpBattleDto,
  BattleResultResponseDto,
  GetBattleReplayDto,
  GetBattleReplayResponseDto, BattleTeamDataDto
} from '../../common/dto/battle.dto';
import { BattleRoom, BattleTeamData, BattleResult } from '../../common/schemas/battle.schema';
import { BattleEngine } from './battle-engine';

// Result模式相关导入
import { BaseService } from '@libs/common/service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 战斗系统服务 - 已适配Result模式
 * 严格基于old项目battleService.js的业务逻辑实现
 *
 * 核心功能：
 * - PVE战斗计算
 * - PVP战斗计算
 * - 战斗房间管理
 * - 战斗回放生成
 * - 战斗结果处理
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的Repository调用和错误处理
 */
@Injectable()
export class BattleService extends BaseService {
  private readonly battleEngine: BattleEngine;

  constructor(
    private readonly battleRepository: BattleRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('BattleService', microserviceClient);
    this.battleEngine = new BattleEngine(this.gameConfig);
  }

  /**
   * PVE战斗
   * 基于old项目: BattleService.prototype.initPveBattle
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async pveBattle(dto: PveBattleDto): Promise<XResult<BattleResultResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`PVE战斗开始: ${dto.characterId}, 类型: ${dto.battleType}`);

      // 1. 创建战斗房间
      const roomId = this.generateRoomId();

      // 2. 初始化战斗数据
      const battleRoomResult = await this.createPveBattleRoom(roomId, dto);
      if (XResultUtils.isFailure(battleRoomResult)) {
        return XResultUtils.error(`创建战斗房间失败: ${battleRoomResult.message}`, battleRoomResult.code);
      }

      const battleRoom = battleRoomResult.data;
      if (!battleRoom) {
        return XResultUtils.error('战斗房间数据为空', 'BATTLE_ROOM_EMPTY');
      }

      // 3. 执行战斗计算
      const battleResult = await this.battleEngine.calculatePveBattle(battleRoom);

      // 4. 保存战斗结果
      const saveResult = await this.saveBattleResult(roomId, battleResult);
      if (XResultUtils.isFailure(saveResult)) {
        this.logger.warn(`保存战斗结果失败: ${saveResult.message}`);
      }

      // 5. 延迟清理战斗房间（给回放查询留时间）
      this.scheduleRoomCleanup(roomId);

      const responseData: BattleResultResponseDto = {
        roomId: roomId,
        homeScore: battleResult.homeScore,
        awayScore: battleResult.awayScore,
        winner: this.determineWinner(battleResult.homeScore, battleResult.awayScore),
        battleRecord: battleResult.battleRecord,
        statistics: battleResult.statistics,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'pve_battle',
      metadata: {
        characterId: dto.characterId,
        battleType: dto.battleType
      }
    });
  }

  /**
   * PVP战斗
   * 基于old项目: BattleService.prototype.pvpMatchBattle
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async pvpBattle(dto: PvpBattleDto): Promise<XResult<BattleResultResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`PVP战斗开始: ${dto.homeCharacterId} vs ${dto.awayCharacterId}`);

      // 1. 创建战斗房间
      const roomId = this.generateRoomId();

      // 2. 初始化战斗数据
      const battleRoomResult = await this.createPvpBattleRoom(roomId, dto);
      if (XResultUtils.isFailure(battleRoomResult)) {
        return XResultUtils.error(`创建战斗房间失败: ${battleRoomResult.message}`, battleRoomResult.code);
      }

      const battleRoom = battleRoomResult.data;
      if (!battleRoom) {
        return XResultUtils.error('战斗房间数据为空', 'BATTLE_ROOM_EMPTY');
      }

      // 3. 执行战斗计算
      const battleResult = await this.battleEngine.calculatePvpBattle(battleRoom);

      // 4. 保存战斗结果
      const saveResult = await this.saveBattleResult(roomId, battleResult);
      if (XResultUtils.isFailure(saveResult)) {
        this.logger.warn(`保存战斗结果失败: ${saveResult.message}`);
      }

      const responseData: BattleResultResponseDto = {
        roomId: roomId,
        homeScore: battleResult.homeScore,
        awayScore: battleResult.awayScore,
        winner: this.determineWinner(battleResult.homeScore, battleResult.awayScore),
        battleRecord: battleResult.battleRecord,
        statistics: battleResult.statistics,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'pvp_battle',
      metadata: {
        homeCharacterId: dto.homeCharacterId,
        awayCharacterId: dto.awayCharacterId,
        battleType: dto.battleType
      }
    });
  }

  /**
   * 获取战斗回放
   * 基于old项目的战斗回放功能
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getBattleReplay(dto: GetBattleReplayDto): Promise<XResult<GetBattleReplayResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`获取战斗回放: ${dto.roomId}`);

      const battleRoomResult = await this.battleRepository.findByRoomId(dto.roomId);
      if (XResultUtils.isFailure(battleRoomResult)) {
        return XResultUtils.error(`查询战斗房间失败: ${battleRoomResult.message}`, battleRoomResult.code);
      }

      const battleRoom = battleRoomResult.data;
      if (!battleRoom) {
        return XResultUtils.error('战斗房间不存在', 'BATTLE_ROOM_NOT_FOUND');
      }

      if (battleRoom.status !== 'finished') {
        return XResultUtils.error('战斗尚未完成', 'BATTLE_NOT_FINISHED');
      }

      const responseData: GetBattleReplayResponseDto = {
        battleRecord: battleRoom.result.battleEndInfo,
        teamAData: battleRoom.teamA,
        teamBData: battleRoom.teamB,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_battle_replay',
      metadata: { roomId: dto.roomId }
    });
  }

  /**
   * 删除战斗房间
   * 基于old项目: BattleService.prototype.deleteBattleRoom
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async deleteBattleRoom(roomId: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`删除战斗房间: ${roomId}`);

      const deleteResult = await this.battleRepository.deleteByRoomId(roomId);
      if (XResultUtils.isFailure(deleteResult)) {
        return XResultUtils.error(`删除战斗房间失败: ${deleteResult.message}`, deleteResult.code);
      }

      return XResultUtils.ok(deleteResult.data);
    }, {
      reason: 'delete_battle_room',
      metadata: { roomId }
    });
  }

  // ==================== 私有方法 ====================

  /**
   * 生成房间ID
   * 基于old项目: utils.syncCreateUid()
   */
  private generateRoomId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `room_${timestamp}_${random}`;
  }

  /**
   * 创建PVE战斗房间
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async createPveBattleRoom(roomId: string, dto: PveBattleDto): Promise<XResult<BattleRoom>> {
    // 构建玩家队伍数据
    const teamA = this.buildTeamDataFromCharacterData(dto.characterBattleData);

    // 构建敌方队伍数据
    const teamB = this.buildTeamDataFromEnemyData(dto.enemyBattleData);

    const battleRoomData: Partial<BattleRoom> = {
      roomId: roomId,
      battleType: this.getBattleTypeCode(dto.battleType),
      status: 'active',
      teamA,
      teamB,
      createdAt: new Date(),
    };

    const createResult = await this.battleRepository.create(battleRoomData);
    if (XResultUtils.isFailure(createResult)) {
      return XResultUtils.error(`创建PVE战斗房间失败: ${createResult.message}`, createResult.code);
    }

    return XResultUtils.ok(createResult.data);
  }

  /**
   * 创建PVP战斗房间
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async createPvpBattleRoom(roomId: string, dto: PvpBattleDto): Promise<XResult<BattleRoom>> {
    // 构建主队数据
    const teamA = this.buildTeamDataFromCharacterData(dto.homeBattleData);
    teamA.characterId = dto.homeCharacterId;

    // 构建客队数据
    const teamB = this.buildTeamDataFromCharacterData(dto.awayBattleData);
    teamB.characterId = dto.awayCharacterId;

    const battleRoomData: Partial<BattleRoom> = {
      roomId: roomId,
      battleType: this.getBattleTypeCode(dto.battleType),
      status: 'active',
      teamA,
      teamB,
      createdAt: new Date(),
    };

    const createResult = await this.battleRepository.create(battleRoomData);
    if (XResultUtils.isFailure(createResult)) {
      return XResultUtils.error(`创建PVP战斗房间失败: ${createResult.message}`, createResult.code);
    }

    return XResultUtils.ok(createResult.data);
  }

  /**
   * 从玩家数据构建队伍数据
   */
  private buildTeamDataFromCharacterData(characterData: any): BattleTeamData {
    // 添加详细日志来跟踪数据流
    this.logger.log(`=== buildTeamDataFromCharacterData 开始 ===`);
    this.logger.log(`输入characterData类型: ${typeof characterData}`);
    this.logger.log(`输入characterData.heroes类型: ${typeof characterData.heroes}`);
    this.logger.log(`输入characterData.heroes是否为数组: ${Array.isArray(characterData.heroes)}`);
    this.logger.log(`输入characterData.heroes长度: ${characterData.heroes?.length || 0}`);
    this.logger.log(`输入characterData完整数据: ${JSON.stringify(characterData, null, 2)}`);

    const teamData = {
      characterId: characterData.characterId || '',
      teamName: characterData.teamName || '',
      formation: characterData.formation || 0,
      tactic: characterData.tactic || 0,
      heroes: characterData.heroes || [],
      totalAttack: characterData.totalAttack || 0,
      totalDefend: characterData.totalDefend || 0,
      morale: characterData.morale || 0,
      score: 0,
    };

    this.logger.log(`输出teamData.heroes类型: ${typeof teamData.heroes}`);
    this.logger.log(`输出teamData.heroes是否为数组: ${Array.isArray(teamData.heroes)}`);
    this.logger.log(`输出teamData.heroes长度: ${teamData.heroes?.length || 0}`);
    this.logger.log(`=== buildTeamDataFromCharacterData 结束 ===`);

    return teamData;
  }

  /**
   * 从敌方配置构建队伍数据
   */
  private buildTeamDataFromEnemyData(enemyData: BattleTeamDataDto): BattleTeamData {
    return {
      characterId: 'ai_enemy',
      teamName: enemyData.teamName || 'AI队伍',
      formation: enemyData.formation || 0,
      tactic: enemyData.tactic || 0,
      heroes: enemyData.heroes || [],
      totalAttack: enemyData.totalAttack || 0,
      totalDefend: enemyData.totalDefend || 0,
      morale: enemyData.morale || 0,
      score: 0,
    };
  }

  /**
   * 获取战斗类型代码
   */
  private getBattleTypeCode(battleType: string): number {
    const battleTypeMap: { [key: string]: number } = {
      'league': 1,        // 联赛
      'business': 2,      // 商业赛
      'trophy': 3,        // 杯赛
      'tournament': 4,    // 锦标赛
      'pvp': 5,          // PVP对战
      'ground': 6,       // 球场争夺战
      'faith': 7,        // 信仰之战
    };
    return battleTypeMap[battleType] || 0;
  }

  /**
   * 保存战斗结果
   * 已适配Result模式：返回XResult类型，移除try-catch和throw
   */
  private async saveBattleResult(roomId: string, battleResult: any): Promise<XResult<void>> {
    const updateData = {
      status: 'finished',
      result: {
        roomId: roomId,
        battleType: battleResult.battleType || 0,
        homeScore: battleResult.homeScore,
        awayScore: battleResult.awayScore,
        winner: this.determineWinner(battleResult.homeScore, battleResult.awayScore),
        battleTime: new Date(),
        roundInfo: battleResult.roundInfo || [],
        battleEndInfo: battleResult.battleEndInfo || {},
        skillRecord: battleResult.skillRecord || {},
      },
      finishedAt: new Date(),
    };

    const updateResult = await this.battleRepository.updateByRoomId(roomId, updateData);
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`保存战斗结果失败: ${updateResult.message}`, updateResult.code);
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 判断胜负
   */
  private determineWinner(homeScore: number, awayScore: number): number {
    if (homeScore > awayScore) return 1;  // 主队胜
    if (awayScore > homeScore) return 2;  // 客队胜
    return 0;  // 平局
  }

  /**
   * 调度房间清理
   */
  private scheduleRoomCleanup(roomId: string): void {
    try {
      // PVE战斗房间保留30分钟用于回放查询，PVP战斗保留更长时间
      setTimeout(async () => {
        await this.cleanupBattleRoom(roomId);
      }, 30 * 60 * 1000); // 30分钟后清理
    } catch (error) {
      this.logger.error('调度房间清理失败', error);
    }
  }

  /**
   * 清理战斗房间
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async cleanupBattleRoom(roomId: string): Promise<XResult<void>> {
    const deleteResult = await this.battleRepository.deleteByRoomId(roomId);
    if (XResultUtils.isFailure(deleteResult)) {
      this.logger.error(`清理战斗房间异常: ${roomId}, ${deleteResult.message}`);
      return XResultUtils.error(`清理战斗房间失败: ${deleteResult.message}`, deleteResult.code);
    }

    if (deleteResult.data) {
      this.logger.log(`战斗房间已清理: ${roomId}`);
    } else {
      this.logger.warn(`战斗房间清理失败，房间不存在: ${roomId}`);
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 获取战斗统计信息
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async getBattleStatistics(): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const statisticsResult = await this.battleRepository.getStatistics();
      if (XResultUtils.isFailure(statisticsResult)) {
        // 返回默认统计信息而不是错误
        const defaultStats = {
          totalRooms: 0,
          activeRooms: 0,
          finishedRooms: 0,
          todayBattles: 0,
          timestamp: new Date(),
        };
        this.logger.warn(`获取战斗统计信息失败，返回默认值: ${statisticsResult.message}`);
        return XResultUtils.ok(defaultStats);
      }

      return XResultUtils.ok(statisticsResult.data);
    }, {
      reason: 'get_battle_statistics'
    });
  }

  /**
   * 清理过期房间
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async cleanExpiredRooms(): Promise<XResult<number>> {
    return this.executeBusinessOperation(async () => {
      const cleanResult = await this.battleRepository.cleanExpiredRooms(24); // 清理24小时前的房间
      if (XResultUtils.isFailure(cleanResult)) {
        this.logger.error(`清理过期房间失败: ${cleanResult.message}`);
        return XResultUtils.ok(0); // 返回0表示没有清理任何房间
      }

      return XResultUtils.ok(cleanResult.data);
    }, {
      reason: 'clean_expired_rooms'
    });
  }
}
