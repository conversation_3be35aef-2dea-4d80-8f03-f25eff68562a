import { Logger } from '@nestjs/common';
import { GameConfigFacade } from '@libs/game-config';
import { BattleRoom, BattleTeamData } from '../../common/schemas/battle.schema';

/**
 * 战斗计算引擎
 * 基于old项目room.js的核心战斗计算逻辑
 * 
 * 核心功能：
 * - 战斗回合计算
 * - 进攻防守计算
 * - 技能效果处理
 * - 战斗结果生成
 */
export class BattleEngine {
  private readonly logger = new Logger(BattleEngine.name);

  constructor(private readonly gameConfig: GameConfigFacade) {}

  /**
   * 计算PVE战斗
   * 基于old项目: Room.prototype.calcBattleResult
   */
  async calculatePveBattle(battleRoom: BattleRoom): Promise<any> {
    this.logger.log(`开始PVE战斗计算: ${battleRoom.roomId}`);

    try {
      // 初始化战斗数据
      const battleData = this.initializeBattleData(battleRoom);

      // 执行战斗回合
      const battleResult = this.executeBattleRounds(battleData);

      // 生成战斗结果
      return this.generateBattleResult(battleResult, battleRoom.battleType);
    } catch (error) {
      this.logger.error('PVE战斗计算失败', error);
      throw error;
    }
  }

  /**
   * 计算PVP战斗
   * 基于old项目: Room.prototype.calcBattleResult
   */
  async calculatePvpBattle(battleRoom: BattleRoom): Promise<any> {
    this.logger.log(`开始PVP战斗计算: ${battleRoom.roomId}`);

    try {
      // 初始化战斗数据
      const battleData = this.initializeBattleData(battleRoom);

      // 执行战斗回合
      const battleResult = this.executeBattleRounds(battleData);

      // 生成战斗结果
      return this.generateBattleResult(battleResult, battleRoom.battleType);
    } catch (error) {
      this.logger.error('PVP战斗计算失败', error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 初始化战斗数据
   * 基于old项目: Room.prototype.initRoundResultData
   */
  private initializeBattleData(battleRoom: BattleRoom): any {
    this.logger.log(`初始化战斗数据 - 房间: ${battleRoom.roomId}`);
    this.logger.debug(`队伍A球员数量: ${battleRoom.teamA?.heroes?.length || 0}, 队伍B球员数量: ${battleRoom.teamB?.heroes?.length || 0}`);

    const battleData = {
      roomId: battleRoom.roomId,
      battleType: battleRoom.battleType,
      teamA: { ...battleRoom.teamA, score: 0 },
      teamB: { ...battleRoom.teamB, score: 0 },
      currentRound: 0,
      maxRounds: 90, // 90分钟比赛
      battleTime: 0,
      roundRecords: [],
      isFinished: false,
    };

    // 计算队伍属性
    this.calculateTeamAttributes(battleData.teamA);
    this.calculateTeamAttributes(battleData.teamB);

    this.logger.log(`战斗数据初始化完成 - 队伍A: ${battleData.teamA.heroes?.length || 0}人, 队伍B: ${battleData.teamB.heroes?.length || 0}人`);

    return battleData;
  }

  /**
   * 计算队伍属性
   * 基于old项目的队伍属性计算逻辑
   */
  private calculateTeamAttributes(team: any): void {
    this.logger.debug(`计算队伍属性 - 队伍: ${team.teamName || 'Unknown'}`);

    let totalAttack = 0;
    let totalDefend = 0;
    let totalMorale = 0;

    
    const heroes = team.heroes || [];

    this.logger.debug(`计算队伍属性，球员数量: ${heroes.length}`);

    // 计算球员总属性
    for (const hero of heroes) {
      if (hero && typeof hero === 'object') {
        totalAttack += hero.attack || 0;
        totalDefend += hero.defend || 0;
        totalMorale += (hero.attack + hero.defend) / 2;
      } else {
        this.logger.warn(`无效的球员数据: ${JSON.stringify(hero)}`);
      }
    }

    // 阵型加成
    const formationBonus = this.getFormationBonus(team.formation);
    totalAttack *= formationBonus.attackFactor;
    totalDefend *= formationBonus.defendFactor;

    // 战术加成
    const tacticBonus = this.getTacticBonus(team.tactic);
    totalAttack *= tacticBonus.attackFactor;
    totalDefend *= tacticBonus.defendFactor;

    // 更新team对象的属性
    team.totalAttack = Math.floor(totalAttack);
    team.totalDefend = Math.floor(totalDefend);
    team.morale = heroes.length > 0 ? Math.floor(totalMorale / heroes.length) : 0;

    this.logger.debug(`队伍属性计算完成 - 总攻击: ${team.totalAttack}, 总防守: ${team.totalDefend}, 士气: ${team.morale}`);
  }

  /**
   * 获取阵型加成
   */
  private getFormationBonus(formationId: number): any {
    // 简化的阵型加成计算，实际应该从配置表获取
    const formationBonuses: { [key: number]: any } = {
      1: { attackFactor: 1.1, defendFactor: 0.9 },  // 进攻型阵型
      2: { attackFactor: 1.0, defendFactor: 1.0 },  // 平衡型阵型
      3: { attackFactor: 0.9, defendFactor: 1.1 },  // 防守型阵型
    };
    return formationBonuses[formationId] || { attackFactor: 1.0, defendFactor: 1.0 };
  }

  /**
   * 获取战术加成
   */
  private getTacticBonus(tacticId: number): any {
    // 简化的战术加成计算，实际应该从配置表获取
    const tacticBonuses: { [key: number]: any } = {
      1: { attackFactor: 1.05, defendFactor: 0.95 }, // 进攻战术
      2: { attackFactor: 1.0, defendFactor: 1.0 },   // 平衡战术
      3: { attackFactor: 0.95, defendFactor: 1.05 }, // 防守战术
    };
    return tacticBonuses[tacticId] || { attackFactor: 1.0, defendFactor: 1.0 };
  }

  /**
   * 执行战斗回合
   * 基于old项目: Room.prototype.calcEventFlow
   */
  private executeBattleRounds(battleData: any): any {
    while (battleData.battleTime < battleData.maxRounds && !battleData.isFinished) {
      // 计算当前回合
      const roundResult = this.calculateRound(battleData);
      battleData.roundRecords.push(roundResult);
      
      // 更新比分
      if (roundResult.goal) {
        if (roundResult.attacker === 'teamA') {
          battleData.teamA.score++;
        } else {
          battleData.teamB.score++;
        }
      }

      // 更新战斗时间
      battleData.battleTime += this.getRandomTime(1, 3);
      battleData.currentRound++;

      // 检查是否结束
      if (battleData.currentRound >= 30) { // 最多30回合
        battleData.isFinished = true;
      }
    }

    return battleData;
  }

  /**
   * 计算单个回合
   * 基于old项目的回合计算逻辑
   */
  private calculateRound(battleData: any): any {
    // 随机选择进攻方
    const attacker = Math.random() > 0.5 ? 'teamA' : 'teamB';
    const defender = attacker === 'teamA' ? 'teamB' : 'teamA';

    const attackerTeam = battleData[attacker];
    const defenderTeam = battleData[defender];

    // 计算进攻成功率
    const attackPower = attackerTeam.totalAttack + attackerTeam.morale;
    const defendPower = defenderTeam.totalDefend + defenderTeam.morale;
    
    const successRate = attackPower / (attackPower + defendPower);
    const random = Math.random();
    
    const isGoal = random < successRate * 0.3; // 30%的成功率转化为进球

    return {
      roundIndex: battleData.currentRound,
      attacker,
      defender,
      attackPower,
      defendPower,
      successRate,
      random,
      goal: isGoal,
      battleTime: battleData.battleTime,
    };
  }

  /**
   * 生成战斗结果
   * 采用old项目的回放记录格式，同时保留现有内容作为参考
   */
  private generateBattleResult(battleData: any, battleType: number): any {
    // 转换为old项目的battleRoundInfo格式
    const battleRoundInfo = battleData.roundRecords.map((round, index) => ({
      eventTime: round.battleTime,
      moraleA: battleData.teamA.morale || 0,
      moraleB: battleData.teamB.morale || 0,
      moraleSlotA: 0, // 士气槽，简化处理
      moraleSlotB: 0,
      attackerType: round.attacker,
      attackMode: round.attackMode || 0,
      periodInfo: [
        {
          A1Info: {
            heroUid: `hero_${round.attacker}_${index}`,
            attrType1: 0,
            attrValue1: 0,
            attrType2: 0,
            attrValue2: 0,
            resId: 21
          },
          actionID: round.goal ? 33 : 0,
          startCommentID: round.goal ? 415000 : 0,
          resultCommentID: 0,
          actionPer: Math.floor(round.successRate * 1000),
          actionResult: round.goal ? 1 : 0,
          moraleA: battleData.teamA.morale || 0,
          moraleB: battleData.teamB.morale || 0
        }
      ],
      scoreA: round.attacker === 'teamA' && round.goal ?
        battleData.roundRecords.slice(0, index + 1).filter(r => r.attacker === 'teamA' && r.goal).length :
        battleData.roundRecords.slice(0, index).filter(r => r.attacker === 'teamA' && r.goal).length,
      scoreB: round.attacker === 'teamB' && round.goal ?
        battleData.roundRecords.slice(0, index + 1).filter(r => r.attacker === 'teamB' && r.goal).length :
        battleData.roundRecords.slice(0, index).filter(r => r.attacker === 'teamB' && r.goal).length
    }));

    // old项目格式的战斗结束信息
    const battleEndInfo = {
      stInfos: [
        {
          shotNum: battleData.teamA.score,
          teamName: battleData.teamA.teamName || 'TeamA',
          characterId: battleData.teamA.characterId
        },
        {
          shotNum: battleData.teamB.score,
          teamName: battleData.teamB.teamName || 'TeamB',
          characterId: battleData.teamB.characterId
        }
      ],
      winner: this.determineWinner(battleData.teamA.score, battleData.teamB.score),
      battleTime: battleData.battleTime
    };

    return {
      roomId: battleData.roomId,
      battleType,
      homeScore: battleData.teamA.score,
      awayScore: battleData.teamB.score,
      // old项目格式的战斗记录
      preBattleInfo: [], // 战前信息，简化处理
      battleRoundInfo: battleRoundInfo,
      battleEndInfo: battleEndInfo,
      skillRecord: {
        durRecord: [],
        insRecord: [],
        nextAtkRecord: []
      },
      // 保留现有格式作为参考
      battleRecord: {
        roundRecords: battleData.roundRecords,
        totalRounds: battleData.currentRound,
        battleTime: battleData.battleTime,
      },
      statistics: {
        teamAAttacks: battleData.roundRecords.filter(r => r.attacker === 'teamA').length,
        teamBAttacks: battleData.roundRecords.filter(r => r.attacker === 'teamB').length,
        teamAGoals: battleData.teamA.score,
        teamBGoals: battleData.teamB.score,
      }
    };
  }

  /**
   * 判断胜负
   */
  private determineWinner(scoreA: number, scoreB: number): number {
    if (scoreA > scoreB) return 1;  // A队胜
    if (scoreB > scoreA) return 2;  // B队胜
    return 0;  // 平局
  }

  /**
   * 获取随机时间
   */
  private getRandomTime(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
}
