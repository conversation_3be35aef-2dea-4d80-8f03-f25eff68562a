import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { RankingService } from './ranking.service';
import { 

  GetGlobalRankingResponseDto,
  GetCharacterRankingResponseDto,
  ClaimRankingRewardResponseDto,
  UpdateRankingResponseDto
} from '../../common/dto/ranking.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import { XResponse, XResultUtils } from '@libs/common/types/result.type';

import {
  ClaimRankingRewardPayloadDto,
  CleanExpiredRankingsPayloadDto,
  GetCharacterRankingPayloadDto,
  GetGlobalRankingPayloadDto,
  GetStatisticsPayloadDto,
  UpdateAllRankingsPayloadDto,
  UpdateRankingPayloadDto
} from "@match/common/dto/ranking-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
/**
 * 排名系统控制器
 * 基于old项目中各种排名功能的接口设计
 * 
 * 核心接口：
 * - ranking.getGlobalRanking: 获取全球排名
 * - ranking.getCharacterRanking: 获取玩家排名信息
 * - ranking.claimRankingReward: 领取排名奖励
 * - ranking.updateRanking: 更新排名数据（管理接口）
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class RankingController extends BaseController {
  constructor(private readonly rankingService: RankingService) {
    super('RankingController');
  }

  /**
   * 获取全球排名
   * 基于old项目的全球排名功能
   */
  @MessagePattern('ranking.getGlobalRanking')
  @Cacheable({
    key: 'ranking:global:#{payload.rankType}:#{payload.limit}:#{payload.offset}',
    dataType: 'global',
    ttl: 600 // 10分钟缓存
  })
  async getGlobalRanking(@Payload() payload: GetGlobalRankingPayloadDto): Promise<XResponse<GetGlobalRankingResponseDto>> {
    this.logger.log(`获取全球排名: ${payload.rankType}, 限制: ${payload.limit || 100}`);
    
    const result = await this.rankingService.getGlobalRanking(payload);
    return this.fromResult(result);
  }

  /**
   * 获取玩家排名信息
   * 基于old项目的玩家个人排名功能
   */
  @MessagePattern('ranking.getCharacterRanking')
  @Cacheable({
    key: 'ranking:character:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getCharacterRanking(@Payload() payload: GetCharacterRankingPayloadDto): Promise<XResponse<GetCharacterRankingResponseDto>> {
    this.logger.log(`获取玩家排名信息: ${payload.characterId}`);
    
    const result = await this.rankingService.getCharacterRanking(payload);
    return this.fromResult(result);
  }

  /**
   * 领取排名奖励
   * 基于old项目的排名奖励发放功能
   */
  @MessagePattern('ranking.claimRankingReward')
  @CacheEvict({
    key: 'ranking:character:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async claimRankingReward(@Payload() payload: ClaimRankingRewardPayloadDto): Promise<XResponse<ClaimRankingRewardResponseDto>> {
    this.logger.log(`领取排名奖励: ${payload.characterId}, 类型: ${payload.rankType}, 赛季: ${payload.season}`);
    
    const result = await this.rankingService.claimRankingReward(payload);
    return this.fromResult(result);
  }

  /**
   * 更新排名数据（管理接口）
   * 基于old项目的排名更新功能
   */
  @MessagePattern('ranking.updateRanking')
  @CacheEvict({
    key: 'ranking:global:#{payload.rankType}:*',
    dataType: 'global'
  })
  async updateRanking(@Payload() payload: UpdateRankingPayloadDto): Promise<XResponse<UpdateRankingResponseDto>> {
    this.logger.log(`更新排名数据: ${payload.rankType}, 赛季: ${payload.season || 'current'}`);
    
    const result = await this.rankingService.updateRanking(payload);
    return this.fromResult(result);
  }

  /**
   * 获取排名统计信息（管理接口）
   */
  @MessagePattern('ranking.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取排名统计信息');
    
    const result = await this.rankingService.getStatistics();
    return this.fromResult(result);
  }

  /**
   * 清理过期排名数据（管理接口）
   * TODO: 未实现
   */
  @MessagePattern('ranking.cleanExpiredRankings')
  async cleanExpiredRankings(@Payload() payload: CleanExpiredRankingsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('清理过期排名数据');
    
    // const result = await this.rankingService.cleanExpiredRankings();
    // return this.fromResult(result);

    return this.toSuccessResponse({});
  }

  /**
   * 批量更新所有排名（管理接口）
   */
  @MessagePattern('ranking.updateAllRankings')
  async updateAllRankings(@Payload() payload: UpdateAllRankingsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量更新所有排名: 赛季 ${payload.season || 'current'}`);

    return this.handleRequest(async () => {
      const rankTypes = ['fans', 'strength', 'level'];
      const results = [];

      for (const rankType of rankTypes) {
        const result = await this.rankingService.updateRanking({
          rankType,
          season: payload.season,
        });

        if (XResultUtils.isFailure(result)) {
          this.logger.log(`更新排名失败: ${result.message}`, result.code);
          return this.toErrorResponse(result.message, result.code);
        }
        results.push({ rankType, ...result.data });
      }

      const successCount = results.filter(r => r.code === 0).length;

      this.logger.log(`批量更新结果统计: 成功 ${successCount}/${rankTypes.length}`);
      this.logger.log(`详细结果: ${JSON.stringify(results)}`);

      return this.toSuccessResponse(results);
    }, payload);
  }
}
