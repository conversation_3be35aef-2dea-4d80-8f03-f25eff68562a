/**
 * 精力系统控制器
 * 基于old项目everyDayEnergy.js和commonActivity.js接口迁移
 */

import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { Cacheable, CacheEvict } from '@libs/redis';
import { EnergyService } from './energy.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  BuyTeamFormationGiftPayloadDto,
  ClaimConsumeRewardPayloadDto,
  ConsumeEnergyPayloadDto,
  GetEnergyInfoPayloadDto,
  GetEnergyStatsPayloadDto,
  TakeDailyEnergyPayloadDto
} from "@activity/common/dto/energy-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class EnergyController extends BaseController {
  constructor(private readonly energyService: EnergyService) {
    super('EnergyController');
  }

  /**
   * 获取精力信息
   */
  @MessagePattern('energy.getInfo')
  @Cacheable({
    key: 'energy:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getEnergyInfo(@Payload() payload: GetEnergyInfoPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取精力信息: ${payload.uid}`);
    const result = await this.energyService.getEnergyInfo(payload.uid, payload.serverId);
    return this.fromResult(result);
  }

  /**
   * 领取每日精力
   */
  @MessagePattern('energy.takeDaily')
  @CacheEvict({
    key: 'energy:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async takeEveryDayEnergy(@Payload() payload: TakeDailyEnergyPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`领取每日精力: ${payload.uid}, 时段: ${payload.timeSlot}`);
    const result = await this.energyService.takeEveryDayEnergy(
      payload.uid, 
      payload.serverId, 
      payload.timeSlot
    );
    return this.fromResult(result);
  }

  /**
   * 消耗精力
   */
  @MessagePattern('energy.consume')
  @CacheEvict({
    key: 'energy:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async consumeEnergy(@Payload() payload: ConsumeEnergyPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`消耗精力: ${payload.uid}, 数量: ${payload.amount}`);
    const result = await this.energyService.consumeEnergy(
      payload.uid, 
      payload.serverId, 
      payload.amount
    );
    return this.fromResult(result);
  }

  /**
   * 领取精力消耗奖励
   */
  @MessagePattern('energy.claimReward')
  @CacheEvict({
    key: 'energy:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async claimConsumeReward(@Payload() payload: ClaimConsumeRewardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`领取精力消耗奖励: ${payload.uid}, 奖励索引: ${payload.rewardIndex}`);
    const result = await this.energyService.claimConsumeReward(
      payload.uid, 
      payload.serverId, 
      payload.rewardIndex
    );
    return this.fromResult(result);
  }

  /**
   * 购买限时阵型重置礼包
   */
  @MessagePattern('energy.buyFormationGift')
  @CacheEvict({
    key: 'energy:info:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyTeamFormationGift(@Payload() payload: BuyTeamFormationGiftPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`购买限时阵型重置礼包: ${payload.uid}`);
    const result = await this.energyService.buyTeamFormationGift(
      payload.uid, 
      payload.serverId
    );
    return this.fromResult(result);
  }

  /**
   * 获取精力统计信息（管理接口）
   */
  @MessagePattern('energy.getStats')
  async getEnergyStats(@Payload() payload: GetEnergyStatsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取精力统计信息');
    // TODO: 验证管理员权限
    const stats = await this.energyService.getEnergyStats();
    return this.fromResult(stats);
  }
}
