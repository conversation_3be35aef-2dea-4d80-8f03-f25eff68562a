import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { GuideService } from './guide.service';


import { XResponse } from '@libs/common/types/result.type';

import {
  CompleteGuideStepPayloadDto,
  GetGuideStatusPayloadDto,
  ResetGuidePayloadDto,
  SkipGuidePayloadDto
} from "@activity/common/dto/guide-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class GuideController extends BaseController {
  constructor(private readonly guideService: GuideService) {
    super('GuideController');
  }

  /**
   * 获取新手引导状态
   */
  @MessagePattern('guide.getStatus')
  async getGuideStatus(@Payload() payload: GetGuideStatusPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取新手引导状态: ${payload.characterId}`);
    const result = await this.guideService.getGuideStatus(payload.characterId);
    return this.fromResult(result);
  }

  /**
   * 完成引导步骤
   */
  @MessagePattern('guide.completeStep')
  async completeGuideStep(@Payload() payload: CompleteGuideStepPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`完成引导步骤: ${payload.characterId}, 步骤: ${payload.stepId}`);
    const result = await this.guideService.updateGuide(payload.characterId, payload.stepId);
    return this.fromResult(result);
  }

  /**
   * 跳过引导
   */
  @MessagePattern('guide.skip')
  async skipGuide(@Payload() payload: SkipGuidePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`跳过引导: ${payload.characterId}`);
    const result = await this.guideService.skipGuide(payload.characterId);
    return this.fromResult(result);
  }

  /**
   * 重置引导
   */
  @MessagePattern('guide.reset')
  async resetGuide(@Payload() payload: ResetGuidePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`重置引导: ${payload.characterId}`);
    const result = await this.guideService.resetGuide(payload.characterId);
    return this.fromResult(result);
  }
}
