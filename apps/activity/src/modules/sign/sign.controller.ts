/*
 * @Author: colorinstance <EMAIL>
 * @Date: 2025-08-29 13:14:01
 * @LastEditors: colorinstance <EMAIL>
 * @LastEditTime: 2025-09-06 18:47:25
 * @FilePath: \server-new\apps\activity\src\modules\sign\sign.controller.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { SignService } from './sign.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  DailySignPayloadDto,
  GetSignStatusPayloadDto, MakeUpSignPayloadDto,
  SevenDayRewardPayloadDto
} from "@activity/common/dto/sign-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class SignController extends BaseController {
  constructor(private readonly signService: SignService) {
    super('SignController');
  }

  /**
   * 每日签到
   */
  @MessagePattern('sign.daily')
  async dailySign(@Payload() payload: DailySignPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`每日签到: ${payload.characterId}`);
    const result = await this.signService.dailySign(payload.characterId, payload.openServerTime || Date.now());
    return this.fromResult(result);
  }

  /**
   * 获取签到状态
   */
  @MessagePattern('sign.getStatus')
  async getSignStatus(@Payload() payload: GetSignStatusPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取签到状态: ${payload.characterId}`);
    const result = await this.signService.getSignInfo(payload.characterId, payload.openServerTime || Date.now());
    return this.fromResult(result);
  }

  /**
   * 七日签到奖励
   */
  @MessagePattern('sign.sevenDayReward')
  async sevenDaySignReward(@Payload() payload: SevenDayRewardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`七日签到奖励: ${payload.characterId}, 第${payload.day}天`);
    const result = await this.signService.getSevenDaySignReward(payload.characterId, payload.day);
    return this.fromResult(result);
  }

  /**
   * 补签
   */
  @MessagePattern('sign.makeUp')
  async makeUpSign(@Payload() payload: MakeUpSignPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`补签: ${payload.characterId}, 目标日期: ${payload.targetDay}`);
    const result = await this.signService.makeUpSign(payload.characterId, payload.targetDay);
    return this.fromResult(result);
  }
}
