/**
 * Created by June on 2019/4/5.
 * Manage Global Player Online State
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');
var Code = require('../../../../shared/code');
var commonEnum = require('../../../../shared/enum');
var routeUtil = require('../../util/routeUtil');
var utils = require('../../util/utils');
//var clusterConfig = require('../../../config/cluster.json');
var http = require('http');
var url = require('url');
var qs = require('querystring');
var clusterConfig = require('../../../config/cluster');
var serversConfig = require('../../../config/servers');
var Player = require('../../domain/entities/player');
var dataApi = require('../../util/dataApi');
var Calc = require('../../util/calc');
var Constant = require('../../../../shared/constant');
//var crypto = require('crypto');

var newVersionDay = new Date("2019-10-24 20:24:00");

module.exports.create = function(app, dbclient){
    return new PayService(app, dbclient);
};

var PayService = function(app, dbclient) {
    EventEmitter.call(this);
    this.app = app;
    //Db init
    this.payDao = require('../../dao/payDao').create(dbclient);
    this.payServer = initPayHttpServer(this);
    this.rankRobotCache = new Map();    //{oid, battleData}
    this.maxRankRobotNum = 45;
    this.lockedPayMap = new Map();
    this.intervalTime = 1 * 1000;	    //timer设置为1秒一次
    this.timeCount = 0;
    this.MAXTimeOut = 30 * 60 * 1000;    //半小时刷新一次标志，以防没有返回
    this.canSend = true;        //是否可以发邮件
    this.sendMailObj = {};      //保存发送邮件对象记录还没发送的
};

util.inherits(PayService, EventEmitter);

var initPayHttpServer = function (self) {
    var port = clusterConfig.payHttpPort;
    var server = http.createServer(function (req, res) {
        if(req.method === "GET") {
            logger.debug('-------- Pay Http Server ---------- GET Method. url: ', req.url);
            // let urlObj = url.parse(req.url);
            // let urlParam = qs.parse(urlObj.query);
            // logger.debug('urlObj, urlParam: ', urlObj, urlParam);
            // //查询发货状态订单
            // //参数格式: http://ip:6620/get_pay_info?order_no=201910161839012234134849950&out_order_no=TQtHgXxQHebEXrQODFI9JL7W
            // if(urlObj.pathname === "/get_pay_info") {
            //     self.getPayOrderInfo(urlParam, res, self);
            // }
            res.write("fail");
            res.end();
        }
        if(req.method === "POST") {
            var postData = "";
            req.on('data', function (data) {
                postData += data;
            });
            req.on('end', function () {
                //解析参数成为Json结构对象
                logger.debug('-------- Pay Http Server ----------- POST Method. postData: ',postData, req.headers);
                // var params = self.getParamFromPost(req.headers['content-type'], postData);
                var params = JSON.parse(postData);
                logger.debug('-------- Pay Http Server ----------- POST Method. params: ',params);
                if(req.url == "/pay_callback/") {
                    self.payCallbackFromDqd(params, res, self);
                } else {
                    res.write("fail");
                    res.end();
                }
                // }else if(req.url == "/actvity/best_eleven/") {
                //     self.dealWithBestEleven(params, res, self);
                // }else if(req.url == "/actvity/lottery_draw/") {
                //     self.dealWithLotteryDraw(params, res, self);
                // }else if(req.url == "/rank_activity/get_base_info/") {
                //     self.dealWithGetBasePlayerInfo(params, res, self);
                // }else if(req.url == "/rank_activity/get_team_formation/") {
                //     self.dealWithGetTeamFormation(params, res, self);
                // }else if(req.url == "/rank_activity/rank_battle/") {
                //     self.dealWithRankBattle(params, res, self);
                // }else if(req.url == "/rank_activity/robot_refresh/") {
                //     self.dealWithRankRobotRefresh(params, res, self);
                // }else if(req.url == "/dmp/get_user_info/")//获取用户基本信息接口
                // {
                //     self.getPlayerInfoByName(params, res, self);
                // }else if(req.url == "/dmp/send_mail/")//发送邮件接口
                // {
                //     self.sendMail(params, res, self);
                // }else if(req.url == "/dmp/update_active/")//更新活动配置
                // {
                //     self.doUpdateActiveData(params, res, self);
                // }else if(req.url == "/dmp/get_activeConfig/")//获取活动配置
                // {
                //     self.getActiveConfigData(params, res, self);
                // }else if(req.url == "/dmp/get_goldPlan_Info/")//获取球币计划信息
                // {
                //     self.getPlayerGoldPlan(params, res, self);
                // }else if(req.url == "/dmp/open_goldPlan/")//开通球币计划
                // {
                //     self.openPlayerGoldPlan(params, res, self);
                // }else {
                //     res.write("fail");
                //     res.end();
                // }
            })
        }
    });
    server.listen(port);
    logger.debug('pay http server start, listen port:',port);
    return server;
};

PayService.prototype.getPayOrderInfo = function(urlParam, res, self) {
    self.payDao.findOneOrder(urlParam.out_order_no, function (code, doc) {
        let retMsg = {};
        retMsg.code = code;
        if(code !== Code.OK) {
            retMsg.result = "fail";
        }
        else {
            retMsg.result = "success";
            //兼容性
            if(!doc.callbackTime) {
                //旧的数据
                if(doc.state === commonEnum.RECHARGE_STATE.GET_CALLBACK) {
                    retMsg.state = commonEnum.RECHARGE_STATE.SEND_GOODS;
                }else {
                    retMsg.state = commonEnum.RECHARGE_STATE.CREATE_ORDER;
                }
                retMsg.createTime = doc.time;
                retMsg.callbackTime = doc.time;
                retMsg.sendTime = doc.time;
            }else {
                retMsg.state = doc.state;
                retMsg.createTime = doc.time;
                retMsg.callbackTime = doc.callbackTime;
                retMsg.sendTime = doc.sendTime;
            }
        }
        logger.debug("get_pay_info retMsg: ", retMsg);
        res.write(JSON.stringify(retMsg));
        res.end();
    });
};

PayService.prototype.getParamFromPost = function(contentType, postData) {
    let params = {};
    let tmpArr = [];
    if(contentType.indexOf("urlencoded") >= 0) {
        tmpArr = postData.split('&');
        for(let i=0,lens=tmpArr.length; i<lens; i++) {
            let arr = tmpArr[i].split('=');
            params[arr[0]] = arr[1];
        }
    }else if(contentType.indexOf("json") >= 0) {
        //params = JSON.parse(postData);
        params = qs.parse(postData);
    }
    return params;
};

PayService.prototype.payCallbackFromDqd = function(params, res, self) {
    let dqdOrderId = params.order_no;
    if(dqdOrderId && this.lockedPayMap.has(dqdOrderId)) {
        logger.error("payCallbackFromDqd now locked. dqdOrderId: ", dqdOrderId);
        dealWithPayBack(self, "fail", res, false, dqdOrderId);
        return;
    }
    //锁单
    logger.debug("payCallbackFromDqd set lock. ", dqdOrderId);
    this.lockedPayMap.set(dqdOrderId, 1);
    let checkSign = utils.getCommonMd5Sign(params, clusterConfig.secret_key);
    logger.debug("payCallbackFromDqd checkSign: ", checkSign, params.sign);
    if(params.sign === checkSign) {
    //if(true) {
        //查询订单信息, 避免重复充值
        let orderId = params.order_no;
        self.payDao.findOneOrder(orderId, function (code, doc) {
            if(code !== Code.OK) {
                logger.error("findOneOrder fail. orderId: ", orderId);
                dealWithPayBack(self, "fail", res, doc, false, dqdOrderId);
                return;
            }
            logger.error("payCallbackFromDqd doc & params :", doc, params);
            //重复充值过滤
            if(doc.state === 2) {
                logger.error("payCallbackFromDqd already rechargeed. orderId: ", orderId);
                dealWithPayBack(self, "success", res, doc, true, dqdOrderId);
                return;
            }
            //设置状态
            self.payDao.updateOrderState(orderId, commonEnum.RECHARGE_STATE.GET_CALLBACK, dqdOrderId, function (code) {
                if(code !== Code.OK) {
                    logger.error("updateOrderState GET_CALLBACK fail. orderId: ", orderId);
                    return dealWithPayBack(self, "fail", res, doc, false, dqdOrderId);
                }
                dealWithPayBack(self, "success", res, doc, false, dqdOrderId);
            })
        });
    }else {
        logger.error("payCallbackFromDqd sign check fail. dqdSign, mySign:", params.sign, checkSign);
        dealWithPayBack(self, "fail", res, false, dqdOrderId);
    }
};

var dealWithPayBack = function (self, result, res, doc, justReturn, dqdOrderId) {
    if(result !== "success" || !doc || !!justReturn) {
        logger.debug("send back msg 1: ", result, dqdOrderId);
        res.write(result);
        res.end();
        if(dqdOrderId) {
            logger.debug("payCallbackFromDqd delete lock. ", dqdOrderId);
            self.lockedPayMap.delete(dqdOrderId);
        }
        return;
    }
    let session = {frontendId: self.app.getServerId(), toServerId: doc.gid};
    let msg = {result: result, doc: doc};
    if(result !== "success") {
        logger.debug("send back msg 3: ", result);
        res.write(result);
        res.end();
        if(dqdOrderId) {
            logger.debug("payCallbackFromDqd delete lock. ", dqdOrderId);
            self.lockedPayMap.delete(dqdOrderId);
        }
        return;
    }
    self.app.rpc.game.entryRemote.dealWithPaymentResult(session, msg, function (code) {
        logger.debug("send back msg 2: ", result, code);
        if(code === Code.OK) {
            self.payDao.updateOrderState(doc.orderId, commonEnum.RECHARGE_STATE.SEND_GOODS, doc.dqdOrderId, function (code) {
                let retMsg = "success";
                if(code !== Code.OK) {
                    retMsg = "fail";
                }
                res.write(retMsg);
                res.end();
                if(dqdOrderId) {
                    logger.debug("payCallbackFromDqd delete lock. ", dqdOrderId);
                    self.lockedPayMap.delete(dqdOrderId);
                }
            })
        }else {
            logger.error("dealWithPaymentResult rpc fail. orderId: ", doc.orderId);
            res.write("fail");
            res.end();
            if(dqdOrderId) {
                logger.debug("payCallbackFromDqd delete lock. ", dqdOrderId);
                self.lockedPayMap.delete(dqdOrderId);
            }
        }
    });
};

var sortAndLinkStr = function (obj){
    let arr = [];
    let num = 0;
    for (let i in obj) {
        if(i === "sign") continue;
        arr[num] = i;
        num++;
    }
    var sortArr = arr.sort();
    let linkStr = "";
    for(let i=0, lens=sortArr.length; i<lens; i++) {
        if(i !== 0) {
            linkStr += '&';
        }
        linkStr += sortArr[i] + '=' + obj[sortArr[i]];
    }
    return linkStr;
};

PayService.prototype.rechargeCreateOrder = function(msg, cb) {
    msg.orderId = utils.syncCreateUid();
    this.payDao.insertNewOrder(msg, function (code) {
        if(code === Code.OK) {
            cb(Code.OK, msg.orderId);
        }
        else {
            cb(Code.FAIL);
        }
    })
};

PayService.prototype.addRenameCardOrder = function (msg, cb) {
    this.payDao.findOrCreateAddRenameCard(msg, function (code, isInsert) {
        cb(code, isInsert);
    })
};

PayService.prototype.dealWithBestEleven = function(params, res, self) {
    let checkSign = utils.getCommonMd5Sign(params, clusterConfig.secret_key);
    logger.debug("dealWithBestEleven checkSign: ", checkSign, params.sign, params);
    if(params.sign === checkSign) {
        let session = {frontendId: self.app.getServerId()};
        let msg = {orderId: params.order_id, itemId: parseInt(params.item_id), openId: params.open_id};
        self.app.rpc.datanode.dataNodeRemote.addBestFootballOrder(session, msg, function (code) {
            logger.debug("dealWithBestEleven addBestFootballOrder ret code:", code);
            res.write(code.toString());
            res.end();
        });
    }else {
        res.write(Code.BEST_ELEVEN.SIGN_FAIL.toString());
        res.end();
    }
};

PayService.prototype.dealWithLotteryDraw = function(params, res, self) {
    let checkSign = utils.getCommonMd5Sign(params, clusterConfig.secret_key);
    logger.debug("dealWithLotteryDraw checkSign: ", checkSign, params.sign, params);
    if(params.sign === checkSign) {
        let session = {frontendId: self.app.getServerId()};
        let msg = {orderId: params.order_id, itemId: parseInt(params.item_id), openId: params.open_id};
        self.app.rpc.datanode.dataNodeRemote.addTurntableOrder(session, msg, function (code) {
            logger.debug("dealWithLotteryDraw addTurntableOrder ret code:", code);
            res.write(code.toString());
            res.end();
        });
    }else {
        res.write(Code.BEST_ELEVEN.SIGN_FAIL.toString());
        res.end();
    }
};

PayService.prototype.dealWithGetBasePlayerInfo = function(params, res, self) {
    //内部接口, 无需加密校验
    let openId = params.open_id;
    let account = {}, player = {};
    async.waterfall([
        function (callback) {
            if(self.rankRobotCache.has(openId)) {
                logger.debug("is robot in...", openId);
                return callback("isRobot");
            }
            callback(null);
        },
        function (callback) {
            logger.debug("getAccountByOpenId 111", openId);
            self.payDao.getAccountByOpenId(openId, function (code, doc) {
                logger.debug("getAccountByOpenId 222", code, doc);
                if(code !== Code.OK || !doc) {
                    return callback(Code.DQD_RANK_ACTIVITY.NOT_FOUND);
                }
                account = doc;
                callback(null);
            })
        },
        function (callback) {
            let session = {frontendId: self.app.getServerId(), toServerId: account.gid};
            let msg = {playerId: account.uid, collectionNames: [commonEnum.DB_NAME.player, commonEnum.DB_NAME.heros,
                    commonEnum.DB_NAME.teamFormation, commonEnum.DB_NAME.footballGround]};
            logger.debug("getLocalPlayerDoc 111", session, msg);
            self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, msg, function (code, playerDoc, isNewRegister) {
                logger.debug("getLocalPlayerDoc 222", code, playerDoc);
                player = new Player(playerDoc[commonEnum.DB_NAME.player].uid);
                player.initByDB(playerDoc[commonEnum.DB_NAME.player]);
                player.heros.initByDB(playerDoc[commonEnum.DB_NAME.heros]);
                player.teamFormations.initByDB(playerDoc[commonEnum.DB_NAME.teamFormation]);
                player.footballGround.initByDB(playerDoc[commonEnum.DB_NAME.footballGround]);
                player.checkFix();
                callback(null);
            })
        }
    ], function (err) {
        let result = {};
        if(err === "isRobot") {
            //机器人
            let robotObj = self.rankRobotCache.get(openId);
            if(robotObj.uid === "dqdRobotLastFive") {
                player = self.getLastFiveRankRobotData();
            }else {
                player = self.getRankActivityRobotData(robotObj.doc);
            }
        } else if(err) {
            //白板用户数据
            player = self.getRankActivityNoAccountData();
        }
        let curFormationId = player.teamFormations.currTeamFormationId;
        let teamFormation = player.teamFormations.getOneTeamFormation(curFormationId);
        if(!teamFormation) {
            result.code = Code.DQD_RANK_ACTIVITY.NO_FORMATION_DATA;
        }else {
            result.code = Code.OK;
            logger.debug("curFormationId, teamFormation.ResId", curFormationId, teamFormation);
            result.formation = dataApi.allData.data["TeamFormation"][teamFormation.ResId].Name;
            result.tatics = dataApi.allData.data["Tactic"][teamFormation.UseTactics].Name+','+dataApi.allData.data["Tactic"][teamFormation.UseDefTactics].Name;
            result.assets = player.teamFormations.calcTeamValue(curFormationId);
            if(player.beliefId !== 0) {
                result.faith = dataApi.allData.data["Belief"][player.beliefId].Team;
            }else {
                result.faith = "无";
            }
            result.strength = player.teamFormations.calcTotalRating(curFormationId);
        }
        logger.debug("dealWithGetBasePlayerInfo result: ", result);
        res.write(JSON.stringify(result));
        res.end();
    });
};

PayService.prototype.dealWithGetTeamFormation = function(params, res, self) {
    //内部接口, 无需加密校验
    let openId = params.open_id;
    let account = {}, player = {};
    async.waterfall([
        function (callback) {
            self.payDao.getAccountByOpenId(openId, function (code, doc) {
                if(code !== Code.OK || !doc) {
                    return callback(Code.DQD_RANK_ACTIVITY.NOT_FOUND);
                }
                account = doc;
                callback(null)
            })
        },
        function (callback) {
            let session = {frontendId: self.app.getServerId(), toServerId: account.gid};
            let msg = {playerId: account.uid, collectionNames: [commonEnum.DB_NAME.player, commonEnum.DB_NAME.heros,
                    commonEnum.DB_NAME.teamFormation, commonEnum.DB_NAME.footballGround]};
            self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, msg, function (code, playerDoc, isNewRegister) {
                player = new Player(playerDoc[commonEnum.DB_NAME.player].uid);
                player.initByDB(playerDoc[commonEnum.DB_NAME.player]);
                player.heros.initByDB(playerDoc[commonEnum.DB_NAME.heros]);
                player.teamFormations.initByDB(playerDoc[commonEnum.DB_NAME.teamFormation]);
                player.footballGround.initByDB(playerDoc[commonEnum.DB_NAME.footballGround]);
                player.checkFix();
                callback(null);
            })
        }
    ], function (err) {
        let result = {};
        if(err === "isRobot") {
            //机器人
            let robotObj = self.rankRobotCache.get(openId);
            if(robotObj.uid === "dqdRobotLastFive") {
                player = self.getLastFiveRankRobotData();
            }else {
                player = self.getRankActivityRobotData(robotObj.doc);
            }
        } else if(err) {
            //白板用户数据
            player = self.getRankActivityNoAccountData();
        }
        let curFormationId = player.teamFormations.currTeamFormationId;
        let teamFormation = player.teamFormations.getOneTeamFormation(curFormationId);
        if(!teamFormation) {
            result.code = Code.DQD_RANK_ACTIVITY.NO_FORMATION_DATA;
        }else {
            result.code = Code.OK;
            result.formation = dataApi.allData.data["TeamFormation"][teamFormation.ResId].Name;
            result.tatics = dataApi.allData.data["Tactic"][teamFormation.UseTactics].Name+','+dataApi.allData.data["Tactic"][teamFormation.UseDefTactics].Name;
            result.hero_position = [];
            for(let i in teamFormation.PositionToHerosObject) {
                for(let j in teamFormation.PositionToHerosObject[i]) {
                    let tmp = {position: i};
                    let heroUid = teamFormation.PositionToHerosObject[i][j];
                    let hero = player.heros.getHero(heroUid);
                    let modelId = dataApi.allData.data["Footballer"][hero.ResID].ModeID;
                    let color = dataApi.allData.data["Footballer"][hero.ResID].Color || "铜";
                    let iconUrl = dataApi.allData.data["FootballerIcon"][modelId].img_url;
                    let arr = iconUrl.split('/');
                    tmp.icon = arr[arr.length-1].split('.')[0]+'.png';
                    tmp.card_id = commonEnum.CLIENT_CARD_TYPE[color];
                    tmp.name = player.heros.getHeroName(heroUid);
                    tmp.power = player.heros.getRating(heroUid);
                    result.hero_position.push(tmp);
                }
            }
        }
        logger.debug("dealWithGetTeamFormation result: ", result);
        res.write(JSON.stringify(result));
        res.end();
    });
};

//白板用户数据
PayService.prototype.getRankActivityNoAccountData = function() {
    let config = dataApi.allData.data["BattleTest"]["3"];
    let playerId = "RankActivityNoAccountData03";
    let player = new Player(playerId);
    player.name = config["TeamName"];
    //阵型
    let formation = player.teamFormations.addTeamFormation(config["FormationId"]);
    player.teamFormations.currTeamFormationId = formation.Uid;
    //设置战术
    logger.debug("getRankActivityNoAccountData formation: ", formation);
    //创建球员
    for(let index = 0; index < config["Position"].length; index++) {
        let hero = player.heros.addHero(config["FootballerId"][index]);
        player.teamFormations.addHero(formation.Uid, config["Position"][index], hero.Uid);
    }
    return player;
};

//机器人数据
PayService.prototype.getRankActivityRobotData = function(robotDoc) {
    let playerId = robotDoc["player"].uid;
    let player = new Player(playerId);
    player.initByDB(robotDoc.player);
    player.heros.initByDB(robotDoc.heros);
    player.teamFormations.initByDB(robotDoc.teamFormation);
    player.footballGround.initByDB(robotDoc.footballGround);
    player.name = "";
    return player;
};

//机器人最后5名模板数据
PayService.prototype.getLastFiveRankRobotData = function() {
    let playerId = "dqdRobotLastFive";
    let player = new Player(playerId);
    let config = dataApi.allData.data["BattleTest"]["4"];
    let formationObj = player.teamFormations.addTeamFormation(config["FormationId"]);
    player.teamFormations.setCurrTeamFormationId(formationObj.Uid);
    //创建球员
    for(let index = 0; index < config["Position"].length; index++) {
        let hero = player.heros.addHero(config["FootballerId"][index]);
        player.teamFormations.addHero(formationObj.Uid, config["Position"][index], hero.Uid);
    }
    //属性赋值
    formationObj.UseTactics = config["AttackTactic"];
    formationObj.UseDefTactics = config["DefendTactic"];
    return player;
};

PayService.prototype.dealWithRankBattle = function(params, res, self) {
    //内部接口, 无需加密校验
    let result = {};
    let openIdA = params.team_a_oid;
    let openIdB = params.team_b_oid;
    let nameA = params.team_a_name;
    let nameB = params.team_b_name;
    let typeA = commonEnum.BATTLE_TEAM_TYPE.Player;
    let typeB = commonEnum.BATTLE_TEAM_TYPE.Player;
    logger.debug("params: ", params, openIdA, openIdB, nameA, nameB);
    let accountA = {}, accountB = {};
    async.waterfall([
        function (callback) {
            //优先判定是否机器人
            if(self.rankRobotCache.has(openIdA)) {
                accountA = self.rankRobotCache.get(openIdA);
                accountA.playerType = commonEnum.BATTLE_TEAM_TYPE.Robot;
                return callback(null);
            }else {
                self.payDao.getAccountByOpenId(openIdA, function (code, doc) {
                    if(code !== Code.OK) {
                        return callback(Code.DQD_RANK_ACTIVITY.NOT_FOUND);
                    }
                    if(!doc) {
                        accountA = {uid: "", gid: ""};
                        accountA.playerType = commonEnum.BATTLE_TEAM_TYPE.NoneAccount;
                    }else {
                        accountA = doc;
                        accountA.playerType = commonEnum.BATTLE_TEAM_TYPE.Player;
                    }
                    callback(null)
                })
            }
        },
        function (callback) {
            if(self.rankRobotCache.has(openIdB)) {
                accountB = self.rankRobotCache.get(openIdB);
                accountB.playerType = commonEnum.BATTLE_TEAM_TYPE.Robot;
                return callback(null);
            }else {
                self.payDao.getAccountByOpenId(openIdB, function (code, doc) {
                    if (code !== Code.OK) {
                        return callback(Code.DQD_RANK_ACTIVITY.NOT_FOUND);
                    }
                    if (!doc) {
                        accountB = {uid: "", gid: ""};
                        accountB.playerType = commonEnum.BATTLE_TEAM_TYPE.NoneAccount;
                    } else {
                        accountB = doc;
                        accountB.playerType = commonEnum.BATTLE_TEAM_TYPE.Player;
                    }
                    callback(null)
                })
            }
        },
        function (callback) {
            let battles = self.app.getServersByType("battle");
            let index = Calc.randRange(0, battles.length-1);
            logger.debug("servers: ", battles, index);
            let session = {frontendId: self.app.getServerId(), toServerId: battles[index].id};
            let msg = {home: accountA, away: accountB};
            self.app.rpc.battle.battleRemote.pvpDqdRankActivityBattleReq(session, msg, function (code, ret) {
                //result = retMsg;
                result.code = code;
                let abArray = ['a', 'b'], retMsg = ret.retMsg;
                logger.debug("retMsg length: ", retMsg, retMsg.length);
                if(code === Code.OK && retMsg.length === 2) {
                    for(let i=0; i<2; i++) {
                        let key = 'team_'+abArray[i]+'_result';
                        result[key] = {};
                        result[key].score = retMsg[i].score;
                        result[key].shot_num = retMsg[i].shotNum;
                        result[key].control = retMsg[i].control;
                        result[key].break = retMsg[i].break;
                        result[key].best_name = retMsg[i].bestId;
                        result[key].place_kick_num = retMsg[i].placeKickNum;
                    }
                }
                result.goal_record = ret.goalRecord;
                callback(null);
            })
        }
    ], function (err) {
        if(err) {
            result.code = err;
        }
        logger.debug("dealWithRankBattle result: ", result);
        res.write(JSON.stringify(result));
        res.end();
    });
};

PayService.prototype.dealWithRankRobotRefresh = function(params, res, self) {
    //内部接口, 无需加密校验
    let result = [];
    let debugKey = "";
    async.waterfall([
        function (callback) {
            //原有cache清空
            self.rankRobotCache.clear();
            //生成oid -> 固定机器人oid
            for(let oid in Constant.DQD_RANK_ROBOT_OID) {
                logger.debug("Constant.DQD_RANK_ROBOT_OID: ", oid);
                result.push(oid);
                debugKey = oid;
                self.rankRobotCache.set(oid, {});
            }
            /*
            for(let i=0; i<self.maxRankRobotNum;i++) {
                let oid = utils.syncCreateUid() + '-RankRobot#' + (i+1);
                result.push(oid);
                if(i === self.maxRankRobotNum-1) {
                    debugKey = oid;
                }
                self.rankRobotCache.set(oid, {});
            }
            */
            callback(null);
        },
        function (callback) {
            //实力排序
            self.payDao.sortAccountByStr(function (code, accountArr) {
                if(code !== Code.OK) {
                    return callback("sortAccountByStr err");
                }
                let topStr = accountArr[0].actualStrength;
                logger.debug("sortAccountByStr topStr: ", topStr, accountArr.length);
                let levelList = [20, 5, 5, 5, 5, 5];
                let index = 0;
                let level = 0;
                let tmpStr = topStr;
                //最后5个机器人另外生成一组实力参数
                for(let i=0,lens=accountArr.length;i<=lens;i++) {
                    if(accountArr[i].actualStrength <= tmpStr) {
                        if(index > levelList[level]) {
                            level++;
                            tmpStr = Math.floor(tmpStr * 0.8);
                            logger.debug("tmpStr, level: ", tmpStr, level);
                        }
                        let tmp = {};
                        tmp.oid = accountArr[i].oid;
                        tmp.uid = accountArr[i].uid;
                        tmp.gid = accountArr[i].gid;
                        tmp.str = accountArr[i].actualStrength;
                        self.rankRobotCache.set(result[index], tmp);
                        index++;
                        if(index >= self.maxRankRobotNum) {
                            break;
                        }
                    }
                }
                logger.debug("rankRobotCache: ", self.rankRobotCache);
                callback(null);
            });
        },
        function (callback) {
            //最后5个机器人实力参数生成 -> 改成模板生成
            /*
            self.payDao.getAccountByLevel(2, 5, function (code, accountArr) {
                if(code !== Code.OK) {
                    return callback("getAccountByLevel fail");
                }
                for(let i=0, len=accountArr.length; i<len; i++) {
                    let tmp = {};
                    tmp.oid = accountArr[i].oid;
                    tmp.uid = accountArr[i].uid;
                    tmp.gid = accountArr[i].gid;
                    tmp.str = accountArr[i].actualStrength;
                    self.rankRobotCache.set(result[self.maxRankRobotNum-5+i], tmp);
                }
                logger.debug("rankRobotCache: ", self.rankRobotCache);
                callback(null);
            });
            */
            for(let i=0; i<5; i++) {
                let tmp = {};
                tmp.oid = "dqdRobotLastFive";
                tmp.uid = "dqdRobotLastFive";
                tmp.gid = "";
                tmp.str = "";
                self.rankRobotCache.set(result[self.maxRankRobotNum-5+i], tmp);
            }
            logger.debug("rankRobotCache: ", self.rankRobotCache);
            callback(null);
        },
        function (callback) {
            //连接db
            self.payDao.connectAllGameDb(function (err) {
                callback(null);
            });
        },
        function (callback) {
            //45个用户数据存入cache
            async.eachSeries(result, function (rid, cb) {
                let obj = self.rankRobotCache.get(rid);
                if(obj.oid === "dqdRobotLastFive") {
                    return cb();
                }
                self.payDao.readPlayerTable(obj.uid, obj.gid, [commonEnum.DB_NAME.player, commonEnum.DB_NAME.teamFormation,
                commonEnum.DB_NAME.heros, commonEnum.DB_NAME.footballGround], function (code, doc) {
                    obj.doc = doc;
                    cb();
                });
            }, function (err) {
                callback(null);
            });
        },
        function (callback) {
            //关闭连接
            self.payDao.closeAllGameConnect(function () {
                callback(null);
            })
        },
        function (callback) {
            //将映射数据写入数据库
            let recordArr = self.rankRobotCacheToDB();
            logger.debug("rankRobotCacheToDB: ", recordArr);
            self.payDao.recordRankRobotCache(recordArr, function (code) {
                callback(null);
            })
        }
    ], function (err) {
        logger.debug("dealWithRankRobotRefresh, robot data: ", self.rankRobotCache.get(debugKey));
        logger.debug("dealWithRankRobotRefresh result: ", result);
        if(err) {
            logger.error("dealWithRankRobotRefresh Error: ", err);
        }
        res.write(JSON.stringify(result));
        res.end();
    });
};
//获取用户基本信息      params.name
PayService.prototype.getPlayerInfoByName = function (params, res, self) {
    // logger.error("!!!!!!!!!!!!!!!!!!!!!!!", params);
    //内部接口, 无需加密校验
    let name = decodeURIComponent(params.name);
    let result = {};
    result.code = Code.OK;
    result.playerId = "";
    result.openId = "";
    result.level = 0;        //等级
    result.cash = 0;          //欧元
    result.gold = 0;          //球币
    result.energy = 0;      //精力
    result.itemList = [];

    let account = {}, player = {};
    async.waterfall([
        function (callback) {
            self.payDao.getAccountByName(name, function (code, doc) {
                if(code !== Code.OK || !doc) {
                    logger.error("找不到用户：", code, params);
                    result.code = Code.DQD_RANK_ACTIVITY.NOT_FOUND;
                    return callback(Code.DQD_RANK_ACTIVITY.NOT_FOUND);
                }
                account = doc;
                callback(null)
            })
        },
        function (callback) {
            let session = {frontendId: self.app.getServerId(), toServerId: account.gid};
            let msg = {playerId: account.uid, collectionNames: [commonEnum.DB_NAME.player, commonEnum.DB_NAME.item]};
            self.app.rpc.game.entryRemote.getLocalPlayerDoc(session, msg, function (code, playerDoc, isNewRegister) {
                player = new Player(playerDoc[commonEnum.DB_NAME.player].uid);
                player.initByDB(playerDoc[commonEnum.DB_NAME.player]);
                player.item.initByDB(playerDoc[commonEnum.DB_NAME.item]);
                player.checkFix();
                callback(null);
            })
        }
    ], function (err) {
        if(result.code === Code.OK)
        {
            result.playerId = player.playerId;
            result.openId = player.openId;
            result.level = player.level;        //等级
            result.cash = player.cash;          //欧元
            result.gold = player.gold;          //球币
            result.energy = player.energy;      //精力
            let itemList = [];
            let itemMap = player.item.item;
            for(let [k, v] of itemMap)
            {
                itemList.push({ResId: v.ResID, Num: v.Num})
            }
            result.itemList = utils.cloneArray(itemList);
        }
        logger.error("PayService getPlayerInfoByName playerInfo:", result);
        res.write(JSON.stringify(result));
        res.end();
    });
};
//发送邮件      params.type(类型: 2.全服邮件, 3.多人邮件) params.playerName = [] params.attachList = [{ResId, Num, ItemType}, ....] params.title(邮件标题) params.content(邮件文本)
PayService.prototype.sendMail = function (params, res, self) {
    //解码
    params.type = Number(decodeURIComponent(params.type));
    params.playerName = JSON.parse(decodeURIComponent(params.playerName));
    params.title = decodeURIComponent(params.title);
    params.content = decodeURIComponent(params.content);
    params.attachList = JSON.parse(decodeURIComponent(params.attachList));

    let uidArray = [];
    let failedName = [];                //失败玩家名
    let playerList = params.playerName; //["玩家名", ...]
    let attachList = [];
    for(let i in params.attachList)
    {
        attachList.push({ResId: Number(params.attachList[i][0]), Num: Number(params.attachList[i][1]), ItemType: Number(params.attachList[i][2])});
    }
    let title = params.title;           //邮件标题
    let content = params.content;       //邮件内容
    let type = params.type;             //邮件类型

    let result = {};
    async.waterfall([
        function (callback) {
           if(type === commonEnum.HTTP_SEND_MAIL_TYPE.ALL_SERVER)//全服
            {
                if(!self.canSend)
                {
                    result.code = Code.SEND_ALL_MAIL;//全服邮件发送中
                    res.write(JSON.stringify(result));
                    res.end();
                    return;
                }
                self.canSend = false;//设置标志为不能发送
                self.timeCount = 0;
                self.payServerTimerStart();//开启定时器
                let games = serversConfig.development.game;//得到game服列表
                async.eachSeries(games, function (game, cb) {
                    let gid = game.id;
                    //得到这个服务器下所有玩家信息
                    let session = {frontendId: self.app.getServerId(), toServerId: gid};
                    self.app.rpc.game.entryRemote.getAllPlayerInfoDoc(session, gid, function (code, playerInfoList) {
                        if(code !== Code.OK)
                        {
                            failedName.push(gid);
                            logger.error("PayService sendMail getAllPlayerInfoDoc failed:", gid, playerInfoList);
                            return cb(null);
                        }
                        for(let i in playerInfoList)
                        {
                            uidArray.push(playerInfoList[i]);
                        }
                        cb(null);
                    });
                },function (err) {
                    callback(null);
                });
                result.code = Code.OK;
                res.write(JSON.stringify(result));
                res.end();
                return;
            }else if(type === commonEnum.HTTP_SEND_MAIL_TYPE.MULTI_PLAYER)//多人
            {
                async.eachSeries(playerList, function (playerName, cb) {
                    self.payDao.getAccountByName(playerName, function (code, doc) {
                        if(code !== Code.OK || !doc) {
                            logger.error("PayService sendMail getAccountByName failed playerName:", playerName);
                            failedName.push(playerName);//记录出错的玩家名
                            return cb(null);
                        }
                        uidArray.push({uid: doc.uid, gid: doc.gid});
                        cb(null)
                    })
                }, function (err) {
                    callback(null);
                });
            }
        },
        function (callback) {
            let mailInfo = {
              title: title,
              content: content,
              attachList: attachList
            };
            let session = {frontendId: self.app.getServerId()};
            let msg = {
                type: type,
                uidArray: uidArray,
                mailInfo: mailInfo
            };
            //记录发送邮件信息
            self.sendMailObj = msg;//utils.deepCopy(msg);
            //logger.error("记录发送邮件信息：", self.sendMailObj.uidArray.length, self.sendMailObj);
            self.app.rpc.datanode.dataNodeRemote.sendUrlSystemMailByUidList(session, msg, function (code) {
                result.code = code;
                callback(null);
            });
        }
    ], function (err) {
       if(type === commonEnum.HTTP_SEND_MAIL_TYPE.MULTI_PLAYER)
        {
            logger.error("PayService sendMail failedList:", failedName.length, failedName);
            logger.error("PayService sendMail playerList:", uidArray.length, uidArray);
            result.uidNum = uidArray.length;
            result.failedName = failedName;
            res.write(JSON.stringify(result));
            res.end();
        }
    });
};
//删除全服邮件记录中发送成功的玩家
PayService.prototype.DeleteSendSuccessfulPlayer = function(playerId, cb) {
    for(let i in this.sendMailObj.uidArray)
    {
        if(this.sendMailObj.uidArray[i].uid === playerId)
        {
            this.sendMailObj.uidArray.splice(i, 1);
            // logger.error("发送成功玩家，从记录中删除：", playerId, this.sendMailObj.uidArray.length, this.sendMailObj);
           break;
        }
    }
    if(!!this.sendMailObj.uidArray && this.sendMailObj.uidArray.length === 0)
    {
        // logger.error("全服邮件发送完返回", this.canSend);
        this.canSend = true;//设置可以发送
        this.timeCount = 0;
        this.sendMailObj = {};
        //发完邮件保存
        this.payDao.updateUnfinishedMail(this.sendMailObj, function () {
        });
    }
    return cb();
};

PayService.prototype.payServerTimerStart = function() {
    let self = this;
    if(self.intervalTime > 0) {
        setInterval(function () {
            logger.error("定时器开启 canSend: %d, timeCount: %d, MAXTimeOut: %d", self.canSend, self.timeCount, self.MAXTimeOut);
            if(self.timeCount >= self.MAXTimeOut)
            {
                self.canSend = true;
                self.timeCount = 0;
            }
           self.timeCount++;
        }, self.intervalTime);
    }
};
PayService.prototype.rankRobotCacheToDB = function () {
    let arr = [];
    for([k,v] of this.rankRobotCache) {
        let tmp = {};
        tmp.robotUid = k;
        tmp.oid = v.oid;
        tmp.uid = v.uid;
        tmp.gid = v.gid;
        tmp.str = v.str;
        arr.push(tmp);
    }
    return arr;
};

PayService.prototype.loadRankRobotCacheFromDB = function (recordDoc) {
    /*
    for(let i=0,len=recordDoc.length; i<len; i++) {
        let tmp = {};
        tmp.oid = recordDoc[i].oid;
        tmp.uid = recordDoc[i].uid;
        tmp.gid = recordDoc[i].gid;
        tmp.str = recordDoc[i].str;
        this.rankRobotCache.set(recordDoc[i].robotUid, tmp);
    }
    */
    let self = this;
    async.waterfall([
        function (callback) {
            self.payDao.connectAllGameDb(callback);
        },
        function (callback) {
            //加载DB数据
            async.eachSeries(recordDoc, function (info, cb) {
                let obj = {};
                obj.oid = info.oid;
                obj.uid = info.uid;
                obj.gid = info.gid;
                obj.str = info.str;
                if(obj.oid === "dqdRobotLastFive") {
                    self.rankRobotCache.set(info.robotUid, obj);
                    return cb();
                }
                self.payDao.readPlayerTable(obj.uid, obj.gid, [commonEnum.DB_NAME.player, commonEnum.DB_NAME.teamFormation,
                    commonEnum.DB_NAME.heros, commonEnum.DB_NAME.footballGround], function (code, doc) {
                    obj.doc = doc;
                    //logger.debug("obj.doc: ", doc)
                    self.rankRobotCache.set(info.robotUid, obj);
                    cb();
                });
            }, function (err) {
                logger.debug("load robot data finish.");
                callback()
            });
        },
        function (callback) {
            self.payDao.closeAllGameConnect(callback);
        }
    ], function (err) {
        logger.debug("loadRankRobotCacheFromDB finished. err:", err);
    });
};

//更新活动数据
PayService.prototype.doUpdateActiveData = function (params, res, self) {
    let tableName = decodeURIComponent(params.tableName).toString();
    let data = JSON.parse(decodeURIComponent(params.data));
    let updateTime = decodeURIComponent(params.updateTime).toString();
    logger.debug("doUpdateActiveData---------------", tableName, updateTime, data);
    let session = {frontendId: self.app.getServerId()};
    let msg = {tableName: tableName, data: data, updateTime: updateTime};
    self.app.rpc.datanode.dataNodeRemote.updateActiveConfig(session, msg, function (code) {
        let result = {
            code: code
        }
        res.write(JSON.stringify(result));
        res.end();
    })
}

//获取活动表内容
PayService.prototype.getActiveConfigData = function (params, res, self) {
    let tableName = decodeURIComponent(params.tableName).toString();
    logger.debug("getActiveConfigData-------------", tableName);
    let session = {frontendId: self.app.getServerId()};
    let msg = {tableName: tableName};
    self.app.rpc.datanode.dataNodeRemote.getConfigByName(session, msg, function (code, data) {
        let result = {
            code: code,
            data: data
        }
        logger.debug("getActiveConfigData-----------------", result);
        res.write(JSON.stringify(result));
        res.end();
    })
}

//更新活动数据
// PayService.prototype.doUpdateActiveData = function (params, cb) {
//     let tableName = params.tableName;
//     let data = params.data;
//     // logger.error("接受到的数据-------------------------", data);
//     let self = this;
//     let updateTime = params.updateTime;
//     let session = {frontendId: self.app.getServerId()};
//     let msg = {tableName: tableName, data: data, updateTime: updateTime};
//     self.app.rpc.datanode.dataNodeRemote.updateActiveConfig(session, msg, function (code) {
//         cb(code);
//     })
// }

//获取活动表内容
// PayService.prototype.getActiveConfigData = function (msg, cb) {
//     let session = {frontendId: this.app.getServerId()};
//     let sMsg = {tableName: "ActiveParam"}
//     this.app.rpc.datanode.dataNodeRemote.getConfigByName(session, sMsg, function (code, data) {
//         cb(code, data);
//     })
// }

//获取球币计划信息
PayService.prototype.testFunctions = function (msg, cb) {
    let oid = msg.oid;
    let type = msg.type;
    let self = this;
    let result = {
        code: Code.FAIL
    };
    async.waterfall([
        function (callback) {
            self.payDao.connectAllGameDb(function (err) {
                callback(null);
            });
        },
        function (callback) {
            self.payDao.getAccountByOpenId(oid, function (code, doc) {
                if (code !== Code.OK) {
                    callback(null);
                }else {
                    if(!!doc) {
                        let session = {frontendId: self.app.getServerId(), toServerId: doc.gid};
                        let sMsg = {playerId: doc.uid, type: type};
                        self.app.rpc.game.entryRemote.setGiftBuyTime(session, sMsg, function (code) {
                            result.code = Code.OK;
                            callback(null);
                        });
                    }else {
                        callback(null);
                    }
                }
            })
        },function (callback) {
            self.payDao.closeAllGameConnect(function (err) {
                callback(null);
            });
        }
    ], function (err) {
        cb()
    });
}

//获取球币计划信息
PayService.prototype.getPlayerGoldPlan = function (params, res, self) {
    let oid = decodeURIComponent(params.oid).toString();
    logger.debug("getPlayerGoldPlan-------------", oid);
    let result = {
        code: Code.FAIL,
        data: {}
    };
    async.waterfall([
        function (callback) {
            self.payDao.connectAllGameDb(function (err) {
                callback(null);
            });
        },
        function (callback) {
            self.payDao.getAccountByOpenId(oid, function (code, doc) {
                if (code !== Code.OK) {
                    callback(null);
                }else {
                    self.payDao.readPlayerTable(doc.uid, doc.gid, [commonEnum.DB_NAME.tasks], function (code, doc) {
                        if(!!doc) {
                            let info = {
                                uid:doc.tasks.uid,
                                firstGearTime: doc.tasks.firstGearTime,
                                secondGearTime:doc.tasks.secondGearTime,
                                everyDayGift: doc.tasks.everyDayGift
                            }
                            result.code = Code.OK;
                            result.data = info;
                            callback(null);
                        }else {
                            callback(null);
                        }
                    });
                }
            })
        },function (callback) {
            self.payDao.closeAllGameConnect(function (err) {
                callback(null);
            });
        }
    ], function (err) {
        res.write(JSON.stringify(result));
        res.end();
    });
}

//开球币计划  type:1 开6元球币计划   type:2  开30元球币计划  type:3  开6元and开30元
PayService.prototype.openPlayerGoldPlan = function (params, res, self) {
    let oid = decodeURIComponent(params.oid).toString();
    let type = Number(decodeURIComponent(params.type));
    logger.debug("openPlayerGoldPlan-------------", oid);
    let result = { code: Code.FAIL};
    if(type < 1 || type > 3) {
        logger.error("openPlayerGoldPlan----------", oid, type);
        result.code = Code.PARAM_FAIL;
        res.write(JSON.stringify(result));
        res.end();
        return;
    }

    async.waterfall([
        function (callback) {
            self.payDao.connectAllGameDb(function (err) {
                callback(null);
            });
        },
        function (callback) {
            self.payDao.getAccountByOpenId(oid, function (code, doc) {
                if (code !== Code.OK) {
                    callback(null);
                }else {
                    if(!!doc) {
                        let session = {frontendId: self.app.getServerId(), toServerId: doc.gid};
                        let sMsg = {playerId: doc.uid, type: type};
                        self.app.rpc.game.entryRemote.setGiftBuyTime(session, sMsg, function (code) {
                            result.code = Code.OK;
                            callback(null);
                        });
                    }else {
                        callback(null);
                    }
                }
            })
        },function (callback) {
            self.payDao.closeAllGameConnect(function (err) {
                callback(null);
            });
        }
    ], function (err) {
        res.write(JSON.stringify(result));
        res.end();
    });
}


PayService.prototype.initWhenAllServerStart = function () {
    let self = this;
    self.payDao.findOrCreateRankRobotCache(function (code, doc) {
        if(code === Code.OK && !!doc) {
            self.loadRankRobotCacheFromDB(doc.record);
        }
    })
};
