/**
 * Created by sea on 2019/7/30.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var timeUtils = require("../../util/timeUtils");
var dataApi = require('../../util/dataApi');
var commonEnum = require('../../../../shared/enum');

var MonthCard = function (player) {
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.isGetReward = 0; //当天是否已领取奖励   0未领取   1已领取
    this.allDay = 0; //总天数
    this.cardTime = 0; //剩余天数
    this.refreshTime = 0; //刷新时间
    this.buyMonthCardTime = 0; //购买月卡的那个时间点
    this.buyMonthNum = 0; //购买月卡次数
    this.monthCardInfo = [] //月卡信息
    this.isBuy = 0; //是否购买月卡   0否  1是
};

util.inherits(MonthCard, EventEmitter);

module.exports = MonthCard;

MonthCard.prototype.initByDB = function (doc) {
    this.uid = doc.uid || "";
    this.isGetReward = doc.isGetReward || 0;
    this.allDay = doc.allDay || 0;
    this.cardTime = doc.cardTime || 0;
    this.refreshTime = doc.refreshTime || 0;
    this.buyMonthCardTime = doc.buyMonthCardTime || 0;
    this.monthCardInfo = doc.monthCardInfo || [];
    this.buyMonthNum = doc.buyMonthNum || 0;
};

MonthCard.prototype.toJSONforClient = function () {};

MonthCard.prototype.toJSONforDB = function () {
    let cardInfo = {
        uid: this.uid,
        isGetReward: this.isGetReward,
        allDay: this.allDay,
        cardTime: this.cardTime,
        refreshTime: this.refreshTime,
        buyMonthCardTime: this.buyMonthCardTime,
        monthCardInfo: this.monthCardInfo,
        buyMonthNum: this.buyMonthNum
    }
    return cardInfo;
};

MonthCard.prototype.getConfig = function () {
    let cfg = {}
    let config = dataApi.allData.data["ActiveParam"];
    if (!config) {
        logger.error("buyMonthCard config is not found");
        return cfg;
    }

    //5 月卡
    for (let i in config) {
        if (5 === config[i].GroupId) {
            cfg.costMoney = config[i].Parameter1;
            cfg.rewardType1 = config[i].RewardType1;
            cfg.reward1 = config[i].Reward1;
            cfg.num1 = config[i].Number1;
            cfg.rewardtype2 = config[i].RewardType2;
            cfg.reward2 = config[i].Reward2;
            cfg.num2 = config[i].Number2;
        }
    }

    return cfg;
};

MonthCard.prototype.getMonthCardInfo = function () {
    let ret = {
        code: Code.FAIL,
        state: this.isGetReward,
        cardTime: this.cardTime,
        isBuy: this.isBuy
    }

    if (this.isBuy === 0) {
        ret.code = Code.OK;
        return ret;
    }

    let day = timeUtils.dayInterval(this.buyMonthCardTime);
    let config = this.getConfig();
    if (JSON.stringify(config) == "{}") {
        return Code.CONFIG_FAIL;
    }
    let index = 0;
    let specialAttachInfo = {
        roomUid: ""
    };
    for (let i = 0; i < day; ++i) {
        //昨日未领取就发邮件
        if (this.monthCardInfo[i].state === 0) {
            this.monthCardInfo[i].state = 2; //状态  0未领取   1已领取  2已发邮件
            let rewardList = [{
                    ItemType: config.rewardType1,
                    ResId: config.reward1,
                    Num: config.num1
                },
                {
                    ItemType: config.rewardtype2,
                    ResId: config.reward2,
                    Num: config.num2
                }
            ];

            //发邮件
            this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.MONTH_CARD, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");
            index++;
        }
    }

    this.cardTime -= index;

    //过期
    if (day >= this.allDay) {
        this.monthCardInfo = [];
        this.isBuy = 0;
        this.cardTime = 0;
        this.isGetReward = 0;
        ret.code = Code.OK;
        ret.isBuy = this.isBuy;
        ret.cardTime = this.cardTime;
        return ret;
    }

    //是否同一天
    if (!timeUtils.isToday(this.refreshTime)) {
        this.isGetReward = 0;
        this.refreshTime = Date.now();
    }

    ret.code = Code.OK;
    ret.state = this.isGetReward;
    ret.cardTime = this.cardTime;
    return ret;
};

MonthCard.prototype.getMonthCardReward = function () {
    let ret = {
        code: Code.FAIL,
        cardTime: this.cardTime
    };

    let day = timeUtils.dayInterval(this.buyMonthCardTime);
    //过期
    if (day >= this.allDay) {
        ret.code = Code.MONTH_CARD.TIMEOUT;
        return ret;
    }
    //已经领取了直接返回
    if (this.isGetReward === 1) {
        ret.code = Code.MONTH_CARD.GET;
        return ret;
    }

    let config = this.getConfig();
    if (JSON.stringify(config) == "{}") {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    //设置状态
    this.isGetReward = 1;
    //减天数
    this.cardTime--;
    //加钱
    this.player.gold += config.num2;
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.TWENTY_TWO, 0, 0, config.num2);
    //加物品
    let code = this.player.bag.addItem(config.reward1, config.num1);
    if (code !== Code.OK) {
        let rewardList = [{
            ItemType: config.rewardType1,
            ResId: config.reward1,
            Num: config.num1
        }];

        let specialAttachInfo = {
            roomUid: ""
        };
        this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.MONTH_CARD, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");
        ret.code = Code.BAG_FULL;
        ret.cardTime = this.cardTime;
        return ret;
    }
    ret.code = Code.OK;
    ret.cardTime = this.cardTime;
    return ret;
};

/**
 * 购买月卡
 * @param  num  金额
 */
MonthCard.prototype.buyMonthCard = function (num) {
    let config = dataApi.allData.data["ActiveParam"];
    if (!config) {
        logger.error("buyMonthCard config is not found");
        return Code.CONFIG_FAIL;
    }

    //5月卡
    let costMoney = 0;
    for (let i in config) {
        if (5 === config[i].GroupId) {
            costMoney = config[i].Parameter1;
        }
    }

    if (costMoney === 0) {
        return Code.CONFIG_FAIL;
    }

    //检查钱是否足够
    if (num < costMoney) {
        return Code.GOLD_FALL;
    }

    //只记录到期购买的时间
    if (this.cardTime === 0) {
        this.buyMonthCardTime = Date.now();
        this.allDay = 0;
    }

    this.cardTime += 30;
    this.allDay += 30;
    this.buyMonthNum++;
    if (this.isBuy == 0) {
        this.isBuy = 1;
    }

    for (let i = 0; i < 30; ++i) {
        let info = {};
        info.state = 0; //状态  0未领取   1已领取  2已发邮件
        this.monthCardInfo.push(info);
    }

    return Code.OK;
};