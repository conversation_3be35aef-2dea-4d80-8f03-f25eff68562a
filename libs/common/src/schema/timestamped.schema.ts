/**
 * Timestamped 基类（毫秒时间戳）
 * - 提供 createTime / updateTime / lastActiveTime（Number, ms）
 * - 自动维护 updateTime
 * - 提供常用查询与实例工具方法
 */

import { Prop } from '@nestjs/mongoose';
import { Schema as MongooseSchema } from 'mongoose';
import { createBaseSchema } from './base.schema';

export interface ITimestampedDoc {
  createTime: number;
  updateTime: number;
  lastActiveTime?: number;

  // 实例方法（由插件注入）
  getAgeDays(): number;
  getTimeSinceUpdate(): number;
  getTimeSinceActive(): number;
  touch(): Promise<void>;
}

@createBaseSchema({ timestamps: false })
export class TimestampedSchemaBase implements ITimestampedDoc {
  @Prop({ type: Number, default: () => Date.now(), index: true })
  createTime: number;

  @Prop({ type: Number, default: () => Date.now(), index: true })
  updateTime: number;

  @Prop({ type: Number, default: () => Date.now(), sparse: true })
  lastActiveTime?: number;

  // 仅作类型声明，实际实现由插件注入
  getAgeDays!: () => number;
  getTimeSinceUpdate!: () => number;
  getTimeSinceActive!: () => number;
  touch!: () => Promise<void>;
}

/**
 * 时间戳插件（与类解耦，可单独用于任意Schema）
 */
export function timestampNewPlugin(schema: MongooseSchema) {
  // 保存前统一更新 updateTime；新建时设置 createTime
  schema.pre('save', function (next) {
    const now = Date.now();
    if (this.isNew && !this.createTime) {
      this.createTime = now;
    }
    this.updateTime = now;
    next();
  });

  // 更新类操作时，自动设置 updateTime
  schema.pre(['updateOne', 'updateMany', 'findOneAndUpdate'], function () {
    const update = this.getUpdate() as any;
    if (!update.$set) update.$set = {};
    update.$set.updateTime = Date.now();
  });

  // 实例方法实现
  schema.methods.getAgeDays = function (): number {
    const created = this.createTime || Date.now();
    return Math.floor((Date.now() - created) / (1000 * 60 * 60 * 24));
  };

  schema.methods.getTimeSinceUpdate = function (): number {
    return Date.now() - (this.updateTime || Date.now());
  };

  schema.methods.getTimeSinceActive = function (): number {
    const last = this.lastActiveTime || this.updateTime || Date.now();
    return Date.now() - last;
  };

  schema.methods.touch = async function () {
    const now = Date.now();
    this.updateTime = now;
    this.lastActiveTime = now;
    return this.save();
  };
}

/** 常量与工具（毫秒制） */
export const TIME_MS = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
  MONTH: 30 * 24 * 60 * 60 * 1000,
  YEAR: 365 * 24 * 60 * 60 * 1000
};

export const TimestampUtils = {
  getDaysAgo(days: number): number {
    return Date.now() - days * TIME_MS.DAY;
  },
  getHoursAgo(hours: number): number {
    return Date.now() - hours * TIME_MS.HOUR;
  },
  isWithin(timestamp: number, windowMs: number): boolean {
    return Date.now() - timestamp <= windowMs;
  }
};
