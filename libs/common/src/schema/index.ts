/*
 * @Author: colorinstance <EMAIL>
 * @Date: 2025-09-08 20:17:58
 * @LastEditors: colorinstance <EMAIL>
 * @LastEditTime: 2025-09-08 20:33:11
 * @FilePath: \server-new\apps\character\src\common\schemas\base-new\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// base-new 统一导出

export {
  createBaseSchema,
  baseNewPlugin,
  applyBaseNewPlugin,
  BaseDoc,
  BaseSchemaOptions
} from './base.schema';

export {
  TimestampedSchemaBase,
  timestampNewPlugin,
  TIME_MS,
  TimestampUtils,
  ITimestampedDoc
} from './timestamped.schema';

export {
  SoftDeleteSchemaBase,
  softDeleteNewPlugin,
  ISoftDeleteDoc
} from './soft-delete.schema';

export {
  IndexPatterns,
  applyIndexes,
  IndexDef,
  IndexDirection
} from './indexed.schema';

export {
  VersionedSchemaBase,
  versionedNewPlugin,
  VersionInfo,
  IVersionedDoc,
  VersionedPluginOptions
} from './versioned.schema';
