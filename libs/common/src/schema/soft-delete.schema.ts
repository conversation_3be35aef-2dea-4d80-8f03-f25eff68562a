/**
 * SoftDelete 基类
 * - deletedAt: number | null（毫秒时间戳）
 * - isDeleted: boolean（可选标记，便于查询优化）
 * - 默认查询自动排除已删除（可通过 { includeDeleted: true } 显式包含）
 */

import { Prop } from '@nestjs/mongoose';
import { Schema as MongooseSchema } from 'mongoose';
import { createBaseSchema } from './base.schema';

export interface ISoftDeleteDoc {
  deletedAt?: number | null;
  isDeleted?: boolean;

  softDelete(deletedAt?: number): Promise<void>;
  restore(): Promise<void>;
  isSoftDeleted(): boolean;
}

@createBaseSchema({ timestamps: false })
export class SoftDeleteSchemaBase implements ISoftDeleteDoc {
  @Prop({ type: Number, default: null, index: true })
  deletedAt?: number | null;

  @Prop({ type: Boolean, default: false, index: true })
  isDeleted?: boolean;

  // 仅类型声明，实际由插件注入
  softDelete!: (deletedAt?: number) => Promise<void>;
  restore!: () => Promise<void>;
  isSoftDeleted!: () => boolean;
}

/**
 * 软删除插件
 * - 拦截查询：find/findOne/findOneAndUpdate/count 等默认排除已删除
 * - 提供实例/静态方法：softDelete/restore/findActive/findDeleted/findWithDeleted 等
 */
export function softDeleteNewPlugin(schema: MongooseSchema, options?: { deletedAtField?: string; useFlag?: boolean }) {
  const deletedAtField = options?.deletedAtField ?? 'deletedAt';
  const isDeletedField = 'isDeleted';

  // 查询前过滤（除非显式 includeDeleted）
  schema.pre(['find', 'findOne', 'findOneAndUpdate', 'count', 'countDocuments'], function () {
    const includeDeleted = (this.getOptions?.() as any)?.includeDeleted;
    const query = this.getQuery?.() || {};
    if (!includeDeleted && query[deletedAtField] === undefined) {
      this.where({ [deletedAtField]: { $in: [null, undefined] } });
    }
  });

  // 保存前保持 isDeleted 与 deletedAt 一致
  schema.pre('save', function (next) {
    const deletedVal = (this as any)[deletedAtField];
    if (options?.useFlag) {
      (this as any)[isDeletedField] = deletedVal != null;
    }
    next();
  });

  // 实例方法
  schema.methods.softDelete = async function (deletedAt?: number) {
    const when = deletedAt ?? Date.now();
    (this as any)[deletedAtField] = when;
    if (options?.useFlag) (this as any)[isDeletedField] = true;
    await this.save();
  };

  schema.methods.restore = async function () {
    (this as any)[deletedAtField] = null;
    if (options?.useFlag) (this as any)[isDeletedField] = false;
    await this.save();
  };

  schema.methods.isSoftDeleted = function (): boolean {
    return ((this as any)[deletedAtField] ?? null) != null;
  };

  // 静态方法
  schema.statics.findActive = function (filter: any = {}) {
    return this.find({ ...filter, [deletedAtField]: { $in: [null, undefined] } });
  };

  schema.statics.findDeleted = function (filter: any = {}) {
    return this.find({ ...filter, [deletedAtField]: { $ne: null } }, null, { includeDeleted: true });
  };

  schema.statics.findWithDeleted = function (filter: any = {}) {
    return this.find(filter, null, { includeDeleted: true });
  };

  schema.statics.softDeleteMany = function (filter: any = {}, deletedAt?: number) {
    const when = deletedAt ?? Date.now();
    const update: any = { [deletedAtField]: when };
    if (options?.useFlag) update[isDeletedField] = true;
    return this.updateMany(filter, { $set: update });
  };

  schema.statics.restoreMany = function (filter: any = {}) {
    const update: any = { [deletedAtField]: null };
    if (options?.useFlag) update[isDeletedField] = false;
    return this.updateMany({ ...filter, [deletedAtField]: { $ne: null } }, { $set: update });
  };
}
