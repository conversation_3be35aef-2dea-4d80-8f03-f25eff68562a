# base-new Schema 基类套件（实用版）

本目录提供一套更贴近实际业务、简洁可落地的 Mongoose/NestJS Schema 基类与插件工具，目标：

- 统一 Schema 默认配置（严格、友好的 JSON 输出）。
- 推荐使用“毫秒数 Number”时间戳，方便与业务层一致的时间处理。
- 软删除、版本控制、索引等能力以“插件 + 轻量工具”形式提供，按需组合。
- 中文注释、命名清晰、可读性强，降低接入成本。

目录结构：

- `base.schema.ts`：基础装饰器与通用插件。
- `timestamped.schema.ts`：毫秒时间戳基类 + 时间戳插件。
- `soft-delete.schema.ts`：软删除基类 + 软删除插件。
- `indexed.schema.ts`：常用索引模式与应用工具。
- `versioned.schema.ts`：轻量版本控制基类 + 版本控制插件。
- `index.ts`：统一导出。

---

## 设计要点

- 推荐以“插件”为主进行组合，类继承为辅：
  - 多功能组合场景（时间戳 + 软删除 + 版本控制）建议：
    - 模型类使用 `createBaseSchema()` 装饰器。
    - 在 `SchemaFactory.createForClass()` 创建完 Schema 后，调用：
      - `timestampNewPlugin(schema)`
      - `softDeleteNewPlugin(schema, { useFlag: true })`
      - `versionedNewPlugin(schema)`
      - （可选）`lifecycleNewPlugin(schema)`
      - `applyIndexes(schema, [...])`
    - 如需字段在类中声明，直接在类上添加 `@Prop()` 字段声明。
  - 若只需单一功能，可继承对应 `*SchemaBase`（其已声明必要字段），并按需叠加插件。

- 时间戳字段均为毫秒数（Number）：`createTime`、`updateTime`、`lastActiveTime`。
- 软删除采用：`deletedAt: number | null` 与可选 `isDeleted: boolean` 标记。
- 版本控制轻量化：`version: number`、`lastModifiedBy?: string`、可选 `versionHistory[]`，默认自动递增。

---

## 快速上手

以“角色背包物品（示例）”为例，组合 时间戳 + 软删除 + 版本 控制 + 常用索引：

```ts
import { Prop, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import {
  createBaseSchema,
  timestampNewPlugin,
  softDeleteNewPlugin,
  versionedNewPlugin,
  IndexPatterns,
  applyIndexes,
  lifecycleNewPlugin,
  TIME_MS
} from '@/apps/character/src/common/schemas/base-new';

@createBaseSchema()
export class InventoryItem {
  @Prop({ type: String, required: true, index: true })
  characterId: string;

  @Prop({ type: String, required: true })
  itemId: string;

  @Prop({ type: Number, default: 0 })
  count: number;

  // 若希望类中声明时间戳，可在此加上 @Prop 字段；
  // 也可仅通过 timestampNewPlugin 注入行为（仍建议声明字段，便于类型提示）。
  @Prop({ type: Number, default: () => Date.now(), index: true })
  createTime: number;

  @Prop({ type: Number, default: () => Date.now(), index: true })
  updateTime: number;

  @Prop({ type: Number, default: () => Date.now(), sparse: true })
  lastActiveTime?: number;

  // 软删除字段
  @Prop({ type: Number, default: null, index: true })
  deletedAt?: number | null;

  @Prop({ type: Boolean, default: false, index: true })
  isDeleted?: boolean;

  // 版本控制字段
  @Prop({ type: Number, default: 1, index: true })
  version: number;

  @Prop({ type: String })
  lastModifiedBy?: string;
}

export type InventoryItemDocument = HydratedDocument<InventoryItem>;
export const InventoryItemSchema = SchemaFactory.createForClass(InventoryItem);

// 应用插件（顺序一般无强制要求）
timestampNewPlugin(InventoryItemSchema);
softDeleteNewPlugin(InventoryItemSchema, { useFlag: true });
versionedNewPlugin(InventoryItemSchema, { autoIncrement: true, trackHistory: true });
// 可选：如确需模型层生命周期钩子
lifecycleNewPlugin(InventoryItemSchema);

// 应用常用索引
applyIndexes(InventoryItemSchema, [
  IndexPatterns.compound({ characterId: 1, itemId: 1 }),
  IndexPatterns.descending('updateTime'),
]);
```

---

## 各模块说明

### base.schema.ts

- `createBaseSchema(options?)`：统一默认 Schema 选项（严格模式、JSON输出含`id`）。
- `baseNewPlugin(schema)`：增加通用实例方法 `toSafeObject()/equals()` 与静态方法 `findByIdSafe()/existsById()`。
- `applyBaseNewPlugin(schema)`：便捷应用插件。

### timestamped.schema.ts

- `TimestampedSchemaBase`：包含 `createTime/updateTime/lastActiveTime` 三个字段。
- `timestampNewPlugin(schema)`：保存与更新自动维护 `updateTime`；提供实例方法：
  - `getAgeDays()`、`getTimeSinceUpdate()`、`getTimeSinceActive()`、`touch()`。
- `TIME_MS`、`TimestampUtils`：毫秒常量与时间工具。

### soft-delete.schema.ts

- `SoftDeleteSchemaBase`：包含 `deletedAt/isDeleted` 字段。
- `softDeleteNewPlugin(schema, { useFlag?: boolean })`：
  - 默认查询排除已删除；`includeDeleted: true` 可包含。
  - 实例：`softDelete()/restore()/isSoftDeleted()`。
  - 静态：`findActive()/findDeleted()/findWithDeleted()/softDeleteMany()/restoreMany()`。

### indexed.schema.ts

- `IndexPatterns`：ascending/descending/unique/compound/text/ttl。
- `applyIndexes(schema, defs)`：批量将索引定义应用到 Schema。

### versioned.schema.ts

- `VersionedSchemaBase`：包含 `version/lastModifiedBy/versionHistory` 字段。
- `versionedNewPlugin(schema, options)`：
  - `autoIncrement` 默认启用；`trackHistory` 默认启用；支持历史裁剪。
  - 实例：`getVersion()/getVersionHistory()/createVersion()`。


## 业务建议（结合 Character/Hero/Match 架构）

- 用户/角色聚焦数据（如阵容、背包、成长）建议启用：时间戳 + 软删除；
- 需要追踪敏感变更（如属性计算配置、养成配置）再叠加版本控制；
- 高频查询字段（`characterId`、`heroId`、`status`、`createTime/updateTime`）优先建立索引；
- 清理策略：对软删除数据定期做归档或物理清理，避免膨胀；
- 注意将时间统一为毫秒，方便前后端一致处理与排序。

---

## 常见问题

- 组合多种能力时如何继承？
  - 推荐以“插件组合”为主；类中仅声明需要的字段，避免多重继承问题。
- 旧有 `base/` 套件还能用吗？
  - 可以。`base-new/` 是更轻量的替代方案，逐步迁移即可。

---

如需增加额外能力（如审计日志、变更原因、操作来源），可在插件基础上扩展，不建议在基类做过度封装，以保持简单与可读性。

---

## 项目共用接入（建议）

- 为了在多个应用中统一引用该基类库，建议在根 `tsconfig.json`（或 `tsconfig.base.json`）里添加路径别名：

```json
{
  "compilerOptions": {
    "paths": {
      "@base-schemas/*": [
        "apps/character/src/common/schemas/base-new/*"
      ]
    }
  }
}
```

- 之后各应用即可使用：

```ts
import { createBaseSchema, timestampNewPlugin } from '@base-schemas';
```

- 若后续迁移到 `libs/common/`，仅需更新路径映射即可，业务代码零改动。
